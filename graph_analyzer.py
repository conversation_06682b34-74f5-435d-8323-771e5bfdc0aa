#!/usr/bin/env python3
"""
Complete Web Exploration Graph Analysis and Visualization Tool
Analyzes run directories and saves rich visualizations to ./visualization folder
"""

import os
import json
import glob
from collections import defaultdict, Counter
from urllib.parse import urlparse
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import networkx as nx
import numpy as np
from typing import Dict, List, Tuple, Set


class WebExplorationAnalyzer:
    def __init__(self, run_dir: str):
        self.run_dir = run_dir
        self.viz_dir = "./visualization"
        self.nodes = []
        self.graph_info = {}
        self.url_stats = {}
        self.G = None
        
        # Create visualization directory
        os.makedirs(self.viz_dir, exist_ok=True)
        
    def load_data(self):
        """Load all data from run directory"""
        print(f"Loading data from: {self.run_dir}")
        
        # Load graph info
        graph_info_path = os.path.join(self.run_dir, "graph", "graph_info.json")
        with open(graph_info_path, 'r') as f:
            self.graph_info = json.load(f)
        
        # Load all nodes
        graph_dir = os.path.join(self.run_dir, "graph")
        node_dirs = glob.glob(os.path.join(graph_dir, "node_*"))
        node_dirs.sort(key=lambda x: int(x.split("_")[-1]))
        
        for node_dir in node_dirs:
            node_info_path = os.path.join(node_dir, "node_info.json")
            if os.path.exists(node_info_path):
                with open(node_info_path, 'r') as f:
                    node_info = json.load(f)
                
                # Count visits (prefixes)
                prefix_dir = os.path.join(node_dir, "prefixes")
                visit_count = 0
                if os.path.exists(prefix_dir):
                    visit_count = len([d for d in os.listdir(prefix_dir) 
                                     if d.startswith("prefix_") and 
                                     os.path.isdir(os.path.join(prefix_dir, d))])
                
                node_info['visit_count'] = visit_count
                node_info['node_id'] = int(os.path.basename(node_dir).split("_")[-1])
                self.nodes.append(node_info)
        
        print(f"Loaded {len(self.nodes)} nodes")
        
    def analyze_url_patterns(self):
        """Analyze URL patterns and relationships"""
        self.url_stats = {}

        for node in self.nodes:
            url = node['url']
            parsed = urlparse(url)

            self.url_stats[url] = {
                'visit_count': node['visit_count'],
                'children_count': len(node.get('children', [])),
                'is_visited': node.get('visited', False),
                'node_id': node['node_id'],
                'domain': parsed.netloc,
                'path': parsed.path,
                'is_localhost': 'localhost' in parsed.netloc,
                'url_type': self._categorize_url(url)
            }

        print(f"Analyzed {len(self.url_stats)} URLs")
        visited_count = sum(1 for stats in self.url_stats.values() if stats['visit_count'] > 0)
        print(f"URLs with visits: {visited_count}")
        print(f"URLs without visits: {len(self.url_stats) - visited_count}")
    
    def _categorize_url(self, url: str) -> str:
        """Categorize URL by type"""
        parsed = urlparse(url)
        
        if 'localhost' not in parsed.netloc:
            return 'external'
        
        path = parsed.path.lower()
        if '/f/' in path:
            return 'forum'
        elif '/wiki' in path:
            return 'wiki'
        elif '/login' in path:
            return 'login'
        elif '/search' in path:
            return 'search'
        elif path in ['/', '/new', '/hot', '/top', '/comments', '/active']:
            return 'main'
        else:
            return 'other_localhost'
    
    def build_graph(self):
        """Build NetworkX graph with rich edge attributes"""
        self.G = nx.DiGraph()
        
        # Add nodes with attributes
        for node in self.nodes:
            url = node['url']
            stats = self.url_stats[url]
            self.G.add_node(url, **stats)
        
        # Add edges with relationship analysis
        for node in self.nodes:
            parent_url = node['url']
            parent_stats = self.url_stats[parent_url]
            
            for child_url in node.get('children', []):
                if child_url in self.url_stats:  # Check if child exists in our data
                    child_stats = self.url_stats[child_url]

                    # Analyze edge relationship
                    edge_type = self._analyze_edge_relationship(parent_stats, child_stats)

                    self.G.add_edge(parent_url, child_url,
                                   edge_type=edge_type,
                                   parent_visits=parent_stats['visit_count'],
                                   child_visits=child_stats['visit_count'])
    
    def _analyze_edge_relationship(self, parent_stats: Dict, child_stats: Dict) -> str:
        """Analyze the relationship between parent and child URLs"""
        parent_visited = parent_stats['is_visited']  # Use visited status, not visit count
        child_visited = child_stats['is_visited']

        if parent_visited and child_visited:
            return 'visited_to_visited'
        elif parent_visited and not child_visited:
            return 'visited_to_unvisited'
        elif not parent_visited and child_visited:
            return 'unvisited_to_visited'
        else:
            return 'unvisited_to_unvisited'
    
    def create_visit_analysis(self):
        """Create comprehensive visit analysis visualizations"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        
        # 1. Top visited URLs bar chart
        visited_urls = [(url, stats['visit_count']) 
                       for url, stats in self.url_stats.items() 
                       if stats['visit_count'] > 0]
        top_visited = sorted(visited_urls, key=lambda x: x[1], reverse=True)[:15]
        
        if top_visited:
            urls, counts = zip(*top_visited)
            display_urls = [self._shorten_url(url) for url in urls]
            
            bars = ax1.bar(range(len(display_urls)), counts, color='skyblue', alpha=0.8)
            ax1.set_xlabel('URLs')
            ax1.set_ylabel('Visit Count')
            ax1.set_title('Top 15 Most Visited URLs')
            ax1.set_xticks(range(len(display_urls)))
            ax1.set_xticklabels(display_urls, rotation=45, ha='right')
            
            # Add value labels
            for bar, count in zip(bars, counts):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        str(count), ha='center', va='bottom')
        
        # 2. URL type distribution
        url_types = defaultdict(int)
        for stats in self.url_stats.values():
            url_types[stats['url_type']] += 1
        
        ax2.pie(url_types.values(), labels=url_types.keys(), autopct='%1.1f%%')
        ax2.set_title('URL Type Distribution')
        
        # 3. Visit distribution histogram
        visit_counts = [stats['visit_count'] for stats in self.url_stats.values()]
        ax3.hist(visit_counts, bins=50, alpha=0.7, color='lightgreen')
        ax3.set_xlabel('Visit Count')
        ax3.set_ylabel('Number of URLs')
        ax3.set_title('Visit Count Distribution')
        ax3.set_yscale('log')
        
        # 4. Visited vs Unvisited comparison (based on visited status, not visit count)
        visited_count = sum(1 for stats in self.url_stats.values() if stats['is_visited'])
        unvisited_count = len(self.url_stats) - visited_count

        ax4.bar(['Visited', 'Unvisited'], [visited_count, unvisited_count],
               color=['green', 'red'], alpha=0.7)
        ax4.set_ylabel('Number of URLs')
        ax4.set_title('Visited vs Unvisited URLs (by visited status)')

        # Add percentage labels
        total = visited_count + unvisited_count
        if visited_count > 0:
            ax4.text(0, visited_count + total*0.01, f'{visited_count/total*100:.1f}%',
                    ha='center', va='bottom')
        if unvisited_count > 0:
            ax4.text(1, unvisited_count + total*0.01, f'{unvisited_count/total*100:.1f}%',
                    ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, 'visit_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _shorten_url(self, url: str, max_len: int = 40) -> str:
        """Shorten URL for display"""
        if len(url) <= max_len:
            return url

        parsed = urlparse(url)
        if parsed.path and parsed.path != '/':
            path_parts = parsed.path.split('/')
            if len(path_parts) > 1:
                return f".../{path_parts[-1]}"

        return url[:max_len-3] + "..."

    def create_network_visualizations(self):
        """Create network graph visualizations with edge relationship analysis"""
        # Filter to localhost URLs for cleaner visualization
        localhost_nodes = [node for node in self.G.nodes()
                          if 'localhost' in node and not node.startswith('about:')]
        G_localhost = self.G.subgraph(localhost_nodes).copy()

        # Create main network visualization
        self._create_main_network_graph(G_localhost)

        # Create edge relationship analysis
        self._create_edge_relationship_analysis(G_localhost)

        # Create visit pattern network
        self._create_visit_pattern_network(G_localhost)

    def _create_main_network_graph(self, G):
        """Create main network graph with visit-based styling"""
        plt.figure(figsize=(24, 18))

        # Calculate layout
        pos = nx.spring_layout(G, k=3, iterations=100, seed=42)

        # Node styling based on visit count
        visit_counts = [G.nodes[node]['visit_count'] for node in G.nodes()]
        max_visits = max(visit_counts) if visit_counts else 1

        node_sizes = []
        node_colors = []
        for node in G.nodes():
            visits = G.nodes[node]['visit_count']

            # Size based on visits
            if visits == 0:
                size = 100
            else:
                size = 200 + (visits / max_visits) * 2000
            node_sizes.append(size)

            # Color based on visit intensity
            if visits == 0:
                node_colors.append('#CCCCCC')  # Gray for unvisited
            elif visits <= 5:
                node_colors.append('#87CEEB')  # Light blue
            elif visits <= 20:
                node_colors.append('#FFA500')  # Orange
            elif visits <= 100:
                node_colors.append('#FF6347')  # Red
            else:
                node_colors.append('#8B0000')  # Dark red

        # Edge styling based on relationship type
        edge_colors = []
        edge_widths = []
        for edge in G.edges():
            edge_data = G.edges[edge]
            edge_type = edge_data.get('edge_type', 'unknown')

            if edge_type == 'visited_to_visited':
                edge_colors.append('#2E8B57')  # Sea green
                edge_widths.append(2.0)
            elif edge_type == 'visited_to_unvisited':
                edge_colors.append('#FF4500')  # Orange red
                edge_widths.append(1.5)
            elif edge_type == 'unvisited_to_visited':
                edge_colors.append('#4169E1')  # Royal blue
                edge_widths.append(1.5)
            else:  # unvisited_to_unvisited
                edge_colors.append('#D3D3D3')  # Light gray
                edge_widths.append(0.5)

        # Draw the graph
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, node_color=node_colors,
                              alpha=0.8, linewidths=1, edgecolors='black')
        nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=edge_widths,
                              alpha=0.7, arrows=True, arrowsize=20, arrowstyle='->')

        # Add labels for high-visit nodes
        high_visit_nodes = {node: G.nodes[node]['visit_count']
                           for node in G.nodes()
                           if G.nodes[node]['visit_count'] > 20}

        if high_visit_nodes:
            labels = {}
            for node, visits in high_visit_nodes.items():
                short_label = self._shorten_url(node, 20)
                labels[node] = f"{short_label}\n({visits})"

            nx.draw_networkx_labels(G, pos, labels, font_size=8,
                                   bbox=dict(boxstyle="round,pad=0.3",
                                           facecolor="white", alpha=0.8))

        # Create legend
        legend_elements = [
            mpatches.Patch(color='#CCCCCC', label='Unvisited (0)'),
            mpatches.Patch(color='#87CEEB', label='Low visits (1-5)'),
            mpatches.Patch(color='#FFA500', label='Medium visits (6-20)'),
            mpatches.Patch(color='#FF6347', label='High visits (21-100)'),
            mpatches.Patch(color='#8B0000', label='Very high visits (100+)'),
        ]

        plt.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 1))
        plt.title(f'Web Exploration Network Graph\n{len(G.nodes())} nodes, {len(G.edges())} edges',
                 fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, 'main_network_graph.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _create_edge_relationship_analysis(self, G):
        """Analyze and visualize edge relationships"""
        # Count edge types
        edge_types = defaultdict(int)
        for edge in G.edges():
            edge_type = G.edges[edge].get('edge_type', 'unknown')
            edge_types[edge_type] += 1

        # Create edge analysis visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # Edge type distribution pie chart
        if edge_types:
            labels = [t.replace('_', ' ').title() for t in edge_types.keys()]
            colors = ['#2E8B57', '#FF4500', '#4169E1', '#D3D3D3'][:len(edge_types)]

            ax1.pie(edge_types.values(), labels=labels, autopct='%1.1f%%', colors=colors)
            ax1.set_title('Edge Relationship Types')

        # Edge type bar chart with details
        ax2.bar(range(len(edge_types)), list(edge_types.values()),
               color=colors[:len(edge_types)], alpha=0.8)
        ax2.set_xlabel('Edge Type')
        ax2.set_ylabel('Count')
        ax2.set_title('Edge Relationship Distribution')
        ax2.set_xticks(range(len(edge_types)))
        ax2.set_xticklabels([t.replace('_', '\n').title() for t in edge_types.keys()],
                           rotation=45, ha='right')

        # Add value labels
        for i, count in enumerate(edge_types.values()):
            ax2.text(i, count + 0.5, str(count), ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, 'edge_relationship_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _create_visit_pattern_network(self, G):
        """Create specialized network showing visit patterns"""
        plt.figure(figsize=(20, 16))

        # Separate visited and unvisited nodes based on visited status
        visited_nodes = [node for node in G.nodes() if G.nodes[node]['is_visited']]
        unvisited_nodes = [node for node in G.nodes() if not G.nodes[node]['is_visited']]

        # Create layout
        pos = nx.spring_layout(G, k=2, iterations=100, seed=42)

        # Draw unvisited nodes (smaller, gray)
        if unvisited_nodes:
            nx.draw_networkx_nodes(G, pos, nodelist=unvisited_nodes,
                                  node_size=150, node_color='lightgray',
                                  alpha=0.5, label='Unvisited')

        # Draw visited nodes with size based on visit count
        if visited_nodes:
            visit_counts = [G.nodes[node]['visit_count'] for node in visited_nodes]
            max_visits = max(visit_counts)
            node_sizes = [300 + (G.nodes[node]['visit_count'] / max_visits) * 2000
                         for node in visited_nodes]

            # Color gradient based on visits
            node_colors = [G.nodes[node]['visit_count'] for node in visited_nodes]

            nodes = nx.draw_networkx_nodes(G, pos, nodelist=visited_nodes,
                                         node_size=node_sizes, node_color=node_colors,
                                         cmap='Reds', alpha=0.8, linewidths=1,
                                         edgecolors='black')

            # Add colorbar
            plt.colorbar(nodes, label='Visit Count', shrink=0.8)

        # Draw edges with different styles based on visited status
        visited_to_visited_edges = [(u, v) for u, v in G.edges()
                                   if G.nodes[u]['is_visited'] and G.nodes[v]['is_visited']]
        visited_to_unvisited_edges = [(u, v) for u, v in G.edges()
                                     if G.nodes[u]['is_visited'] and not G.nodes[v]['is_visited']]
        unvisited_to_visited_edges = [(u, v) for u, v in G.edges()
                                     if not G.nodes[u]['is_visited'] and G.nodes[v]['is_visited']]
        unvisited_to_unvisited_edges = [(u, v) for u, v in G.edges()
                                       if not G.nodes[u]['is_visited'] and not G.nodes[v]['is_visited']]

        # Draw different edge types
        if visited_to_visited_edges:
            nx.draw_networkx_edges(G, pos, edgelist=visited_to_visited_edges,
                                  edge_color='green', width=2, alpha=0.8, arrows=True)
        if visited_to_unvisited_edges:
            nx.draw_networkx_edges(G, pos, edgelist=visited_to_unvisited_edges,
                                  edge_color='orange', width=1.5, alpha=0.7, arrows=True)
        if unvisited_to_visited_edges:
            nx.draw_networkx_edges(G, pos, edgelist=unvisited_to_visited_edges,
                                  edge_color='blue', width=1.5, alpha=0.7, arrows=True)
        if unvisited_to_unvisited_edges:
            nx.draw_networkx_edges(G, pos, edgelist=unvisited_to_unvisited_edges,
                                  edge_color='lightgray', width=0.5, alpha=0.3, arrows=True)

        # Add labels for top visited nodes
        top_visited = sorted(visited_nodes,
                           key=lambda x: G.nodes[x]['visit_count'], reverse=True)[:10]
        if top_visited:
            labels = {node: f"{self._shorten_url(node, 15)}\n({G.nodes[node]['visit_count']})"
                     for node in top_visited}
            nx.draw_networkx_labels(G, pos, labels, font_size=8,
                                   bbox=dict(boxstyle="round,pad=0.2",
                                           facecolor="white", alpha=0.9))

        # Create edge legend
        edge_legend = [
            plt.Line2D([0], [0], color='green', linewidth=2, label='Visited → Visited'),
            plt.Line2D([0], [0], color='orange', linewidth=1.5, label='Visited → Unvisited'),
            plt.Line2D([0], [0], color='blue', linewidth=1.5, label='Unvisited → Visited'),
            plt.Line2D([0], [0], color='lightgray', linewidth=0.5, label='Unvisited → Unvisited'),
        ]

        plt.legend(handles=edge_legend, loc='upper right', bbox_to_anchor=(1, 1))
        plt.title('Visit Pattern Network\n(Node size = visit count, Edge color = relationship type)',
                 fontsize=14, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, 'visit_pattern_network.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def create_detailed_statistics(self):
        """Create detailed statistical analysis and save to file"""
        stats = {
            'basic_stats': {},
            'visit_analysis': {},
            'url_type_analysis': {},
            'edge_analysis': {},
            'top_urls': {}
        }

        # Basic statistics
        total_nodes = len(self.nodes)
        visited_nodes = sum(1 for n in self.nodes if n.get('visited', False))
        total_visits = sum(n['visit_count'] for n in self.nodes)

        stats['basic_stats'] = {
            'total_nodes': total_nodes,
            'visited_nodes': visited_nodes,
            'unvisited_nodes': total_nodes - visited_nodes,
            'total_visits': total_visits,
            'avg_visits_per_url': total_visits / total_nodes if total_nodes > 0 else 0,
            'exploration_efficiency': visited_nodes / total_nodes * 100 if total_nodes > 0 else 0
        }

        # Visit analysis
        visit_counts = [n['visit_count'] for n in self.nodes]
        stats['visit_analysis'] = {
            'max_visits': max(visit_counts) if visit_counts else 0,
            'min_visits': min(visit_counts) if visit_counts else 0,
            'median_visits': np.median(visit_counts) if visit_counts else 0,
            'std_visits': np.std(visit_counts) if visit_counts else 0
        }

        # URL type analysis
        url_types = defaultdict(int)
        for node in self.nodes:
            url_type = self._categorize_url(node['url'])
            url_types[url_type] += 1
        stats['url_type_analysis'] = dict(url_types)

        # Edge analysis
        if self.G:
            edge_types = defaultdict(int)
            for edge in self.G.edges():
                edge_type = self.G.edges[edge].get('edge_type', 'unknown')
                edge_types[edge_type] += 1
            stats['edge_analysis'] = dict(edge_types)

        # Top URLs
        top_visited = sorted(self.nodes, key=lambda x: x['visit_count'], reverse=True)[:20]
        stats['top_urls'] = {
            'most_visited': [(n['url'], n['visit_count']) for n in top_visited],
            'most_connected': sorted([(n['url'], len(n.get('children', []))) for n in self.nodes],
                                   key=lambda x: x[1], reverse=True)[:10]
        }

        # Save statistics to JSON
        with open(os.path.join(self.viz_dir, 'detailed_statistics.json'), 'w') as f:
            json.dump(stats, f, indent=2)

        # Create text report
        self._create_text_report(stats)

        return stats

    def _create_text_report(self, stats):
        """Create human-readable text report"""
        report_lines = []
        report_lines.append("="*60)
        report_lines.append(f"WEB EXPLORATION ANALYSIS REPORT")
        report_lines.append(f"Run Directory: {self.run_dir}")
        report_lines.append("="*60)

        # Basic stats
        basic = stats['basic_stats']
        report_lines.append(f"\n📊 BASIC STATISTICS")
        report_lines.append(f"   Total URLs discovered: {basic['total_nodes']}")
        report_lines.append(f"   URLs actually visited: {basic['visited_nodes']} ({basic['exploration_efficiency']:.1f}%)")
        report_lines.append(f"   Total visits made: {basic['total_visits']}")
        report_lines.append(f"   Average visits per URL: {basic['avg_visits_per_url']:.1f}")

        # Visit analysis
        visit = stats['visit_analysis']
        report_lines.append(f"\n📈 VISIT ANALYSIS")
        report_lines.append(f"   Maximum visits to single URL: {visit['max_visits']}")
        report_lines.append(f"   Median visits: {visit['median_visits']:.1f}")
        report_lines.append(f"   Standard deviation: {visit['std_visits']:.1f}")

        # URL types
        report_lines.append(f"\n🌐 URL TYPE DISTRIBUTION")
        for url_type, count in sorted(stats['url_type_analysis'].items(),
                                     key=lambda x: x[1], reverse=True):
            percentage = count / basic['total_nodes'] * 100
            report_lines.append(f"   {url_type.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")

        # Edge analysis
        if stats['edge_analysis']:
            report_lines.append(f"\n🔗 EDGE RELATIONSHIP ANALYSIS")
            total_edges = sum(stats['edge_analysis'].values())
            for edge_type, count in stats['edge_analysis'].items():
                percentage = count / total_edges * 100 if total_edges > 0 else 0
                report_lines.append(f"   {edge_type.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")

        # Top URLs
        report_lines.append(f"\n🔥 TOP 10 MOST VISITED URLS")
        for i, (url, visits) in enumerate(stats['top_urls']['most_visited'][:10], 1):
            display_url = self._shorten_url(url, 70)
            report_lines.append(f"   {i:2d}. {display_url} ({visits} visits)")

        report_lines.append(f"\n🔗 TOP 5 MOST CONNECTED URLS")
        for i, (url, connections) in enumerate(stats['top_urls']['most_connected'][:5], 1):
            display_url = self._shorten_url(url, 70)
            report_lines.append(f"   {i}. {display_url} ({connections} connections)")

        # Save report
        with open(os.path.join(self.viz_dir, 'analysis_report.txt'), 'w') as f:
            f.write('\n'.join(report_lines))

        # Print to console
        print('\n'.join(report_lines))

    def find_and_analyze_trajectory(self):
        """Find and analyze the longest trajectory from all tasks"""
        print("Searching for longest trajectory...")

        all_trajectories = []

        # Search through all nodes and tasks
        for node in self.nodes:
            node_dir = os.path.join(self.run_dir, "graph", f"node_{node['node_id']}")
            tasks_dir = os.path.join(node_dir, "tasks")

            if os.path.exists(tasks_dir):
                for task_name in os.listdir(tasks_dir):
                    task_dir = os.path.join(tasks_dir, task_name)
                    if os.path.isdir(task_dir):
                        # Check positive and negative trajectories
                        for traj_type in ['positive_trajs', 'negative_trajs']:
                            traj_type_dir = os.path.join(task_dir, traj_type)
                            if os.path.exists(traj_type_dir):
                                for traj_name in os.listdir(traj_type_dir):
                                    traj_dir = os.path.join(traj_type_dir, traj_name)
                                    if os.path.isdir(traj_dir):
                                        # Count steps
                                        step_count = len([f for f in os.listdir(traj_dir)
                                                        if f.startswith('step_') and
                                                        os.path.isdir(os.path.join(traj_dir, f))])

                                        # Check if has evaluation info
                                        has_eval_info = False
                                        traj_info_path = os.path.join(traj_dir, "traj_info.json")
                                        if os.path.exists(traj_info_path):
                                            try:
                                                with open(traj_info_path, 'r') as f:
                                                    traj_data = json.load(f)
                                                    if traj_data.get('misc', {}).get('evaluation_info', {}).get('output', {}).get('thoughts'):
                                                        has_eval_info = True
                                            except:
                                                pass

                                        all_trajectories.append({
                                            'node_id': node['node_id'],
                                            'node_url': node['url'],
                                            'task_name': task_name,
                                            'traj_type': traj_type,
                                            'traj_name': traj_name,
                                            'step_count': step_count,
                                            'traj_dir': traj_dir,
                                            'has_eval_info': has_eval_info
                                        })

        if not all_trajectories:
            print("No trajectories found!")
            return None

        # Prefer trajectories with evaluation info, then by length
        trajectories_with_eval = [t for t in all_trajectories if t['has_eval_info']]
        if trajectories_with_eval:
            traj = max(trajectories_with_eval, key=lambda x: x['step_count'])
            print(f"Found longest trajectory with evaluation info: {traj['step_count']} steps")
        else:
            traj = max(all_trajectories, key=lambda x: x['step_count'])
            print(f"Found longest trajectory (no eval info): {traj['step_count']} steps")

        # Analyze the longest trajectory
        return self._analyze_trajectory(traj)

    def _analyze_trajectory(self, traj_info):
        """Analyze a specific trajectory in detail"""
        traj_dir = traj_info['traj_dir']

        # Load trajectory info
        traj_info_path = os.path.join(traj_dir, "traj_info.json")
        if os.path.exists(traj_info_path):
            with open(traj_info_path, 'r') as f:
                traj_data = json.load(f)
        else:
            traj_data = {}

        # Load task info
        task_dir = os.path.dirname(os.path.dirname(traj_dir))
        task_info_path = os.path.join(task_dir, "task_info.json")
        if os.path.exists(task_info_path):
            with open(task_info_path, 'r') as f:
                task_data = json.load(f)
        else:
            task_data = {}

        # Load all steps with screenshots
        steps = []
        for i in range(traj_info['step_count']):
            step_dir = os.path.join(traj_dir, f"step_{i}")
            step_info_path = os.path.join(step_dir, "step_info.json")
            if os.path.exists(step_info_path):
                with open(step_info_path, 'r') as f:
                    step_data = json.load(f)
                    step_data['step_number'] = i

                    # Load screenshot if available
                    screenshot_path = os.path.join(step_dir, "screenshot.png")
                    if os.path.exists(screenshot_path):
                        step_data['screenshot_path'] = screenshot_path

                    steps.append(step_data)

        analysis = {
            'trajectory_info': traj_info,
            'task_data': task_data,
            'trajectory_data': traj_data,
            'steps': steps,
            'analysis': {
                'total_steps': len(steps),
                'success': traj_data.get('success', False),
                'reward': traj_data.get('reward', 0),
                'goal': traj_data.get('goal', task_data.get('goal', 'Unknown')),
                'agent_info': traj_data.get('agent_info', {}),
                'final_response': traj_data.get('response', 'No response'),
                'actions_summary': self._summarize_actions(steps),
                'thoughts_summary': self._summarize_thoughts(steps)
            }
        }

        # Create trajectory visualization
        self._create_trajectory_visualization(analysis)

        # Create HTML trajectory visualization
        self._create_trajectory_html(analysis)

        # Save detailed trajectory analysis
        with open(os.path.join(self.viz_dir, 'trajectory_analysis.json'), 'w') as f:
            json.dump(analysis, f, indent=2)

        # Create trajectory report
        self._create_trajectory_report(analysis)

        return analysis

    def _summarize_actions(self, steps):
        """Summarize actions taken in the trajectory"""
        actions = []
        for step in steps:
            parsed_action = step.get('parsed_action', 'Unknown action')
            actions.append(parsed_action)

        # Count action types
        action_types = {}
        for action in actions:
            action_type = action.split('(')[0] if '(' in action else action
            action_types[action_type] = action_types.get(action_type, 0) + 1

        return {
            'all_actions': actions,
            'action_types': action_types,
            'total_actions': len(actions)
        }

    def _summarize_thoughts(self, steps):
        """Summarize thoughts from the trajectory"""
        thoughts = []
        for step in steps:
            thought = step.get('thought', '')
            if thought:
                thoughts.append(thought)

        return {
            'all_thoughts': thoughts,
            'total_thoughts': len(thoughts),
            'avg_thought_length': sum(len(t) for t in thoughts) / len(thoughts) if thoughts else 0
        }

    def _create_trajectory_visualization(self, analysis):
        """Create visualization for the trajectory analysis"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

        # 1. Action types distribution
        action_types = analysis['analysis']['actions_summary']['action_types']
        if action_types:
            ax1.bar(action_types.keys(), action_types.values(), color='skyblue', alpha=0.8)
            ax1.set_title('Action Types Distribution')
            ax1.set_xlabel('Action Type')
            ax1.set_ylabel('Count')
            ax1.tick_params(axis='x', rotation=45)

        # 2. Step progression (thought length over time)
        thoughts = analysis['analysis']['thoughts_summary']['all_thoughts']
        if thoughts:
            thought_lengths = [len(t) for t in thoughts]
            ax2.plot(range(len(thought_lengths)), thought_lengths, marker='o', alpha=0.7)
            ax2.set_title('Thought Length Over Steps')
            ax2.set_xlabel('Step Number')
            ax2.set_ylabel('Thought Length (characters)')

        # 3. Success/Failure info
        success = analysis['analysis']['success']
        reward = analysis['analysis']['reward']

        ax3.bar(['Success', 'Reward'], [1 if success else 0, reward],
               color=['green' if success else 'red', 'blue'], alpha=0.7)
        ax3.set_title('Trajectory Outcome')
        ax3.set_ylabel('Value')
        ax3.set_ylim(0, 1.1)

        # Add text annotations
        ax3.text(0, (1 if success else 0) + 0.05, 'Success' if success else 'Failed',
                ha='center', va='bottom')
        ax3.text(1, reward + 0.05, f'{reward:.2f}', ha='center', va='bottom')

        # 4. Timeline of actions
        actions = analysis['analysis']['actions_summary']['all_actions']
        if actions:
            # Show first 10 actions for readability
            display_actions = actions[:10]
            y_pos = range(len(display_actions))

            ax4.barh(y_pos, [1] * len(display_actions), alpha=0.6)
            ax4.set_yticks(y_pos)
            ax4.set_yticklabels([f"Step {i}: {action[:30]}..." if len(action) > 30 else f"Step {i}: {action}"
                               for i, action in enumerate(display_actions)])
            ax4.set_title('Action Timeline (First 10 Steps)')
            ax4.set_xlabel('Step')

        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, 'trajectory_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _create_trajectory_report(self, analysis):
        """Create detailed text report for trajectory"""
        report_lines = []
        report_lines.append("="*80)
        report_lines.append("LONGEST TRAJECTORY ANALYSIS")
        report_lines.append("="*80)

        # Basic info
        traj_info = analysis['trajectory_info']
        traj_analysis = analysis['analysis']

        report_lines.append(f"\n📍 TRAJECTORY LOCATION")
        report_lines.append(f"   Node: {traj_info['node_id']} ({traj_info['node_url']})")
        report_lines.append(f"   Task: {traj_info['task_name']}")
        report_lines.append(f"   Type: {traj_info['traj_type']}")
        report_lines.append(f"   Trajectory: {traj_info['traj_name']}")

        report_lines.append(f"\n🎯 TASK & OUTCOME")
        report_lines.append(f"   Goal: {traj_analysis['goal']}")
        report_lines.append(f"   Success: {'✅ Yes' if traj_analysis['success'] else '❌ No'}")
        report_lines.append(f"   Reward: {traj_analysis['reward']}")
        report_lines.append(f"   Final Response: {traj_analysis['final_response']}")

        report_lines.append(f"\n📊 TRAJECTORY STATISTICS")
        report_lines.append(f"   Total Steps: {traj_analysis['total_steps']}")
        report_lines.append(f"   Total Thoughts: {traj_analysis['thoughts_summary']['total_thoughts']}")
        report_lines.append(f"   Avg Thought Length: {traj_analysis['thoughts_summary']['avg_thought_length']:.1f} chars")

        # Agent info
        agent_info = traj_analysis['agent_info']
        if agent_info:
            report_lines.append(f"\n🤖 AGENT INFORMATION")
            report_lines.append(f"   Name: {agent_info.get('name', 'Unknown')}")
            report_lines.append(f"   Model: {agent_info.get('model_id', 'Unknown')}")
            report_lines.append(f"   Temperature: {agent_info.get('temperature', 'Unknown')}")

        # Action summary
        actions_summary = traj_analysis['actions_summary']
        report_lines.append(f"\n🎬 ACTION SUMMARY")
        report_lines.append(f"   Total Actions: {actions_summary['total_actions']}")
        for action_type, count in sorted(actions_summary['action_types'].items(),
                                       key=lambda x: x[1], reverse=True):
            report_lines.append(f"   {action_type}: {count}")

        # Step-by-step breakdown (first 5 steps)
        steps = analysis['steps']
        report_lines.append(f"\n📝 STEP-BY-STEP BREAKDOWN (First 5 steps)")
        for i, step in enumerate(steps[:5]):
            report_lines.append(f"\n   Step {i}:")
            report_lines.append(f"     Action: {step.get('parsed_action', 'Unknown')}")
            thought = step.get('thought', '')
            if thought:
                # Truncate long thoughts
                display_thought = thought[:200] + "..." if len(thought) > 200 else thought
                report_lines.append(f"     Thought: {display_thought}")

            # Add screenshot info if available
            if 'screenshot_path' in step:
                report_lines.append(f"     Screenshot: Available")

        if len(steps) > 5:
            report_lines.append(f"\n   ... and {len(steps) - 5} more steps")

        # Save report
        with open(os.path.join(self.viz_dir, 'trajectory_report.txt'), 'w') as f:
            f.write('\n'.join(report_lines))

        # Print summary to console
        print('\n'.join(report_lines[:30]))  # Print first 30 lines to console
        if len(report_lines) > 30:
            print(f"\n... (Full report saved to trajectory_report.txt)")

    def _create_trajectory_html(self, analysis):
        """Create interactive HTML visualization for the trajectory"""
        html_content = self._generate_trajectory_html(analysis)

        with open(os.path.join(self.viz_dir, 'trajectory_interactive.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _generate_trajectory_html(self, analysis):
        """Generate HTML content for trajectory visualization"""
        traj_info = analysis['trajectory_info']
        traj_analysis = analysis['analysis']
        steps = analysis['steps']
        task_data = analysis['task_data']
        trajectory_data = analysis['trajectory_data']

        # Get evaluation info if available
        eval_info = trajectory_data.get('misc', {}).get('evaluation_info', {})
        eval_output = eval_info.get('output', {})

        # Helper function to convert image to base64
        def image_to_base64(image_path):
            try:
                import base64
                with open(image_path, 'rb') as img_file:
                    return base64.b64encode(img_file.read()).decode('utf-8')
            except:
                return None

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trajectory Analysis: {traj_analysis['goal']}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header .subtitle {{
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .summary-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }}
        .summary-card .value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }}
        .success {{
            color: #28a745;
        }}
        .failure {{
            color: #dc3545;
        }}
        .steps-container {{
            padding: 30px;
        }}
        .step {{
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }}
        .step-header {{
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .step-number {{
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        .step-action {{
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }}
        .step-content {{
            padding: 20px;
        }}
        .thought-section {{
            margin-bottom: 20px;
        }}
        .thought-section h4 {{
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1em;
        }}
        .thought-text {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #17a2b8;
            font-style: italic;
            color: #495057;
        }}
        .observation-section {{
            margin-top: 20px;
        }}
        .observation-text {{
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            max-height: 200px;
            overflow-y: auto;
        }}
        .evaluation-section {{
            background: #d1ecf1;
            padding: 20px;
            margin: 30px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }}
        .evaluation-section h3 {{
            margin: 0 0 15px 0;
            color: #0c5460;
        }}
        .agent-info {{
            background: #e2e3e5;
            padding: 20px;
            margin: 30px;
            border-radius: 8px;
        }}
        .agent-info h3 {{
            margin: 0 0 15px 0;
            color: #383d41;
        }}
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        .info-item {{
            background: white;
            padding: 10px;
            border-radius: 4px;
        }}
        .info-label {{
            font-weight: bold;
            color: #495057;
            font-size: 0.9em;
        }}
        .info-value {{
            color: #6c757d;
            font-size: 0.95em;
        }}
        .toggle-btn {{
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }}
        .toggle-btn:hover {{
            background: #5a6268;
        }}
        .collapsible {{
            display: none;
        }}
        .collapsible.show {{
            display: block;
        }}
        .screenshot-container {{
            margin-top: 15px;
            text-align: center;
        }}
        .screenshot {{
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trajectory Analysis</h1>
            <div class="subtitle">
                Task: {traj_analysis['goal']}<br>
                Node {traj_info['node_id']} • {traj_info['task_name']} • {traj_info['traj_type']}
            </div>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>📊 Steps</h3>
                <div class="value">{traj_analysis['total_steps']}</div>
            </div>
            <div class="summary-card">
                <h3>🎯 Success</h3>
                <div class="value {'success' if traj_analysis['success'] else 'failure'}">
                    {'✅ Yes' if traj_analysis['success'] else '❌ No'}
                </div>
            </div>
            <div class="summary-card">
                <h3>🏆 Reward</h3>
                <div class="value">{traj_analysis['reward']}</div>
            </div>
            <div class="summary-card">
                <h3>🤖 Agent</h3>
                <div class="value" style="font-size: 1em;">{traj_analysis['agent_info'].get('name', 'Unknown')}</div>
            </div>
        </div>
"""

        # Add agent information
        agent_info = traj_analysis['agent_info']
        if agent_info:
            html += f"""
        <div class="agent-info">
            <h3>🤖 Agent Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Model</div>
                    <div class="info-value">{agent_info.get('model_id', 'Unknown')}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Temperature</div>
                    <div class="info-value">{agent_info.get('temperature', 'Unknown')}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Character Limit</div>
                    <div class="info-value">{agent_info.get('char_limit', 'Unknown')}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Demo Mode</div>
                    <div class="info-value">{agent_info.get('demo_mode', 'Unknown')}</div>
                </div>
            </div>
        </div>
"""

        # Add evaluation information if available
        if eval_output:
            html += f"""
        <div class="evaluation-section">
            <h3>📝 Evaluation Agent Analysis</h3>
            <p><strong>Final Status:</strong> {eval_output.get('status', 'Unknown')}</p>
            <p><strong>How the evaluator determined success/failure:</strong></p>
            <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                {eval_output.get('thoughts', 'No evaluation thoughts available.')}
            </div>
        </div>
"""

        # Try to get more detailed evaluation info from trajectory data
        eval_details = trajectory_data.get('misc', {})
        if eval_details and 'evaluation_info' in eval_details:
            eval_info_full = eval_details['evaluation_info']
            if 'input' in eval_info_full:
                eval_input = eval_info_full['input']
                html += f"""
        <div class="evaluation-section">
            <h3>🔍 Evaluation Process Details</h3>
            <p><strong>Evaluation Prompt:</strong></p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em;">
                {eval_input.get('prompt', 'No evaluation prompt available.')[:500]}{'...' if len(eval_input.get('prompt', '')) > 500 else ''}
            </div>
        </div>
"""

        # Add final response if available
        if traj_analysis['final_response'] and traj_analysis['final_response'] != 'N/A':
            html += f"""
        <div class="evaluation-section">
            <h3>💬 Final Response</h3>
            <div style="background: white; padding: 15px; border-radius: 4px;">
                {traj_analysis['final_response']}
            </div>
        </div>
"""

        # Add steps
        html += """
        <div class="steps-container">
            <h2>📝 Step-by-Step Breakdown</h2>
"""

        for i, step in enumerate(steps):
            thought = step.get('thought', 'No thought recorded')
            action = step.get('parsed_action', 'Unknown action')
            observation = step.get('observation', {})

            # Get last action and error info
            last_action = observation.get('last_action', 'N/A')
            last_action_error = observation.get('last_action_error', '')

            # Truncate long observations for display
            axtree = observation.get('axtree_visible_only_txt', observation.get('axtree_txt', ''))
            if len(axtree) > 1000:
                axtree = axtree[:1000] + "\\n... (truncated)"

            # Get screenshot as base64 if available
            screenshot_base64 = None
            if 'screenshot_path' in step:
                screenshot_base64 = image_to_base64(step['screenshot_path'])

            html += f"""
            <div class="step">
                <div class="step-header">
                    <div>
                        <span class="step-number">Step {i}</span>
                        <span class="step-action">{action}</span>
                    </div>
                    <button class="toggle-btn" onclick="toggleStep({i})">Toggle Details</button>
                </div>
                <div class="step-content" id="step-content-{i}">
                    <div class="thought-section">
                        <h4>💭 Agent Thought</h4>
                        <div class="thought-text">{thought}</div>
                    </div>

                    <div class="observation-section">
                        <h4>👁️ Observation Summary</h4>
                        <p><strong>Last Action:</strong> {last_action}</p>
                        {f'<p><strong>Error:</strong> <span style="color: #dc3545;">{last_action_error}</span></p>' if last_action_error else ''}

                        <button class="toggle-btn" onclick="toggleObservation({i})">Toggle Full Observation</button>
                        <div id="obs-{i}" class="collapsible">
                            <div class="observation-text">{axtree.replace('<', '&lt;').replace('>', '&gt;')}</div>

                            {f'''
                            <div class="screenshot-container">
                                <h4>📸 Visual Observation (UI Screenshot)</h4>
                                <img src="data:image/png;base64,{screenshot_base64}" class="screenshot" alt="Step {i} Screenshot" />
                            </div>
                            ''' if screenshot_base64 else ''}
                        </div>
                    </div>
                </div>
            </div>
"""

        html += """
        </div>

        <!-- Final Evaluation Section -->
"""

        # Add final evaluation at the end - get from the correct location in traj_info
        eval_thoughts = None
        eval_status = None

        # First try to get from misc.evaluation_info.output (this is where it should be)
        misc_data = trajectory_data.get('misc', {})
        if misc_data and 'evaluation_info' in misc_data:
            eval_info = misc_data['evaluation_info']
            if 'output' in eval_info:
                eval_output_data = eval_info['output']
                eval_thoughts = eval_output_data.get('thoughts')
                eval_status = eval_output_data.get('status')

        # Fallback to the eval_output we got earlier
        if not eval_thoughts and eval_output and eval_output.get('thoughts'):
            eval_thoughts = eval_output.get('thoughts')
            eval_status = eval_output.get('status')

        # If still no eval thoughts, add a note that evaluation info is missing
        if not eval_thoughts:
            html += f"""
        <div class="evaluation-section" style="margin-top: 40px; border: 2px solid #ffc107;">
            <h3>🏁 Final Evaluation Agent Decision</h3>
            <p><strong>Final Status:</strong> <span style="font-size: 1.2em; font-weight: bold; color: {'green' if traj_analysis['success'] else 'red'};">
                {'✅ SUCCESS' if traj_analysis['success'] else '❌ FAILURE'}
            </span></p>
            <p><strong>Evaluation Agent's Reasoning:</strong></p>
            <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <em>No evaluation agent reasoning found in this trajectory. The success/failure status is based on the reward value ({traj_analysis['reward']}).</em>
            </div>
        </div>
"""
        else:
            html += f"""
        <div class="evaluation-section" style="margin-top: 40px; border: 2px solid #17a2b8;">
            <h3>🏁 Final Evaluation Agent Decision</h3>
            <p><strong>Final Status:</strong> <span style="font-size: 1.2em; font-weight: bold; color: {'green' if traj_analysis['success'] else 'red'};">
                {'✅ SUCCESS' if traj_analysis['success'] else '❌ FAILURE'}
            </span>
            {f'(Eval Status: {eval_status})' if eval_status else ''}</p>
            <p><strong>Evaluation Agent's Final Reasoning:</strong></p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8; font-style: italic;">
                {eval_thoughts}
            </div>
            <p style="margin-top: 15px; font-size: 0.9em; color: #6c757d;">
                <strong>Note:</strong> This is how the evaluation agent determined whether the task was completed successfully or not.
            </p>
        </div>
"""

        html += """
    </div>

    <script>
        function toggleStep(stepNum) {
            const element = document.getElementById('step-content-' + stepNum);
            if (element) {
                element.classList.toggle('collapsible');
                element.classList.toggle('show');
            }
        }

        function toggleObservation(stepNum) {
            const element = document.getElementById('obs-' + stepNum);
            element.classList.toggle('show');
        }

        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Scroll to final evaluation on page load if it exists
        window.addEventListener('load', function() {
            const finalEval = document.querySelector('.evaluation-section:last-of-type');
            if (finalEval) {
                // Add a subtle highlight animation
                finalEval.style.animation = 'highlight 2s ease-in-out';
            }
        });
    </script>

    <style>
        @keyframes highlight {
            0% { background-color: rgba(23, 162, 184, 0.1); }
            50% { background-color: rgba(23, 162, 184, 0.2); }
            100% { background-color: transparent; }
        }
    </style>
</body>
</html>
"""

        return html

    def create_website_level_analysis(self):
        """Create website-level analysis and visualizations"""
        print("Creating website-level analysis...")

        # Analyze website structure
        website_stats = self._analyze_website_structure()

        # Create website visualizations
        self._create_website_visualizations(website_stats)

        # Save website analysis
        with open(os.path.join(self.viz_dir, 'website_analysis.json'), 'w') as f:
            json.dump(website_stats, f, indent=2)

        return website_stats

    def _analyze_website_structure(self):
        """Analyze the overall website structure and patterns with focus on localhost insights"""
        website_stats = {
            'localhost_deep_analysis': {},
            'path_hierarchy': {},
            'user_journey_patterns': {},
            'content_discovery': {},
            'navigation_efficiency': {},
            'page_importance_ranking': {}
        }

        # Deep localhost analysis
        localhost_urls = {url: stats for url, stats in self.url_stats.items() if stats['is_localhost']}

        # Analyze localhost URL patterns and structure
        localhost_analysis = {
            'total_localhost_urls': len(localhost_urls),
            'visited_localhost_urls': sum(1 for stats in localhost_urls.values() if stats['is_visited']),
            'total_localhost_visits': sum(stats['visit_count'] for stats in localhost_urls.values()),
            'url_depth_analysis': {},
            'functional_areas': {},
            'user_flow_patterns': {},
            'content_accessibility': {}
        }

        # Analyze URL depth and structure
        depth_stats = defaultdict(lambda: {'count': 0, 'visited': 0, 'avg_visits': 0})
        for url, stats in localhost_urls.items():
            path = stats['path']
            depth = len([p for p in path.split('/') if p])  # Count path segments
            depth_stats[depth]['count'] += 1
            if stats['is_visited']:
                depth_stats[depth]['visited'] += 1
            depth_stats[depth]['avg_visits'] += stats['visit_count']

        # Calculate averages
        for depth in depth_stats:
            if depth_stats[depth]['count'] > 0:
                depth_stats[depth]['avg_visits'] /= depth_stats[depth]['count']

        localhost_analysis['url_depth_analysis'] = dict(depth_stats)

        # Analyze functional areas in detail
        functional_areas = {
            'authentication': {'urls': [], 'patterns': [], 'visit_efficiency': 0},
            'content_browsing': {'urls': [], 'patterns': [], 'visit_efficiency': 0},
            'wiki_system': {'urls': [], 'patterns': [], 'visit_efficiency': 0},
            'forum_system': {'urls': [], 'patterns': [], 'visit_efficiency': 0},
            'search_discovery': {'urls': [], 'patterns': [], 'visit_efficiency': 0},
            'navigation_hubs': {'urls': [], 'patterns': [], 'visit_efficiency': 0}
        }

        for url, stats in localhost_urls.items():
            path = stats['path'].lower()

            # Categorize by functional area
            if any(keyword in path for keyword in ['/login', '/register', '/auth', '/signup']):
                functional_areas['authentication']['urls'].append((url, stats))
            elif any(keyword in path for keyword in ['/wiki']):
                functional_areas['wiki_system']['urls'].append((url, stats))
            elif any(keyword in path for keyword in ['/f/', '/forum', '/post', '/comment']):
                functional_areas['forum_system']['urls'].append((url, stats))
            elif any(keyword in path for keyword in ['/search', '/find']):
                functional_areas['search_discovery']['urls'].append((url, stats))
            elif path in ['/', '/new', '/hot', '/top', '/active', '/controversial']:
                functional_areas['navigation_hubs']['urls'].append((url, stats))
            else:
                functional_areas['content_browsing']['urls'].append((url, stats))

        # Calculate efficiency and patterns for each area
        for area_name, area_data in functional_areas.items():
            urls = area_data['urls']
            if urls:
                total_urls = len(urls)
                visited_urls = sum(1 for _, stats in urls if stats['is_visited'])
                total_visits = sum(stats['visit_count'] for _, stats in urls)

                area_data['visit_efficiency'] = visited_urls / total_urls * 100 if total_urls > 0 else 0
                area_data['total_urls'] = total_urls
                area_data['visited_urls'] = visited_urls
                area_data['total_visits'] = total_visits
                area_data['avg_visits_per_url'] = total_visits / total_urls if total_urls > 0 else 0

                # Identify patterns
                if area_name == 'authentication':
                    # Look for login flow patterns
                    login_urls = [url for url, _ in urls if 'login' in url.lower()]
                    register_urls = [url for url, _ in urls if any(kw in url.lower() for kw in ['register', 'signup'])]
                    area_data['patterns'] = {
                        'login_pages': len(login_urls),
                        'registration_pages': len(register_urls),
                        'auth_complexity': 'High' if total_urls > 10 else 'Medium' if total_urls > 5 else 'Low'
                    }
                elif area_name == 'wiki_system':
                    # Analyze wiki structure
                    wiki_pages = [url for url, _ in urls]
                    area_data['patterns'] = {
                        'wiki_pages_discovered': len(wiki_pages),
                        'wiki_depth': max(len(url.split('/')) for url in wiki_pages) if wiki_pages else 0,
                        'wiki_coverage': 'Extensive' if total_urls > 20 else 'Moderate' if total_urls > 5 else 'Limited'
                    }
                elif area_name == 'forum_system':
                    # Analyze forum structure
                    forum_urls = [url for url, _ in urls]
                    area_data['patterns'] = {
                        'forum_threads': len([url for url in forum_urls if '/f/' in url]),
                        'forum_complexity': 'High' if total_urls > 15 else 'Medium' if total_urls > 5 else 'Low'
                    }

        localhost_analysis['functional_areas'] = functional_areas

        # Analyze user journey patterns
        user_journey_patterns = self._analyze_user_journeys(localhost_urls)
        localhost_analysis['user_journey_patterns'] = user_journey_patterns

        # Page importance ranking
        page_importance = self._calculate_page_importance(localhost_urls)
        localhost_analysis['page_importance_ranking'] = page_importance

        # Content accessibility analysis
        content_accessibility = self._analyze_content_accessibility(localhost_urls)
        localhost_analysis['content_accessibility'] = content_accessibility

        website_stats['localhost_deep_analysis'] = localhost_analysis

        # Navigation patterns (analyze edges)
        if self.G:
            navigation_patterns = {
                'total_edges': len(self.G.edges()),
                'avg_outgoing_links': sum(len(list(self.G.successors(node))) for node in self.G.nodes()) / len(self.G.nodes()) if self.G.nodes() else 0,
                'most_connected_pages': [],
                'navigation_hubs': []
            }

            # Find most connected pages
            node_connections = [(node, len(list(self.G.successors(node)))) for node in self.G.nodes()]
            most_connected = sorted(node_connections, key=lambda x: x[1], reverse=True)[:5]
            navigation_patterns['most_connected_pages'] = most_connected

            # Find navigation hubs (high in-degree and out-degree)
            hubs = []
            for node in self.G.nodes():
                in_degree = len(list(self.G.predecessors(node)))
                out_degree = len(list(self.G.successors(node)))
                if in_degree > 2 and out_degree > 2:  # Arbitrary threshold
                    hubs.append((node, in_degree, out_degree))

            navigation_patterns['navigation_hubs'] = sorted(hubs, key=lambda x: x[1] + x[2], reverse=True)[:5]

            website_stats['navigation_patterns'] = navigation_patterns

        return website_stats

    def _analyze_user_journeys(self, localhost_urls):
        """Analyze user journey patterns within localhost"""
        journey_patterns = {
            'entry_points': [],
            'popular_paths': [],
            'dead_ends': [],
            'navigation_loops': []
        }

        if not self.G:
            return journey_patterns

        # Find entry points (pages with high visit count but few incoming links)
        for url, stats in localhost_urls.items():
            if url in self.G.nodes:
                in_degree = len(list(self.G.predecessors(url)))
                visit_count = stats['visit_count']

                if visit_count > 50 and in_degree < 3:  # High visits, low incoming links
                    journey_patterns['entry_points'].append({
                        'url': url,
                        'visit_count': visit_count,
                        'incoming_links': in_degree,
                        'entry_score': visit_count / max(in_degree, 1)
                    })

        # Find popular navigation paths
        for url in self.G.nodes():
            if url in localhost_urls:
                successors = list(self.G.successors(url))
                if len(successors) > 5:  # Pages with many outgoing links
                    # Calculate path popularity
                    path_popularity = sum(localhost_urls.get(succ, {}).get('visit_count', 0)
                                        for succ in successors if succ in localhost_urls)

                    journey_patterns['popular_paths'].append({
                        'hub_url': url,
                        'outgoing_links': len(successors),
                        'path_popularity_score': path_popularity,
                        'hub_visits': localhost_urls[url]['visit_count']
                    })

        # Find dead ends (visited pages with no outgoing links)
        for url, stats in localhost_urls.items():
            if stats['is_visited'] and url in self.G.nodes:
                out_degree = len(list(self.G.successors(url)))
                if out_degree == 0:
                    journey_patterns['dead_ends'].append({
                        'url': url,
                        'visit_count': stats['visit_count'],
                        'path': stats['path']
                    })

        # Sort results
        journey_patterns['entry_points'].sort(key=lambda x: x['entry_score'], reverse=True)
        journey_patterns['popular_paths'].sort(key=lambda x: x['path_popularity_score'], reverse=True)
        journey_patterns['dead_ends'].sort(key=lambda x: x['visit_count'], reverse=True)

        return journey_patterns

    def _calculate_page_importance(self, localhost_urls):
        """Calculate page importance based on multiple factors"""
        importance_scores = []

        for url, stats in localhost_urls.items():
            # Calculate importance score based on multiple factors
            visit_score = stats['visit_count'] * 0.4  # 40% weight

            # Network centrality (if in graph)
            centrality_score = 0
            if self.G and url in self.G.nodes:
                in_degree = len(list(self.G.predecessors(url)))
                out_degree = len(list(self.G.successors(url)))
                centrality_score = (in_degree + out_degree) * 0.3  # 30% weight

            # Path depth (shorter paths are more important)
            depth = len([p for p in stats['path'].split('/') if p])
            depth_score = max(0, (5 - depth)) * 0.2  # 20% weight, prefer shallower pages

            # Content type importance
            content_score = 0
            path = stats['path'].lower()
            if path in ['/', '/new', '/hot', '/top']:
                content_score = 10  # Main pages are very important
            elif '/wiki' in path:
                content_score = 8   # Wiki pages are important
            elif '/f/' in path:
                content_score = 6   # Forum pages are moderately important
            elif '/login' in path:
                content_score = 4   # Auth pages are less important for content

            content_score *= 0.1  # 10% weight

            total_score = visit_score + centrality_score + depth_score + content_score

            importance_scores.append({
                'url': url,
                'total_score': total_score,
                'visit_score': visit_score,
                'centrality_score': centrality_score,
                'depth_score': depth_score,
                'content_score': content_score,
                'is_visited': stats['is_visited'],
                'path_type': self._get_path_type(stats['path'])
            })

        # Sort by importance score
        importance_scores.sort(key=lambda x: x['total_score'], reverse=True)

        return {
            'top_important_pages': importance_scores[:20],
            'important_unvisited': [page for page in importance_scores if not page['is_visited']][:10],
            'scoring_methodology': {
                'visit_weight': 0.4,
                'centrality_weight': 0.3,
                'depth_weight': 0.2,
                'content_weight': 0.1
            }
        }

    def _analyze_content_accessibility(self, localhost_urls):
        """Analyze how accessible different content types are"""
        accessibility_analysis = {
            'content_types': {},
            'accessibility_barriers': [],
            'discovery_patterns': {}
        }

        # Group by content type and analyze accessibility
        content_groups = defaultdict(list)
        for url, stats in localhost_urls.items():
            content_type = self._get_path_type(stats['path'])
            content_groups[content_type].append((url, stats))

        for content_type, urls_stats in content_groups.items():
            total_urls = len(urls_stats)
            visited_urls = sum(1 for _, stats in urls_stats if stats['is_visited'])
            total_visits = sum(stats['visit_count'] for _, stats in urls_stats)

            # Calculate accessibility metrics
            accessibility_score = visited_urls / total_urls * 100 if total_urls > 0 else 0

            # Find barriers (high-value content that's not visited)
            unvisited_important = []
            for url, stats in urls_stats:
                if not stats['is_visited'] and stats['visit_count'] == 0:
                    # Check if it's linked from visited pages
                    linked_from_visited = False
                    if self.G and url in self.G.nodes:
                        for pred in self.G.predecessors(url):
                            if pred in localhost_urls and localhost_urls[pred]['is_visited']:
                                linked_from_visited = True
                                break

                    if linked_from_visited:
                        unvisited_important.append(url)

            accessibility_analysis['content_types'][content_type] = {
                'total_urls': total_urls,
                'visited_urls': visited_urls,
                'accessibility_score': accessibility_score,
                'total_visits': total_visits,
                'avg_visits_per_url': total_visits / total_urls if total_urls > 0 else 0,
                'unvisited_but_linked': len(unvisited_important),
                'sample_unvisited': unvisited_important[:5]
            }

        return accessibility_analysis

    def _get_path_type(self, path):
        """Categorize path by type"""
        path_lower = path.lower()
        if path_lower in ['/', '/new', '/hot', '/top', '/active', '/controversial']:
            return 'main_navigation'
        elif '/wiki' in path_lower:
            return 'wiki_content'
        elif '/f/' in path_lower:
            return 'forum_content'
        elif any(kw in path_lower for kw in ['/login', '/register', '/auth', '/signup']):
            return 'authentication'
        elif '/search' in path_lower:
            return 'search_discovery'
        else:
            return 'other_content'

    def _create_website_visualizations(self, website_stats):
        """Create website-level visualizations focused on localhost insights"""
        fig = plt.figure(figsize=(28, 20))

        # Create a 4x3 grid for more detailed analysis
        gs = fig.add_gridspec(4, 3, hspace=0.4, wspace=0.3)

        localhost_analysis = website_stats['localhost_deep_analysis']

        # Get localhost URLs for analysis
        localhost_urls_for_discovery = {url: stats for url, stats in self.url_stats.items() if stats['is_localhost']}

        # 1. URL Path Depth Distribution (top-left)
        ax1 = fig.add_subplot(gs[0, 0])
        depth_analysis = localhost_analysis['url_depth_analysis']

        if depth_analysis:
            depths = sorted(depth_analysis.keys())
            depth_counts = [depth_analysis[d]['count'] for d in depths]
            depth_visited = [depth_analysis[d]['visited'] for d in depths]

            x = range(len(depths))
            width = 0.35

            ax1.bar([i - width/2 for i in x], depth_counts, width,
                   label='Total URLs', color='lightblue', alpha=0.8)
            ax1.bar([i + width/2 for i in x], depth_visited, width,
                   label='Visited URLs', color='darkblue', alpha=0.8)

            ax1.set_xlabel('URL Depth (Path Segments)')
            ax1.set_ylabel('Number of URLs')
            ax1.set_title('URL Depth Distribution\n(How deep are the URLs?)')
            ax1.set_xticks(x)
            ax1.set_xticklabels([f'Depth {d}' for d in depths])
            ax1.legend()

            # Add visit rate info
            for i, depth in enumerate(depths):
                total = depth_analysis[depth]['count']
                visited = depth_analysis[depth]['visited']
                rate = (visited / total * 100) if total > 0 else 0
                ax1.text(i, max(depth_counts) * 0.9, f'{rate:.0f}%',
                        ha='center', va='bottom', fontsize=8)

        # 2. Visit Count vs Incoming Links Scatter (top-center)
        ax2 = fig.add_subplot(gs[0, 1])

        # Create scatter plot of visit count vs incoming links
        visit_counts = []
        incoming_links = []
        colors = []

        for url, stats in localhost_urls_for_discovery.items():
            if self.G and url in self.G.nodes and stats['visit_count'] > 0:  # Only visited pages
                in_degree = len(list(self.G.predecessors(url)))
                visit_counts.append(stats['visit_count'])
                incoming_links.append(in_degree)

                # Color by URL type
                url_type = self._get_path_type(stats['path'])
                if url_type == 'main_navigation':
                    colors.append('red')
                elif url_type == 'wiki_content':
                    colors.append('blue')
                elif url_type == 'forum_content':
                    colors.append('green')
                elif url_type == 'authentication':
                    colors.append('orange')
                else:
                    colors.append('gray')

        if visit_counts:
            scatter = ax2.scatter(incoming_links, visit_counts, c=colors, alpha=0.7, s=60)
            ax2.set_xlabel('Number of Incoming Links')
            ax2.set_ylabel('Visit Count')
            ax2.set_title('Visit Count vs Incoming Links\n(Color = URL type)')

            # Add trend line
            if len(visit_counts) > 1:
                import numpy as np
                z = np.polyfit(incoming_links, visit_counts, 1)
                p = np.poly1d(z)
                ax2.plot(sorted(incoming_links), p(sorted(incoming_links)), "r--", alpha=0.8)

            # Add legend for colors
            legend_elements = [
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='Main Nav'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='Wiki'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=8, label='Forum'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='orange', markersize=8, label='Auth'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', markersize=8, label='Other')
            ]
            ax2.legend(handles=legend_elements, loc='upper right', fontsize=8)

        # 3. Most Visited Pages (top-right)
        ax3 = fig.add_subplot(gs[0, 2])

        # Get top visited pages directly
        visited_pages = [(url, stats['visit_count']) for url, stats in localhost_urls_for_discovery.items()
                        if stats['is_visited']]
        visited_pages.sort(key=lambda x: x[1], reverse=True)
        top_visited = visited_pages[:8]

        if top_visited:
            page_names = [self._shorten_url(url, 25) for url, _ in top_visited]
            visit_counts = [count for _, count in top_visited]

            bars = ax3.barh(range(len(page_names)), visit_counts, color='green', alpha=0.7)
            ax3.set_yticks(range(len(page_names)))
            ax3.set_yticklabels(page_names)
            ax3.set_xlabel('Visit Count')
            ax3.set_title('Most Visited Pages\n(Direct visit count metric)')
            ax3.invert_yaxis()  # Highest visits at top

        # 4. High Visit, Low Incoming Link Pages (second row, left)
        ax4 = fig.add_subplot(gs[1, 0])

        # Find pages with high visits but few incoming links (potential entry points)
        entry_candidates = []
        for url, stats in localhost_urls_for_discovery.items():
            if stats['visit_count'] > 10 and self.G and url in self.G.nodes:  # Only high-visit pages
                in_degree = len(list(self.G.predecessors(url)))
                if in_degree <= 3:  # Few incoming links
                    entry_candidates.append((url, stats['visit_count'], in_degree))

        # Sort by visit count
        entry_candidates.sort(key=lambda x: x[1], reverse=True)
        top_entries = entry_candidates[:6]

        if top_entries:
            entry_names = [self._shorten_url(url, 20) for url, _, _ in top_entries]
            visit_counts = [visits for _, visits, _ in top_entries]

            bars = ax4.bar(range(len(entry_names)), visit_counts, color='purple', alpha=0.7)
            ax4.set_xticks(range(len(entry_names)))
            ax4.set_xticklabels(entry_names, rotation=45, ha='right')
            ax4.set_title('Potential Entry Points\n(High visits, ≤3 incoming links)')
            ax4.set_ylabel('Visit Count')

            # Add incoming link info as text
            for i, (_, visits, in_links) in enumerate(top_entries):
                ax4.text(i, visits + max(visit_counts)*0.01, f'{in_links} links',
                        ha='center', va='bottom', fontsize=8)

        # 5. Pages by Outgoing Link Count (second row, center)
        ax5 = fig.add_subplot(gs[1, 1])

        # Analyze pages by number of outgoing links
        outgoing_link_analysis = []
        for url, stats in localhost_urls_for_discovery.items():
            if self.G and url in self.G.nodes:
                out_degree = len(list(self.G.successors(url)))
                outgoing_link_analysis.append((url, out_degree, stats['visit_count'], stats['is_visited']))

        # Group by outgoing link ranges
        link_ranges = {
            '0 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '1-5 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '6-20 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '21+ links': {'total': 0, 'visited': 0, 'total_visits': 0}
        }

        for url, out_links, visits, is_visited in outgoing_link_analysis:
            if out_links == 0:
                range_key = '0 links'
            elif out_links <= 5:
                range_key = '1-5 links'
            elif out_links <= 20:
                range_key = '6-20 links'
            else:
                range_key = '21+ links'

            link_ranges[range_key]['total'] += 1
            if is_visited:
                link_ranges[range_key]['visited'] += 1
                link_ranges[range_key]['total_visits'] += visits

        range_names = list(link_ranges.keys())
        total_counts = [link_ranges[r]['total'] for r in range_names]
        visited_counts = [link_ranges[r]['visited'] for r in range_names]

        x = range(len(range_names))
        width = 0.35

        ax5.bar([i - width/2 for i in x], total_counts, width,
               label='Total URLs', color='lightgreen', alpha=0.8)
        ax5.bar([i + width/2 for i in x], visited_counts, width,
               label='Visited URLs', color='darkgreen', alpha=0.8)

        ax5.set_xlabel('Outgoing Link Count')
        ax5.set_ylabel('Number of URLs')
        ax5.set_title('URLs by Outgoing Link Count\n(Navigation capability)')
        ax5.set_xticks(x)
        ax5.set_xticklabels(range_names)
        ax5.legend()

        # 6. Pages with Most Outgoing Links (second row, right)
        ax6 = fig.add_subplot(gs[1, 2])

        # Find pages with most outgoing links (navigation hubs)
        hub_pages = []
        for url, stats in localhost_urls_for_discovery.items():
            if self.G and url in self.G.nodes:
                out_degree = len(list(self.G.successors(url)))
                if out_degree > 0:
                    hub_pages.append((url, out_degree, stats['visit_count']))

        # Sort by outgoing links
        hub_pages.sort(key=lambda x: x[1], reverse=True)
        top_hubs = hub_pages[:8]

        if top_hubs:
            hub_names = [self._shorten_url(url, 20) for url, _, _ in top_hubs]
            out_degrees = [out_deg for _, out_deg, _ in top_hubs]

            bars = ax6.barh(range(len(hub_names)), out_degrees, color='orange', alpha=0.7)
            ax6.set_yticks(range(len(hub_names)))
            ax6.set_yticklabels(hub_names)
            ax6.set_xlabel('Number of Outgoing Links')
            ax6.set_title('Navigation Hub Pages\n(Most outgoing links)')
            ax6.invert_yaxis()

            # Add visit count info as text
            for i, (_, out_deg, visits) in enumerate(top_hubs):
                ax6.text(out_deg + max(out_degrees)*0.01, i, f'{visits} visits',
                        ha='left', va='center', fontsize=8)

        # 7. Pages by Incoming Link Count (third row, left)
        ax7 = fig.add_subplot(gs[2, 0])

        # Analyze pages by number of incoming links
        incoming_link_analysis = []
        for url, stats in localhost_urls_for_discovery.items():
            if self.G and url in self.G.nodes:
                in_degree = len(list(self.G.predecessors(url)))
                incoming_link_analysis.append((url, in_degree, stats['visit_count'], stats['is_visited']))

        # Group by incoming link ranges
        link_ranges = {
            '0 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '1-2 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '3-5 links': {'total': 0, 'visited': 0, 'total_visits': 0},
            '6+ links': {'total': 0, 'visited': 0, 'total_visits': 0}
        }

        for url, in_links, visits, is_visited in incoming_link_analysis:
            if in_links == 0:
                range_key = '0 links'
            elif in_links <= 2:
                range_key = '1-2 links'
            elif in_links <= 5:
                range_key = '3-5 links'
            else:
                range_key = '6+ links'

            link_ranges[range_key]['total'] += 1
            if is_visited:
                link_ranges[range_key]['visited'] += 1
                link_ranges[range_key]['total_visits'] += visits

        range_names = list(link_ranges.keys())
        total_counts = [link_ranges[r]['total'] for r in range_names]
        visited_counts = [link_ranges[r]['visited'] for r in range_names]

        x = range(len(range_names))
        width = 0.35

        ax7.bar([i - width/2 for i in x], total_counts, width,
               label='Total URLs', color='lightcoral', alpha=0.8)
        ax7.bar([i + width/2 for i in x], visited_counts, width,
               label='Visited URLs', color='darkred', alpha=0.8)

        ax7.set_xlabel('Incoming Link Count')
        ax7.set_ylabel('Number of URLs')
        ax7.set_title('URLs by Incoming Link Count\n(How well-connected are pages?)')
        ax7.set_xticks(x)
        ax7.set_xticklabels(range_names)
        ax7.legend()

        # 8. Unvisited High-Link Pages (third row, center)
        ax8 = fig.add_subplot(gs[2, 1])

        # Find unvisited pages with many incoming links (missed opportunities)
        unvisited_high_links = []
        for url, stats in localhost_urls_for_discovery.items():
            if not stats['is_visited'] and self.G and url in self.G.nodes:
                in_degree = len(list(self.G.predecessors(url)))
                if in_degree >= 2:  # Pages with multiple incoming links
                    unvisited_high_links.append((url, in_degree))

        # Sort by incoming links
        unvisited_high_links.sort(key=lambda x: x[1], reverse=True)
        top_unvisited = unvisited_high_links[:8]

        if top_unvisited:
            unvisited_names = [self._shorten_url(url, 20) for url, _ in top_unvisited]
            link_counts = [links for _, links in top_unvisited]

            bars = ax8.barh(range(len(unvisited_names)), link_counts,
                           color='orange', alpha=0.7)
            ax8.set_yticks(range(len(unvisited_names)))
            ax8.set_yticklabels(unvisited_names)
            ax8.set_xlabel('Incoming Link Count')
            ax8.set_title('Unvisited Well-Connected Pages\n(Missed opportunities)')
            ax8.invert_yaxis()

        # 9. URL Types Distribution (third row, right)
        ax9 = fig.add_subplot(gs[2, 2])

        # Count URLs by type
        url_type_counts = defaultdict(lambda: {'total': 0, 'visited': 0})
        for url, stats in localhost_urls_for_discovery.items():
            url_type = self._get_path_type(stats['path'])
            url_type_counts[url_type]['total'] += 1
            if stats['is_visited']:
                url_type_counts[url_type]['visited'] += 1

        # Prepare data for visualization
        type_names = list(url_type_counts.keys())
        total_counts = [url_type_counts[t]['total'] for t in type_names]
        visited_counts = [url_type_counts[t]['visited'] for t in type_names]

        x = range(len(type_names))
        width = 0.35

        ax9.bar([i - width/2 for i in x], total_counts, width,
               label='Total URLs', color='lightgreen', alpha=0.8)
        ax9.bar([i + width/2 for i in x], visited_counts, width,
               label='Visited URLs', color='darkgreen', alpha=0.8)

        ax9.set_xlabel('URL Types')
        ax9.set_ylabel('Number of URLs')
        ax9.set_title('URL Types Distribution\n(What types of content exist?)')
        ax9.set_xticks(x)
        ax9.set_xticklabels([t.replace('_', ' ').title() for t in type_names], rotation=45, ha='right')
        ax9.legend()

        # 10-12. Fourth row for additional insights
        # 10. Content Type Performance Analysis (fourth row, left)
        ax10 = fig.add_subplot(gs[3, 0])

        # Analyze performance by content type
        content_performance = {}
        for url, stats in localhost_urls_for_discovery.items():
            content_type = self._get_path_type(stats['path'])
            if content_type not in content_performance:
                content_performance[content_type] = {
                    'total_urls': 0,
                    'visited_urls': 0,
                    'total_visits': 0,
                    'avg_visits_per_visited': 0
                }

            content_performance[content_type]['total_urls'] += 1
            if stats['is_visited']:
                content_performance[content_type]['visited_urls'] += 1
                content_performance[content_type]['total_visits'] += stats['visit_count']

        # Calculate averages
        for ct in content_performance:
            if content_performance[ct]['visited_urls'] > 0:
                content_performance[ct]['avg_visits_per_visited'] = (
                    content_performance[ct]['total_visits'] / content_performance[ct]['visited_urls']
                )

        # Create visualization
        content_types = list(content_performance.keys())
        avg_visits = [content_performance[ct]['avg_visits_per_visited'] for ct in content_types]

        if content_types:
            bars = ax10.bar(range(len(content_types)), avg_visits, color='skyblue', alpha=0.8)
            ax10.set_xticks(range(len(content_types)))
            ax10.set_xticklabels([ct.replace('_', ' ').title() for ct in content_types],
                               rotation=45, ha='right')
            ax10.set_title('Content Type Performance\n(Avg visits per visited page)')
            ax10.set_ylabel('Average Visits')

            # Add explanation
            ax10.text(0.02, 0.98, 'Shows engagement level\nfor each content type',
                    transform=ax10.transAxes, fontsize=8, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

        # 11. Exploration Depth vs Success Analysis (fourth row, center)
        ax11 = fig.add_subplot(gs[3, 1])

        # Analyze relationship between URL depth and visit success
        depth_success = defaultdict(lambda: {'total': 0, 'visited': 0, 'total_visits': 0})

        for url, stats in localhost_urls_for_discovery.items():
            depth = len([p for p in stats['path'].split('/') if p])
            depth_success[depth]['total'] += 1
            if stats['is_visited']:
                depth_success[depth]['visited'] += 1
                depth_success[depth]['total_visits'] += stats['visit_count']

        depths = sorted(depth_success.keys())
        success_rates = []
        avg_visits = []

        for depth in depths:
            data = depth_success[depth]
            success_rate = (data['visited'] / data['total'] * 100) if data['total'] > 0 else 0
            avg_visit = (data['total_visits'] / data['visited']) if data['visited'] > 0 else 0
            success_rates.append(success_rate)
            avg_visits.append(avg_visit)

        if depths:
            # Create dual axis plot
            ax11_twin = ax11.twinx()

            line1 = ax11.plot(depths, success_rates, 'o-', color='blue', label='Visit Success Rate (%)')
            line2 = ax11_twin.plot(depths, avg_visits, 's-', color='red', label='Avg Visits (when visited)')

            ax11.set_xlabel('URL Depth (Path Segments)')
            ax11.set_ylabel('Visit Success Rate (%)', color='blue')
            ax11_twin.set_ylabel('Average Visits', color='red')
            ax11.set_title('Exploration Depth vs Success\n(Deeper = Harder to reach?)')

            # Combine legends
            lines1, labels1 = ax11.get_legend_handles_labels()
            lines2, labels2 = ax11_twin.get_legend_handles_labels()
            ax11.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

            # Add explanation
            ax11.text(0.02, 0.02, 'Success Rate = % of URLs at depth that were visited\nAvg Visits = Average visits for visited URLs at depth',
                    transform=ax11.transAxes, fontsize=7, verticalalignment='bottom',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

        # 12. URL Length vs Visit Success (fourth row, right)
        ax12 = fig.add_subplot(gs[3, 2])

        # Analyze URL length vs visit success (direct metric)
        length_analysis = []

        for url, stats in localhost_urls_for_discovery.items():
            url_length = len(url)
            length_analysis.append({
                'url': url,
                'length': url_length,
                'is_visited': stats['is_visited'],
                'visit_count': stats['visit_count']
            })

        # Group by length ranges
        length_ranges = {
            'Short (<50)': {'total': 0, 'visited': 0},
            'Medium (50-100)': {'total': 0, 'visited': 0},
            'Long (100-150)': {'total': 0, 'visited': 0},
            'Very Long (150+)': {'total': 0, 'visited': 0}
        }

        for item in length_analysis:
            length = item['length']
            if length < 50:
                range_key = 'Short (<50)'
            elif length < 100:
                range_key = 'Medium (50-100)'
            elif length < 150:
                range_key = 'Long (100-150)'
            else:
                range_key = 'Very Long (150+)'

            length_ranges[range_key]['total'] += 1
            if item['is_visited']:
                length_ranges[range_key]['visited'] += 1

        # Calculate success rates
        range_names = list(length_ranges.keys())
        success_rates = []
        total_counts = []

        for range_name in range_names:
            data = length_ranges[range_name]
            success_rate = (data['visited'] / data['total'] * 100) if data['total'] > 0 else 0
            success_rates.append(success_rate)
            total_counts.append(data['total'])

        if range_names and any(total_counts):
            bars = ax12.bar(range(len(range_names)), success_rates,
                           color=['green', 'yellow', 'orange', 'red'], alpha=0.7)
            ax12.set_xticks(range(len(range_names)))
            ax12.set_xticklabels(range_names, rotation=45, ha='right')
            ax12.set_ylabel('Visit Success Rate (%)')
            ax12.set_title('URL Length vs Visit Success\n(Character count impact)')

            # Add value labels with counts
            for i, (bar, rate, count) in enumerate(zip(bars, success_rates, total_counts)):
                ax12.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                         f'{rate:.1f}%\n({count} URLs)', ha='center', va='bottom', fontsize=8)

        plt.suptitle('Localhost Website Deep Analysis', fontsize=18, fontweight='bold')
        plt.savefig(os.path.join(self.viz_dir, 'website_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def run_complete_analysis(self):
        """Run complete analysis and generate all visualizations"""
        print("Starting complete web exploration analysis...")

        # Load data
        self.load_data()

        # Analyze patterns
        self.analyze_url_patterns()

        # Build graph
        self.build_graph()

        # Create all visualizations
        print("Creating visit analysis visualizations...")
        self.create_visit_analysis()

        print("Creating network visualizations...")
        self.create_network_visualizations()

        print("Generating detailed statistics...")
        stats = self.create_detailed_statistics()

        # New analyses
        print("Analyzing longest trajectory...")
        trajectory_analysis = self.find_and_analyze_trajectory()

        print("Creating website-level analysis...")
        website_stats = self.create_website_level_analysis()

        print(f"\nAnalysis complete! Results saved to: {self.viz_dir}")
        print(f"Generated files:")
        print(f"  📊 URL & Visit Analysis:")
        print(f"    - visit_analysis.png")
        print(f"    - detailed_statistics.json")
        print(f"    - analysis_report.txt")
        print(f"  🕸️  Network Analysis:")
        print(f"    - main_network_graph.png")
        print(f"    - edge_relationship_analysis.png")
        print(f"    - visit_pattern_network.png")
        print(f"  🎯 Trajectory Analysis:")
        print(f"    - trajectory_analysis.png")
        print(f"    - trajectory_interactive.html (Interactive step-by-step)")
        print(f"    - trajectory_analysis.json")
        print(f"    - trajectory_report.txt")
        print(f"  🌐 Localhost Website Deep Analysis:")
        print(f"    - website_analysis.png (12-panel localhost insights)")
        print(f"    - website_analysis.json")

        return {
            'basic_stats': stats,
            'trajectory_analysis': trajectory_analysis,
            'website_stats': website_stats
        }


def main():
    """Main function to run the analysis"""
    import argparse

    parser = argparse.ArgumentParser(description='Complete web exploration graph analysis')
    parser.add_argument('run_dir', nargs='?',
                       default='/workspace/siyuan/Go-Browse/runs/reddit_qwen_02',
                       help='Path to the run directory')

    args = parser.parse_args()

    if not os.path.exists(args.run_dir):
        print(f"Error: Run directory not found: {args.run_dir}")
        return 1

    try:
        analyzer = WebExplorationAnalyzer(args.run_dir)
        analyzer.run_complete_analysis()
        return 0

    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
