"""
具体的集成示例：如何修改现有代码以支持Agent Memory
"""

# 1. 修改 webexp/explore/core/graph.py
class Graph:
    def __init__(self, root_url: str, exp_dir: str, denylist_patterns: list, allowlist_patterns: list, memory_enabled: bool = True):
        # ... 现有初始化代码 ...
        
        # 添加memory支持
        if memory_enabled:
            from .memory import AgentMemory
            self.memory = AgentMemory(os.path.join(exp_dir, "memory"))
        else:
            self.memory = None
    
    def get_next_node(self) -> Node | None:
        """基于memory优化的节点选择"""
        if len(self.unexplored_nodes) == 0:
            logger.info("No nodes left to explore.")
            return None
        
        if self.memory is None:
            return self.unexplored_nodes[0]  # 原始逻辑
        
        # Memory-aware节点选择
        unexplored_urls = [node.url for node in self.unexplored_nodes]
        
        # 过滤过度访问的URL
        viable_urls = []
        for url in unexplored_urls:
            if not self.memory.should_skip_url(url):
                viable_urls.append(url)
        
        if not viable_urls:
            logger.warning("All unexplored URLs are overvisited, selecting least visited")
            viable_urls = unexplored_urls
        
        # 基于memory优先级排序
        prioritized_urls = self.memory.prioritize_urls(viable_urls)
        selected_url = prioritized_urls[0]
        
        # 找到对应的节点
        for node in self.unexplored_nodes:
            if node.url == selected_url:
                logger.info(f"Memory-selected next node: {node.url}")
                return node
        
        return self.unexplored_nodes[0]  # fallback

# 2. 修改 webexp/explore/algorithms/web_explore.py 中的回调函数
def memory_aware_callback(
    agent, step_num: int, goal: str, env: BrowserEnv, graph: Graph, node: Node, traj: Trajectory, obs: dict,
    reward: float, terminated: bool, truncated: bool, env_info: dict, callback_context: dict
):
    """Memory-aware回调函数，记录访问和结果"""
    if graph.memory is None:
        return step_num, obs, reward, terminated, truncated, env_info, goal, callback_context
    
    current_url = env.page.url
    
    # 记录访问
    task_success = reward > 0 if reward is not None else None
    task_name = callback_context.get('task_name', goal)
    graph.memory.record_visit(current_url, task_success, task_name)
    
    # 记录动作结果
    if 'last_action' in callback_context:
        graph.memory.record_action_result(current_url, callback_context['last_action'], task_success)
    
    # 检查是否需要跳过当前URL
    if graph.memory.should_skip_url(current_url):
        logger.info(f"URL {current_url} is overvisited, suggesting navigation away")
        callback_context['suggest_navigation'] = True
    
    return step_num, obs, reward, terminated, truncated, env_info, goal, callback_context

def enhanced_process_open_urls_callback(
    agent, step_num: int, goal: str, env: BrowserEnv, graph: Graph, node: Node, traj: Trajectory, obs: dict,
    reward: float, terminated: bool, truncated: bool, env_info: dict, callback_context: dict
):
    """增强的URL处理回调，集成memory"""
    # 原始的URL处理逻辑
    open_urls = obs['open_pages_urls']
    
    for url in open_urls:
        curr_prefix = Trace.from_trajectory_steps(
            steps=traj.steps,
            start_url=node.url,
            end_url=url,
            misc={'agent_info': agent.get_config(), 'goal': goal, 'task_misc': callback_context.get('task_misc', {})}
        )

        if graph.check_if_url_allowed(url):
            update_prefix = url != node.url
            
            # Memory增强：记录URL发现
            if graph.memory:
                graph.memory.record_visit(url, task_success=None, task_name=f"discovered_from_{node.url}")
            
            url_node = graph.get_node(url)
            if url_node:
                if update_prefix:
                    url_node.add_prefix(curr_prefix)
            else:
                # Memory增强：为新URL设置初始探索价值
                node_misc = {
                    'discovered_by': agent.get_config(), 
                    'goal': goal, 
                    'task_misc': callback_context.get('task_misc', {})
                }
                if graph.memory:
                    node_misc['initial_exploration_value'] = graph.memory.calculate_exploration_value(url)
                
                graph.add_url(
                    url=url,
                    parent=node,
                    prefixes=[curr_prefix] if update_prefix else [],
                    node_misc=node_misc
                )
            
            if url not in node.children:
                node.children.append(url)
                node.update_save(save_prefix=False, save_info=True)

    return step_num, obs, reward, terminated, truncated, env_info, goal, callback_context

# 3. 修改主探索循环
def web_explore_loop():
    # ... 现有配置加载代码 ...
    
    # 创建带memory的graph
    if config.resume_from:
        graph = Graph.load(os.path.join(config.resume_from, "graph"), load_images=False)
        # 为恢复的graph添加memory
        if not hasattr(graph, 'memory') or graph.memory is None:
            from webexp.explore.core.memory import AgentMemory
            graph.memory = AgentMemory(os.path.join(config.exp_dir, "memory"))
    else:
        graph = Graph(
            root_url=root_url,
            exp_dir=config.exp_dir,
            denylist_patterns=config_dict['denylist_patterns'], 
            allowlist_patterns=config_dict['allowlist_patterns'],
            memory_enabled=True  # 启用memory
        )
    
    # 修改agent包装，添加memory回调
    page_explorers = [
        wrap_agent_for_callback_protocol(
            AgentFactory.create_agent(**explorer['agent_factory_args']),
            pre_step_callbacks=[prestep_store_url, memory_aware_callback],
            post_step_callbacks=[backtrack_if_needed, enhanced_process_open_urls_callback],
        )
        for explorer in config_dict['page_explorers']
    ]
    
    solvers = [
        wrap_agent_for_callback_protocol(
            AgentFactory.create_agent(**solver['agent_factory_args']),
            pre_step_callbacks=[prestep_store_url, memory_aware_callback],
            post_step_callbacks=[backtrack_if_needed, enhanced_process_open_urls_callback],
        )
        for solver in config_dict['solvers']
    ]
    
    # 主探索循环
    try:
        exploration_count = 0
        curr_node = graph.get_next_node()  # 现在使用memory-aware选择
        
        while curr_node and exploration_count < config.max_nodes:
            logger.info(f"Exploring node {exploration_count + 1}/{config.max_nodes}: {curr_node.url}")
            
            # Memory统计信息
            if graph.memory:
                visit_record = graph.memory.url_visits.get(curr_node.url)
                if visit_record:
                    logger.info(f"URL visit history: count={visit_record.visit_count}, success_rate={visit_record.success_rate:.2f}")
            
            # ... 现有的exploration逻辑 ...
            
            graph.add_to_explored(curr_node)
            exploration_count += 1
            curr_node = graph.get_next_node()  # Memory-aware下一个节点选择
            
            # 定期保存memory
            if graph.memory and exploration_count % 5 == 0:
                graph.memory.save_memory()
                logger.info("Memory saved to disk")
    
    finally:
        # 最终保存memory
        if graph.memory:
            graph.memory.save_memory()
            logger.info("Final memory save completed")
        env.close()

# 4. 创建memory模块文件结构
"""
webexp/explore/core/memory.py - 主要的memory实现
webexp/explore/core/memory/
    __init__.py
    agent_memory.py - AgentMemory类
    visit_record.py - VisitRecord类
    action_pattern.py - ActionPattern类
    memory_utils.py - 工具函数
"""

# 5. 配置文件修改示例
"""
在config.yaml中添加memory配置：

memory:
  enabled: true
  max_visit_threshold: 10
  success_rate_threshold: 0.3
  retention_days: 30
  save_interval: 5  # 每5个节点保存一次
  
exploration:
  prioritization_strategy: "memory_aware"  # 或 "original"
  skip_overvisited: true
  exploration_value_weights:
    novelty: 0.5
    success_rate: 0.3
    time_decay: 0.2
"""

# 6. 监控和分析工具
class MemoryAnalyzer:
    """Memory分析工具"""
    
    def __init__(self, memory: AgentMemory):
        self.memory = memory
    
    def generate_report(self) -> dict:
        """生成memory使用报告"""
        total_urls = len(self.memory.url_visits)
        overvisited_urls = sum(1 for url in self.memory.url_visits if self.memory.should_skip_url(url))
        
        avg_visits = sum(record.visit_count for record in self.memory.url_visits.values()) / total_urls if total_urls > 0 else 0
        avg_success_rate = sum(record.success_rate for record in self.memory.url_visits.values()) / total_urls if total_urls > 0 else 0
        
        return {
            "total_urls_in_memory": total_urls,
            "overvisited_urls": overvisited_urls,
            "overvisit_rate": overvisited_urls / total_urls if total_urls > 0 else 0,
            "average_visits_per_url": avg_visits,
            "average_success_rate": avg_success_rate,
            "top_visited_urls": sorted(
                [(url, record.visit_count) for url, record in self.memory.url_visits.items()],
                key=lambda x: x[1], reverse=True
            )[:10]
        }
    
    def suggest_optimizations(self) -> List[str]:
        """建议优化措施"""
        suggestions = []
        report = self.generate_report()
        
        if report["overvisit_rate"] > 0.3:
            suggestions.append("Consider lowering max_visit_threshold")
        
        if report["average_success_rate"] < 0.2:
            suggestions.append("Review task generation strategy")
        
        if report["average_visits_per_url"] > 15:
            suggestions.append("Implement more aggressive URL filtering")
        
        return suggestions
