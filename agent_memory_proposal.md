# Agent Memory改进方案

## 问题分析
当前exploration过程中存在严重的重复访问问题：
- 291个URL中只访问了20个（6.9%效率）
- 总访问5092次，平均每URL访问17.5次
- 最高单URL访问691次，说明agent缺乏记忆机制

## 核心改进方案：分层Agent Memory系统

### 1. Global Exploration Memory (全局探索记忆)
```python
class GlobalExplorationMemory:
    def __init__(self):
        self.url_visit_history = {}  # URL -> VisitRecord
        self.failed_actions = {}     # URL -> List[FailedAction]
        self.successful_patterns = {}  # Pattern -> List[SuccessfulAction]
        self.exploration_strategy = {}  # URL -> ExplorationStrategy
        
    class VisitRecord:
        url: str
        visit_count: int
        last_visit_time: datetime
        successful_tasks: List[str]
        failed_tasks: List[str]
        discovered_elements: Set[str]
        page_type: str  # login, forum, wiki, etc.
```

### 2. Task-Specific Memory (任务特定记忆)
```python
class TaskMemory:
    def __init__(self):
        self.task_attempts = {}      # task_id -> List[AttemptRecord]
        self.element_interactions = {}  # element_id -> InteractionHistory
        self.navigation_patterns = {}   # start_url -> end_url -> List[Path]
        
    class AttemptRecord:
        task_id: str
        url: str
        actions_taken: List[str]
        success: bool
        failure_reason: str
        timestamp: datetime
```

### 3. Contextual Memory (上下文记忆)
```python
class ContextualMemory:
    def __init__(self):
        self.page_context = {}       # URL -> PageContext
        self.session_context = {}    # session_id -> SessionContext
        self.user_state = {}         # logged_in, permissions, etc.
        
    class PageContext:
        url: str
        page_elements: Dict[str, ElementInfo]
        available_actions: List[str]
        required_prerequisites: List[str]  # e.g., need login
        optimal_entry_path: List[str]
```

## 具体实现策略

### 1. Memory-Aware Action Selection
```python
def get_action_with_memory(self, obs: dict, memory: AgentMemory) -> str:
    current_url = obs['url']
    
    # 检查是否过度访问
    if memory.is_overvisited(current_url):
        return self.select_alternative_action(obs, memory)
    
    # 基于历史成功模式选择动作
    successful_patterns = memory.get_successful_patterns(current_url)
    if successful_patterns:
        return self.apply_successful_pattern(obs, successful_patterns)
    
    # 避免重复失败的动作
    failed_actions = memory.get_failed_actions(current_url)
    return self.select_novel_action(obs, exclude=failed_actions)
```

### 2. Smart URL Prioritization
```python
def prioritize_urls(self, unexplored_urls: List[str], memory: AgentMemory) -> List[str]:
    scored_urls = []
    for url in unexplored_urls:
        score = self.calculate_exploration_value(url, memory)
        scored_urls.append((url, score))
    
    return [url for url, score in sorted(scored_urls, key=lambda x: x[1], reverse=True)]

def calculate_exploration_value(self, url: str, memory: AgentMemory) -> float:
    # 基于多个因素计算探索价值
    novelty_score = 1.0 - memory.get_visit_frequency(url)
    diversity_score = memory.get_content_diversity_score(url)
    task_potential_score = memory.estimate_task_potential(url)
    
    return novelty_score * 0.4 + diversity_score * 0.3 + task_potential_score * 0.3
```

### 3. Adaptive Exploration Strategy
```python
class AdaptiveExplorationStrategy:
    def __init__(self, memory: AgentMemory):
        self.memory = memory
        self.exploration_modes = ['breadth_first', 'depth_first', 'targeted', 'random']
        
    def select_strategy(self, current_context: dict) -> str:
        # 基于当前探索状态和历史效果选择策略
        if self.memory.get_recent_success_rate() < 0.3:
            return 'targeted'  # 专注于高价值目标
        elif self.memory.get_coverage_rate() < 0.5:
            return 'breadth_first'  # 扩大覆盖范围
        else:
            return 'depth_first'  # 深入探索
```

## 实现步骤

### Phase 1: Memory Infrastructure
1. 创建AgentMemory基础类
2. 实现memory persistence机制
3. 集成到现有agent架构

### Phase 2: Smart Decision Making
1. 实现memory-aware action selection
2. 添加URL prioritization算法
3. 集成adaptive exploration strategy

### Phase 3: Optimization & Tuning
1. 添加memory compression机制
2. 优化memory查询性能
3. 调整exploration参数

## 预期效果

1. **减少重复访问**: 通过记忆机制避免无意义的重复访问
2. **提高探索效率**: 从6.9%提升到30%+的URL覆盖率
3. **智能任务分配**: 基于历史成功率优化任务执行
4. **自适应学习**: 根据探索效果动态调整策略

## 技术细节

### Memory Storage Format
```json
{
  "global_memory": {
    "url_visits": {
      "http://localhost:9999/forums": {
        "count": 691,
        "last_visit": "2024-01-15T10:30:00Z",
        "success_rate": 0.15,
        "discovered_tasks": ["browse_forums", "search_posts"],
        "failed_tasks": ["create_post", "login"]
      }
    }
  },
  "task_memory": {
    "navigation_patterns": {
      "login_sequence": ["home", "login", "dashboard"],
      "post_creation": ["forums", "new_post", "submit"]
    }
  }
}
```

### Integration Points
1. **Graph.get_next_node()**: 集成memory-based prioritization
2. **Agent.get_action()**: 添加memory consultation
3. **Trajectory.save()**: 自动更新memory records
4. **Episode.run()**: 记录exploration outcomes

这个方案通过引入分层的agent memory系统，可以显著减少重复访问，提高exploration效率，并实现自适应的探索策略。
