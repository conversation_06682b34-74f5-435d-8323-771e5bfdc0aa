"""
Intelligent Source Mapper - 基于LLM的智能源文件映射系统

这个模块使用CodebaseIndexer的结果和Qwen Coder来智能匹配URL和UI问题描述对应的源文件，
替代硬编码的假设逻辑，适用于不同框架和域名的网站。
"""

import json
import logging
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import re
from urllib.parse import urlparse

from codebase_indexer import CodebaseIndexer, CodebaseIndex, FileMetadata

logger = logging.getLogger(__name__)


@dataclass
class SourceMappingResult:
    """源文件映射结果"""
    url: str
    ui_issue_description: str
    matched_files: List[str]
    confidence_scores: Dict[str, float]  # file_path -> confidence
    reasoning: str
    total_confidence: float


class IntelligentSourceMapper:
    """基于LLM的智能源文件映射器"""
    
    def __init__(self, coder_model: str = "Qwen/Qwen2.5-Coder-32B-Instruct", 
                 coder_base_url: str = "http://localhost:8001"):
        self.coder_model = coder_model
        self.coder_base_url = coder_base_url
        self.indexer = CodebaseIndexer(coder_model, coder_base_url)

    def map_url_to_sources(self, container_name: str, url: str, ui_issue_description: str,
                          codebase_index: Optional[CodebaseIndex] = None) -> SourceMappingResult:
        """
        基于URL和UI问题描述，智能匹配相关源文件
        
        Args:
            container_name: Docker容器名称
            url: 目标URL
            ui_issue_description: UI问题描述
            codebase_index: 可选的预构建索引，如果没有提供会自动构建
        
        Returns:
            SourceMappingResult: 映射结果
        """
        logger.info(f"Mapping sources for URL: {url}")
        logger.info(f"UI issue: {ui_issue_description}")
        
        # 1. 获取或构建代码库索引
        if codebase_index is None:
            logger.info("Building codebase index...")
            codebase_index = self.indexer.index_codebase(container_name)
        
        # 2. 分析URL和问题描述，提取关键信息
        url_analysis = self._analyze_url_and_issue(url, ui_issue_description)
        logger.info(f"URL analysis: {url_analysis}")
        
        # 3. 基于关键词进行初步文件筛选
        candidate_files = self._filter_candidate_files(codebase_index, url_analysis)
        logger.info(f"Found {len(candidate_files)} candidate files")
        
        # 4. 使用Coder进行智能匹配和排序
        mapping_result = self._intelligent_file_matching(
            url, ui_issue_description, candidate_files, codebase_index, url_analysis
        )
        
        logger.info(f"Final mapping: {len(mapping_result.matched_files)} files with confidence {mapping_result.total_confidence:.2f}")
        return mapping_result

    def _analyze_url_and_issue(self, url: str, ui_issue_description: str) -> Dict[str, Any]:
        """分析URL和问题描述，提取关键信息"""
        analysis = {
            "url_path": urlparse(url).path,
            "url_params": urlparse(url).query,
            "ui_keywords": [],
            "component_types": [],
            "action_keywords": [],
            "state_keywords": []
        }
        
        # 提取UI组件关键词
        ui_keywords = [
            'dropdown', 'button', 'form', 'input', 'select', 'checkbox', 'radio',
            'modal', 'dialog', 'popup', 'tooltip', 'menu', 'navigation', 'nav',
            'tab', 'accordion', 'carousel', 'slider', 'pagination', 'breadcrumb',
            'card', 'panel', 'widget', 'sort', 'filter', 'search'
        ]
        
        issue_lower = ui_issue_description.lower()
        url_lower = url.lower()
        
        for keyword in ui_keywords:
            if keyword in issue_lower or keyword in url_lower:
                analysis["ui_keywords"].append(keyword)
        
        # 提取动作关键词
        action_keywords = [
            'click', 'select', 'toggle', 'expand', 'collapse', 'show', 'hide',
            'open', 'close', 'submit', 'cancel', 'save', 'delete', 'edit',
            'sort', 'filter', 'search', 'navigate'
        ]
        
        for keyword in action_keywords:
            if keyword in issue_lower:
                analysis["action_keywords"].append(keyword)
        
        # 提取状态关键词
        state_keywords = [
            'active', 'selected', 'expanded', 'collapsed', 'open', 'closed',
            'visible', 'hidden', 'enabled', 'disabled', 'current', 'highlighted'
        ]
        
        for keyword in state_keywords:
            if keyword in issue_lower:
                analysis["state_keywords"].append(keyword)
        
        return analysis

    def _filter_candidate_files(self, codebase_index: CodebaseIndex, url_analysis: Dict) -> List[FileMetadata]:
        """基于关键词筛选候选文件"""
        candidate_files = []
        all_keywords = (url_analysis["ui_keywords"] + 
                       url_analysis["action_keywords"] + 
                       url_analysis["state_keywords"])
        
        for file_path, metadata in codebase_index.indexed_files.items():
            score = 0
            
            # 检查UI组件匹配
            for keyword in url_analysis["ui_keywords"]:
                if keyword in metadata.ui_components:
                    score += 3  # UI组件匹配权重最高
                if keyword in metadata.functionality.lower():
                    score += 2
                if any(keyword in css_class.lower() for css_class in metadata.css_classes):
                    score += 2
            
            # 检查CSS类名匹配
            for keyword in url_analysis["state_keywords"]:
                if any(keyword in css_class.lower() for css_class in metadata.css_classes):
                    score += 2
            
            # 检查JavaScript模式匹配
            for keyword in url_analysis["action_keywords"]:
                if any(keyword in pattern.lower() for pattern in metadata.js_patterns):
                    score += 1
            
            # 检查文件路径匹配
            file_path_lower = file_path.lower()
            for keyword in all_keywords:
                if keyword in file_path_lower:
                    score += 1
            
            if score > 0:
                candidate_files.append(metadata)
        
        # 按分数排序
        candidate_files.sort(key=lambda x: self._calculate_file_score(x, url_analysis), reverse=True)
        
        # 返回前20个候选文件
        return candidate_files[:20]

    def _calculate_file_score(self, metadata: FileMetadata, url_analysis: Dict) -> float:
        """计算文件匹配分数"""
        score = 0.0
        
        # UI组件匹配
        for keyword in url_analysis["ui_keywords"]:
            if keyword in metadata.ui_components:
                score += 3.0
            if keyword in metadata.functionality.lower():
                score += 1.5
        
        # CSS类匹配
        for keyword in url_analysis["state_keywords"]:
            if any(keyword in css_class.lower() for css_class in metadata.css_classes):
                score += 2.0
        
        # JavaScript模式匹配
        for keyword in url_analysis["action_keywords"]:
            if any(keyword in pattern.lower() for pattern in metadata.js_patterns):
                score += 1.0
        
        # 文件置信度
        score *= metadata.confidence
        
        return score

    def _intelligent_file_matching(self, url: str, ui_issue_description: str,
                                  candidate_files: List[FileMetadata],
                                  codebase_index: CodebaseIndex,
                                  url_analysis: Dict) -> SourceMappingResult:
        """使用Coder进行智能文件匹配"""

        # 构建候选文件信息
        candidates_info = []
        for metadata in candidate_files:
            candidates_info.append({
                "file_path": metadata.file_path,
                "file_type": metadata.file_type,
                "functionality": metadata.functionality,
                "ui_components": metadata.ui_components,
                "css_classes": metadata.css_classes[:10],  # 限制数量
                "js_patterns": metadata.js_patterns[:5],   # 限制数量
                "confidence": metadata.confidence
            })

        # 构建分析prompt
        prompt = self._build_matching_prompt(url, ui_issue_description, candidates_info, url_analysis)

        # 调用Coder API
        matching_result = self._call_coder_api(prompt)

        if matching_result:
            return self._parse_matching_result(url, ui_issue_description, matching_result)
        else:
            # 如果API调用失败，使用基于规则的后备方案
            return self._fallback_matching(url, ui_issue_description, candidate_files)

    def _build_matching_prompt(self, url: str, ui_issue_description: str,
                              candidates_info: List[Dict], url_analysis: Dict) -> str:
        """构建智能匹配的prompt"""

        candidates_json = json.dumps(candidates_info, indent=2)

        prompt = f"""
You are an expert web developer analyzing which source files are most relevant to fix a UI issue.

**Context:**
- URL: {url}
- UI Issue: {ui_issue_description}
- Extracted Keywords: {url_analysis}

**Available Source Files:**
```json
{candidates_json}
```

**Task:**
Analyze which files are most relevant to fix the UI issue. Consider:
1. UI component types mentioned in the issue
2. CSS classes that might control the problematic behavior
3. JavaScript files that handle the component's functionality
4. File relationships and dependencies

**Response Format:**
Provide a JSON response with this structure:
{{
    "matched_files": [
        {{
            "file_path": "/path/to/file",
            "confidence": 0.95,
            "reasoning": "Why this file is relevant"
        }}
    ],
    "overall_reasoning": "Overall analysis of the issue and file selection",
    "total_confidence": 0.85
}}

**Guidelines:**
- Prioritize files that directly control the UI component mentioned in the issue
- Include both CSS/LESS files for styling and JS files for behavior
- Consider state management (expanded, active, selected classes)
- Rank files by relevance (most relevant first)
- Provide confidence scores between 0.0 and 1.0
- Include 3-8 most relevant files

Respond with ONLY the JSON object, no additional text.
"""
        return prompt.strip()

    def _call_coder_api(self, prompt: str) -> Optional[Dict]:
        """调用Coder API进行智能匹配"""
        try:
            payload = {
                "model": self.coder_model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 1500
            }

            response = requests.post(
                f"{self.coder_base_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"].strip()

                # 尝试解析JSON
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # 如果有代码块包装，尝试提取
                    json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group(1))
                    else:
                        logger.warning(f"Failed to parse Coder response: {content}")
                        return None
            else:
                logger.error(f"Coder API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error calling Coder API: {e}")
            return None

    def _parse_matching_result(self, url: str, ui_issue_description: str,
                              matching_result: Dict) -> SourceMappingResult:
        """解析Coder匹配结果"""
        matched_files = []
        confidence_scores = {}

        for file_info in matching_result.get("matched_files", []):
            file_path = file_info.get("file_path", "")
            confidence = float(file_info.get("confidence", 0.5))

            if file_path:
                matched_files.append(file_path)
                confidence_scores[file_path] = confidence

        return SourceMappingResult(
            url=url,
            ui_issue_description=ui_issue_description,
            matched_files=matched_files,
            confidence_scores=confidence_scores,
            reasoning=matching_result.get("overall_reasoning", ""),
            total_confidence=float(matching_result.get("total_confidence", 0.5))
        )

    def _fallback_matching(self, url: str, ui_issue_description: str,
                          candidate_files: List[FileMetadata]) -> SourceMappingResult:
        """基于规则的后备匹配方案"""
        logger.warning("Using fallback matching due to API failure")

        # 简单地返回前5个候选文件
        matched_files = [f.file_path for f in candidate_files[:5]]
        confidence_scores = {f.file_path: f.confidence * 0.7 for f in candidate_files[:5]}  # 降低置信度

        return SourceMappingResult(
            url=url,
            ui_issue_description=ui_issue_description,
            matched_files=matched_files,
            confidence_scores=confidence_scores,
            reasoning="Fallback matching based on keyword similarity",
            total_confidence=0.6
        )

    def save_mapping_result(self, result: SourceMappingResult, output_file: str):
        """保存映射结果到文件"""
        try:
            result_dict = {
                "url": result.url,
                "ui_issue_description": result.ui_issue_description,
                "matched_files": result.matched_files,
                "confidence_scores": result.confidence_scores,
                "reasoning": result.reasoning,
                "total_confidence": result.total_confidence
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"Mapping result saved to: {output_file}")

        except Exception as e:
            logger.error(f"Error saving mapping result: {e}")

    @classmethod
    def load_mapping_result(cls, result_file: str) -> Optional[SourceMappingResult]:
        """从文件加载映射结果"""
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                result_dict = json.load(f)

            return SourceMappingResult(
                url=result_dict["url"],
                ui_issue_description=result_dict["ui_issue_description"],
                matched_files=result_dict["matched_files"],
                confidence_scores=result_dict["confidence_scores"],
                reasoning=result_dict["reasoning"],
                total_confidence=result_dict["total_confidence"]
            )

        except Exception as e:
            logger.error(f"Error loading mapping result from {result_file}: {e}")
            return None


def main():
    """命令行入口"""
    import argparse

    parser = argparse.ArgumentParser(description="Intelligent Source Mapper - 基于LLM的智能源文件映射")
    parser.add_argument("--container", required=True, help="Docker容器名称")
    parser.add_argument("--url", required=True, help="目标URL")
    parser.add_argument("--issue", required=True, help="UI问题描述")
    parser.add_argument("--index_file", help="预构建的代码库索引文件")
    parser.add_argument("--output", help="输出映射结果文件路径")
    parser.add_argument("--coder_url", default="http://localhost:8001", help="Coder API地址")

    args = parser.parse_args()

    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建映射器
    mapper = IntelligentSourceMapper(coder_base_url=args.coder_url)

    # 加载预构建索引（如果提供）
    codebase_index = None
    if args.index_file:
        codebase_index = CodebaseIndexer.load_index(args.index_file)
        if codebase_index:
            print(f"Loaded codebase index with {len(codebase_index.indexed_files)} files")
        else:
            print("Failed to load codebase index, will build new one")

    # 执行映射
    result = mapper.map_url_to_sources(args.container, args.url, args.issue, codebase_index)

    # 输出结果
    print(f"\n=== SOURCE MAPPING RESULTS ===")
    print(f"URL: {result.url}")
    print(f"Issue: {result.ui_issue_description}")
    print(f"Total Confidence: {result.total_confidence:.2f}")
    print(f"\nMatched Files ({len(result.matched_files)}):")

    for i, file_path in enumerate(result.matched_files, 1):
        confidence = result.confidence_scores.get(file_path, 0.0)
        print(f"  {i}. {file_path} (confidence: {confidence:.2f})")

    print(f"\nReasoning: {result.reasoning}")

    # 保存结果
    if args.output:
        mapper.save_mapping_result(result, args.output)
        print(f"\nResult saved to: {args.output}")


if __name__ == "__main__":
    main()
