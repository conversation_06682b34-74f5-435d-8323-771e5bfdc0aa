"""
Directory-Based Codebase Indexer - LLM-powered codebase analysis system
(Refactored and empowered by Gemini)

This module scans specified directories within a Docker container to discover source files.
It then uses a language model to analyze functionality, UI components,
and other critical information for each discovered file. This provides a reliable,
file-system-based alternative to dynamic browser inspection.
"""

import json
import logging
import subprocess
import requests
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class FileMetadata:
    """文件元数据结构"""
    file_path: str
    file_type: str
    language: str
    functionality: str
    symbols: List[str]
    ui_components: List[str]
    css_selectors: List[str]
    imports_exports: List[str]
    dependencies: List[str]
    semantic_tags: List[str]


@dataclass
class CodebaseIndex:
    """代码库索引结构"""
    container_name: str
    indexed_files: Dict[str, FileMetadata]
    language_stats: Dict[str, int]
    symbol_map: Dict[str, List[str]]
    ui_component_map: Dict[str, List[str]]
    css_selector_map: Dict[str, List[str]]
    semantic_map: Dict[str, List[str]]
    created_at: str


class DirectoryBasedIndexer:
    """基于目录扫描的LLM代码库索引器"""

    def __init__(self, coder_model: str = "Qwen/Qwen2.5-Coder-32B-Instruct",
                 coder_base_url: str = "http://localhost:8001"):
        self.coder_model = coder_model
        self.coder_base_url = coder_base_url.rstrip('/')
        # 定义要索引的文件扩展名
        self.target_extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.less', '.html', '.php', '.py']
        # 定义要忽略的目录
        self.ignore_dirs = ['node_modules', 'dist', 'build', 'vendor', 'assets', '.git', '.vscode']

    def index_codebase(self, container_name: str, scan_dirs: List[str], output_file: Optional[str] = None) -> CodebaseIndex:
        """
        通过扫描Docker容器中的指定目录来索引代码库。
        """
        logger.info(f"🚀 Starting directory-based codebase indexing for container: {container_name}")
        logger.info(f"📂 Scanning directories: {scan_dirs}")

        if output_file:
            self._clear_previous_output(output_file)

        # 1. 通过扫描文件系统发现源文件
        source_files = self._discover_source_files_by_scanning(container_name, scan_dirs)
        if not source_files:
            logger.error("🚫 No source files discovered in the specified directories. Halting indexing.")
            return CodebaseIndex(container_name, {}, {}, {}, {}, {}, {}, datetime.now().isoformat())

        logger.info(f"✅ Found {len(source_files)} source files to analyze.")
        self._print_directory_summary(source_files)

        # 2. 初始化输出文件以进行增量更新
        if output_file:
            self._initialize_output_file(output_file, container_name)

        # 3. 分析每个文件
        indexed_files, lang_stats, sym_map, ui_map, css_map, sem_map = {}, {}, {}, {}, {}, {}

        for i, file_path in enumerate(source_files):
            progress = f"{i+1}/{len(source_files)}"
            logger.info(f"[{progress}] Analyzing: {file_path}")
            print(f"Progress: [{progress}] - {file_path}")

            try:
                metadata = self._analyze_file(container_name, file_path)
                if metadata:
                    indexed_files[file_path] = metadata
                    # 更新所有统计信息和映射
                    lang_stats[metadata.language] = lang_stats.get(metadata.language, 0) + 1
                    for symbol in metadata.symbols:
                        sym_map.setdefault(symbol, []).append(file_path)
                    for component in metadata.ui_components:
                        ui_map.setdefault(component, []).append(file_path)
                    for selector in metadata.css_selectors:
                        css_map.setdefault(selector, []).append(file_path)
                    for tag in metadata.semantic_tags:
                        sem_map.setdefault(tag, []).append(file_path)

                    if output_file:
                        self._append_file_result(output_file, file_path, metadata)

            except Exception as e:
                logger.warning(f"⚠️ Failed to analyze {file_path}: {e}")
                continue

        # 4. 构建并最终确定完整的索引
        index = CodebaseIndex(
            container_name=container_name,
            indexed_files=indexed_files,
            language_stats=lang_stats,
            symbol_map=sym_map,
            ui_component_map=ui_map,
            css_selector_map=css_map,
            semantic_map=sem_map,
            created_at=datetime.now().isoformat()
        )

        if output_file:
            self._finalize_output_file(output_file, index)

        logger.info(f"🎉 Codebase indexing completed. Indexed {len(indexed_files)} files.")
        return index

    def _discover_source_files_by_scanning(self, container_name: str, scan_dirs: List[str]) -> List[str]:
        """
        直接扫描 /var/www/html 目录，只查找 CSS/LESS 文件
        """
        logger.info("🔎 Discovering CSS/LESS files in /var/www/html...")
        all_files: Set[str] = set()

        # 直接扫描 /var/www/html，只查找 CSS/LESS 文件
        directory = "/var/www/html"

        # 确保目录存在于容器中
        check_cmd = f"docker exec {container_name} test -d {directory}"
        if subprocess.run(check_cmd, shell=True, capture_output=True).returncode != 0:
            logger.warning(f"Directory '{directory}' does not exist in container '{container_name}'.")
            return []

        # 构建 find 命令，只查找 CSS 和 LESS 文件
        cmd = f"docker exec {container_name} find {directory} -type f \\( -name '*.css' -o -name '*.less' \\) -print"
        logger.info(f"Executing find command: {cmd}")

        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            if result.returncode == 0 and result.stdout:
                found_files = result.stdout.strip().split('\n')
                all_files.update(f for f in found_files if f) # 过滤掉空行
            elif result.returncode != 0:
                logger.error(f"Error scanning directory {directory}: {result.stderr}")
        except subprocess.TimeoutExpired:
            logger.error(f"Timeout expired while scanning directory: {directory}")
        except Exception as e:
            logger.error(f"An unexpected error occurred during scan: {e}")

        logger.info(f"Found {len(all_files)} CSS/LESS files")
        return sorted(list(all_files))

    # --- LLM分析和文件I/O方法 (与之前版本基本相同) ---

    def _analyze_file(self, container_name: str, file_path: str) -> Optional[FileMetadata]:
        """使用Coder LLM分析单个文件"""
        try:
            file_content = self._read_file_content(container_name, file_path)
            if not file_content or not file_content.strip():
                return None
            
            # 限制文件大小
            if len(file_content) > 20000:
                file_content = file_content[:20000] + "\n... [truncated]"

            prompt = self._build_analysis_prompt(file_path, file_content)
            analysis_result = self._call_coder_api(prompt)
            return self._parse_analysis_result(file_path, analysis_result) if analysis_result else None

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return None

    def _read_file_content(self, container_name: str, file_path: str) -> Optional[str]:
        """从Docker容器中读取文件内容"""
        try:
            cmd = f"docker exec {container_name} cat '{file_path}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            return result.stdout if result.returncode == 0 else None
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None

    def _build_analysis_prompt(self, file_path: str, file_content: str) -> str:
        """为Coder LLM构建分析提示"""
        return f"""
Analyze this source file and extract structured information about its functionality, symbols, and patterns.

File Path: {file_path}
File Content:
```
{file_content}
```

Provide a JSON response with the following structure. Be comprehensive but precise.
{{
    "file_type": "css|js|php|html|json|etc",
    "language": "javascript|css|php|html|python|etc",
    "functionality": "Brief description of what this file does",
    "symbols": ["function_names", "class_names", "variable_names"],
    "ui_components": ["dropdown", "button", "form", "modal"],
    "css_selectors": [".class-names", "#ids"],
    "imports_exports": ["imported_modules", "exported_symbols"],
    "dependencies": ["referenced_files", "external_libraries"],
    "semantic_tags": ["authentication", "navigation", "ui-component"]
}}

Respond with ONLY the JSON object, with no additional text or code block wrapping.
"""

    def _call_coder_api(self, prompt: str) -> Optional[Dict]:
        """调用Coder API进行分析"""
        try:
            payload = {
                "model": self.coder_model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.0,
                "max_tokens": 2048,
                "response_format": {"type": "json_object"}
            }
            response = requests.post(f"{self.coder_base_url}/v1/chat/completions", json=payload, timeout=45)
            response.raise_for_status()
            content = response.json()["choices"][0]["message"]["content"].strip()
            if content.startswith("```json"):
                content = content[7:-3].strip()
            return json.loads(content)
        except Exception as e:
            logger.error(f"Coder API call failed: {e}")
            return None

    def _parse_analysis_result(self, file_path: str, res: Dict) -> Optional[FileMetadata]:
        """将Coder分析结果解析为FileMetadata数据类"""
        try:
            return FileMetadata(
                file_path=file_path,
                file_type=res.get("file_type", "unknown"),
                language=res.get("language", "unknown"),
                functionality=res.get("functionality", ""),
                symbols=res.get("symbols", []),
                ui_components=res.get("ui_components", []),
                css_selectors=res.get("css_selectors", []),
                imports_exports=res.get("imports_exports", []),
                dependencies=res.get("dependencies", []),
                semantic_tags=res.get("semantic_tags", [])
            )
        except Exception as e:
            logger.error(f"Error parsing analysis result for {file_path}: {e}")
            return None

    # --- 实用程序和文件I/O方法 ---
    def _clear_previous_output(self, output_file: str):
        if os.path.exists(output_file):
            os.remove(output_file)

    def _print_directory_summary(self, source_files: List[str]):
        dirs = {}
        for file in source_files:
            dir_path = str(Path(file).parent)
            dirs[dir_path] = dirs.get(dir_path, 0) + 1
        print("\n=== Directory Summary ===")
        for directory, count in sorted(dirs.items()):
            print(f"{directory}: {count} files")
        print("========================\n")

    def _initialize_output_file(self, output_file: str, container_name: str):
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        initial_data = {
            "container_name": container_name,
            "created_at": datetime.now().isoformat(),
            "status": "in_progress",
            "indexed_files": {}
        }
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2)

    def _append_file_result(self, output_file: str, file_path: str, metadata: FileMetadata):
        try:
            with open(output_file, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data["indexed_files"][file_path] = asdict(metadata)
                f.seek(0)
                json.dump(data, f, indent=2)
                f.truncate()
        except Exception as e:
            logger.warning(f"Error appending to output file: {e}")
            
    def _finalize_output_file(self, output_file: str, index: CodebaseIndex):
        """用完整的索引数据完成输出文件"""
        try:
            index_dict = {
                "container_name": index.container_name,
                "indexed_files": {k: asdict(v) for k, v in index.indexed_files.items()},
                "language_stats": index.language_stats,
                "symbol_map": index.symbol_map,
                "ui_component_map": index.ui_component_map,
                "css_selector_map": index.css_selector_map,
                "semantic_map": index.semantic_map,
                "created_at": index.created_at,
                "status": "completed"
            }
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(index_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"Finalized index saved to: {output_file}")
        except Exception as e:
            logger.error(f"Error finalizing output file: {e}")


def main():
    """命令行入口点"""
    import argparse

    parser = argparse.ArgumentParser(description="Directory-Based Codebase Indexer")
    parser.add_argument("--container", required=True, help="要索引的Docker容器名称")
    parser.add_argument("--scan_dirs", required=True, nargs='+', help="要在容器内扫描的目录列表 (例如 /app /var/www)")
    parser.add_argument("--output", help="输出索引文件的路径 (默认为 dir_index_<container>.json)")
    parser.add_argument("--coder_url", default="http://localhost:8001", help="Coder LLM API端点的URL")
    
    args = parser.parse_args()

    # 设置日志记录
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建索引器
    indexer = DirectoryBasedIndexer(coder_base_url=args.coder_url)

    # 执行索引
    output_file = args.output or f"dir_index_{args.container}.json"
    index = indexer.index_codebase(
        container_name=args.container,
        scan_dirs=args.scan_dirs,
        output_file=output_file,
    )

    if index.indexed_files:
        print(f"\n索引完成！分析了 {len(index.indexed_files)} 个文件。")
        print(f"发现的语言: {list(index.language_stats.keys())}")
        print(f"索引已保存至: {output_file}")
    else:
        print("\n索引完成，但未分析任何文件。请检查日志以获取错误信息。")

if __name__ == "__main__":
    main()
