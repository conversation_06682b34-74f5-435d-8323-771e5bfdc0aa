"""
Web Agent Revision System

This module provides tools for analyzing negative trajectories from web agents,
identifying UI design issues, and generating code fixes to improve website usability.

Main components:
- DesignJudge: Analyzes UI issues using multimodal models
- CodebaseIndexer: Builds intelligent codebase indexes
- IntelligentSourceMapper: Maps issues to relevant source files
- DynamicSelectorAnalyzer: Analyzes CSS selectors and JavaScript patterns
- RevisionCoder: Generates code fixes using LLMs
- DockerCodeApplier: Applies fixes to Docker containers
- RevisionPipeline: Orchestrates the complete revision workflow
"""

from .design_judge import DesignJudge
from .codebase_indexer import CodebaseIndexer, CodebaseIndex, FileMetadata
from .intelligent_source_mapper import IntelligentSourceMapper, SourceMappingResult
from .dynamic_selector_analyzer import DynamicSelectorAnalyzer, SelectorAnalysisResult
from .revision_coder import DesignCoder, QwenCoderClient
from .docker_code_applier import DockerCodeApplier
from .perform_revision import RevisionPipeline

__all__ = [
    'DesignJudge',
    'CodebaseIndexer', 'CodebaseIndex', 'FileMetadata',
    'IntelligentSourceMapper', 'SourceMappingResult',
    'DynamicSelectorAnalyzer', 'SelectorAnalysisResult',
    'DesignCoder', 'QwenCoderClient',
    'DockerCodeApplier',
    'RevisionPipeline'
]
