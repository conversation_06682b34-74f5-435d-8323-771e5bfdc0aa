"""
Dynamic Selector Analyzer - 动态CSS选择器发现和分析系统

这个模块使用Qwen Coder来动态发现目标元素的CSS选择器模式，
分析JavaScript状态管理，生成最佳选择器策略，替代硬编码的假设。
"""

import json
import logging
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import re
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


@dataclass
class SelectorStrategy:
    """选择器策略"""
    primary_selector: str
    fallback_selectors: List[str]
    state_selectors: Dict[str, str]  # state_name -> selector
    confidence: float
    reasoning: str


@dataclass
class ElementAnalysis:
    """元素分析结果"""
    element_type: str
    element_classes: List[str]
    element_attributes: Dict[str, str]
    parent_context: str
    sibling_context: str
    state_management_type: str  # 'css_class', 'aria_attribute', 'data_attribute', 'mixed'
    state_patterns: List[str]
    javascript_handlers: List[str]


@dataclass
class SelectorAnalysisResult:
    """选择器分析结果"""
    target_element: str
    element_analysis: ElementAnalysis
    selector_strategy: SelectorStrategy
    javascript_analysis: Dict[str, Any]
    css_analysis: Dict[str, Any]
    recommendations: List[str]


class DynamicSelectorAnalyzer:
    """动态CSS选择器分析器"""
    
    def __init__(self, coder_model: str = "Qwen/Qwen2.5-Coder-32B-Instruct", 
                 coder_base_url: str = "http://localhost:8001"):
        self.coder_model = coder_model
        self.coder_base_url = coder_base_url

    def analyze_element_selectors(self, container_name: str, url: str, 
                                 target_element_description: str,
                                 html_content: Optional[str] = None,
                                 relevant_js_files: Optional[List[str]] = None,
                                 relevant_css_files: Optional[List[str]] = None) -> SelectorAnalysisResult:
        """
        分析目标元素的选择器策略
        
        Args:
            container_name: Docker容器名称
            url: 目标URL
            target_element_description: 目标元素描述
            html_content: 可选的HTML内容
            relevant_js_files: 相关JavaScript文件列表
            relevant_css_files: 相关CSS文件列表
        
        Returns:
            SelectorAnalysisResult: 分析结果
        """
        logger.info(f"Analyzing selectors for element: {target_element_description}")
        
        # 1. 获取HTML内容（如果没有提供）
        if html_content is None:
            html_content = self._fetch_html_content(container_name, url)
        
        # 2. 分析HTML结构，找到目标元素
        element_analysis = self._analyze_html_structure(html_content, target_element_description)
        
        # 3. 分析相关JavaScript文件
        js_analysis = self._analyze_javascript_files(container_name, relevant_js_files or [], element_analysis)
        
        # 4. 分析相关CSS文件
        css_analysis = self._analyze_css_files(container_name, relevant_css_files or [], element_analysis)
        
        # 5. 生成选择器策略
        selector_strategy = self._generate_selector_strategy(
            element_analysis, js_analysis, css_analysis, target_element_description
        )
        
        # 6. 生成建议
        recommendations = self._generate_recommendations(element_analysis, js_analysis, css_analysis)
        
        return SelectorAnalysisResult(
            target_element=target_element_description,
            element_analysis=element_analysis,
            selector_strategy=selector_strategy,
            javascript_analysis=js_analysis,
            css_analysis=css_analysis,
            recommendations=recommendations
        )

    def _fetch_html_content(self, container_name: str, url: str) -> str:
        """获取URL的HTML内容"""
        try:
            # 使用curl获取HTML内容
            cmd = f"docker exec {container_name} curl -s '{url}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return result.stdout
            else:
                logger.warning(f"Failed to fetch HTML from {url}: {result.stderr}")
                return ""
                
        except Exception as e:
            logger.error(f"Error fetching HTML content: {e}")
            return ""

    def _analyze_html_structure(self, html_content: str, target_description: str) -> ElementAnalysis:
        """分析HTML结构，找到目标元素"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 使用Coder分析HTML结构和目标元素
            analysis_prompt = self._build_html_analysis_prompt(html_content, target_description)
            analysis_result = self._call_coder_api(analysis_prompt)
            
            if analysis_result:
                return self._parse_element_analysis(analysis_result)
            else:
                # 后备方案：基于关键词的简单分析
                return self._fallback_element_analysis(soup, target_description)
                
        except Exception as e:
            logger.error(f"Error analyzing HTML structure: {e}")
            return self._create_default_element_analysis()

    def _build_html_analysis_prompt(self, html_content: str, target_description: str) -> str:
        """构建HTML分析prompt"""
        # 限制HTML内容长度
        if len(html_content) > 15000:
            html_content = html_content[:15000] + "\n... [truncated]"
        
        prompt = f"""
Analyze this HTML content to find and understand the target element described below.

**Target Element Description:** {target_description}

**HTML Content:**
```html
{html_content}
```

**Task:**
Find the target element and analyze its structure, context, and potential state management patterns.

**Response Format:**
Provide a JSON response with this structure:
{{
    "element_type": "button|select|div|etc",
    "element_classes": ["list", "of", "css", "classes"],
    "element_attributes": {{"attr": "value"}},
    "parent_context": "description of parent elements",
    "sibling_context": "description of sibling elements",
    "state_management_type": "css_class|aria_attribute|data_attribute|mixed",
    "state_patterns": ["list", "of", "state", "patterns"],
    "javascript_handlers": ["list", "of", "potential", "js", "handlers"]
}}

**Focus on:**
1. Exact element type and CSS classes
2. ARIA attributes and data attributes
3. Parent-child relationships
4. State management patterns (expanded, active, selected, etc.)
5. Potential JavaScript event handlers

Respond with ONLY the JSON object, no additional text.
"""
        return prompt.strip()

    def _call_coder_api(self, prompt: str) -> Optional[Dict]:
        """调用Coder API"""
        try:
            payload = {
                "model": self.coder_model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 1000
            }
            
            response = requests.post(
                f"{self.coder_base_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"].strip()
                
                # 尝试解析JSON
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # 如果有代码块包装，尝试提取
                    json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group(1))
                    else:
                        logger.warning(f"Failed to parse Coder response: {content}")
                        return None
            else:
                logger.error(f"Coder API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error calling Coder API: {e}")
            return None

    def _parse_element_analysis(self, analysis_result: Dict) -> ElementAnalysis:
        """解析元素分析结果"""
        return ElementAnalysis(
            element_type=analysis_result.get("element_type", "unknown"),
            element_classes=analysis_result.get("element_classes", []),
            element_attributes=analysis_result.get("element_attributes", {}),
            parent_context=analysis_result.get("parent_context", ""),
            sibling_context=analysis_result.get("sibling_context", ""),
            state_management_type=analysis_result.get("state_management_type", "unknown"),
            state_patterns=analysis_result.get("state_patterns", []),
            javascript_handlers=analysis_result.get("javascript_handlers", [])
        )

    def _fallback_element_analysis(self, soup: BeautifulSoup, target_description: str) -> ElementAnalysis:
        """后备的元素分析方案"""
        # 基于关键词查找元素
        keywords = target_description.lower().split()
        
        # 查找可能的元素
        possible_elements = []
        
        for keyword in keywords:
            # 查找包含关键词的元素
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for element in elements:
                if element.parent:
                    possible_elements.append(element.parent)
        
        if possible_elements:
            # 使用第一个找到的元素
            element = possible_elements[0]
            return ElementAnalysis(
                element_type=element.name or "unknown",
                element_classes=element.get('class', []),
                element_attributes=dict(element.attrs),
                parent_context=str(element.parent.name) if element.parent else "",
                sibling_context="",
                state_management_type="unknown",
                state_patterns=[],
                javascript_handlers=[]
            )
        
        return self._create_default_element_analysis()

    def _create_default_element_analysis(self) -> ElementAnalysis:
        """创建默认的元素分析"""
        return ElementAnalysis(
            element_type="unknown",
            element_classes=[],
            element_attributes={},
            parent_context="",
            sibling_context="",
            state_management_type="unknown",
            state_patterns=[],
            javascript_handlers=[]
        )

    def _analyze_javascript_files(self, container_name: str, js_files: List[str],
                                 element_analysis: ElementAnalysis) -> Dict[str, Any]:
        """分析JavaScript文件中的状态管理模式"""
        js_analysis = {
            "state_management_patterns": [],
            "event_handlers": [],
            "css_class_operations": [],
            "dom_manipulations": [],
            "state_classes": []
        }

        try:
            for js_file in js_files:
                logger.info(f"Analyzing JavaScript file: {js_file}")

                # 读取JavaScript文件内容
                cmd = f"docker exec {container_name} cat {js_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    js_content = result.stdout

                    # 使用Coder分析JavaScript状态管理
                    js_prompt = self._build_js_analysis_prompt(js_content, element_analysis)
                    js_result = self._call_coder_api(js_prompt)

                    if js_result:
                        # 合并分析结果
                        for key in js_analysis.keys():
                            if key in js_result:
                                js_analysis[key].extend(js_result[key])

        except Exception as e:
            logger.error(f"Error analyzing JavaScript files: {e}")

        return js_analysis

    def _build_js_analysis_prompt(self, js_content: str, element_analysis: ElementAnalysis) -> str:
        """构建JavaScript分析prompt"""
        # 限制内容长度
        if len(js_content) > 10000:
            js_content = js_content[:10000] + "\n... [truncated]"

        element_classes = element_analysis.element_classes

        prompt = f"""
Analyze this JavaScript code to understand how it manages the state of UI elements, particularly focusing on the target element.

**Target Element Info:**
- Type: {element_analysis.element_type}
- Classes: {element_classes}
- Attributes: {element_analysis.element_attributes}

**JavaScript Code:**
```javascript
{js_content}
```

**Task:**
Analyze the JavaScript code and identify state management patterns, especially for the target element.

**Response Format:**
Provide a JSON response with this structure:
{{
    "state_management_patterns": ["pattern1", "pattern2"],
    "event_handlers": ["click", "keydown", "etc"],
    "css_class_operations": ["classList.add('class')", "classList.toggle('class')"],
    "dom_manipulations": ["setAttribute", "removeAttribute", "etc"],
    "state_classes": ["expanded", "active", "selected", "etc"]
}}

**Focus on:**
1. CSS class additions/removals/toggles
2. Attribute manipulations (aria-expanded, data-*, etc.)
3. Event handlers that change element state
4. State management patterns (expanded, active, selected, etc.)
5. DOM manipulations related to the target element

Respond with ONLY the JSON object, no additional text.
"""
        return prompt.strip()

    def _analyze_css_files(self, container_name: str, css_files: List[str],
                          element_analysis: ElementAnalysis) -> Dict[str, Any]:
        """分析CSS文件中的选择器模式"""
        css_analysis = {
            "relevant_selectors": [],
            "state_selectors": [],
            "pseudo_selectors": [],
            "attribute_selectors": [],
            "class_selectors": []
        }

        try:
            for css_file in css_files:
                logger.info(f"Analyzing CSS file: {css_file}")

                # 读取CSS文件内容
                cmd = f"docker exec {container_name} cat {css_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    css_content = result.stdout

                    # 使用Coder分析CSS选择器
                    css_prompt = self._build_css_analysis_prompt(css_content, element_analysis)
                    css_result = self._call_coder_api(css_prompt)

                    if css_result:
                        # 合并分析结果
                        for key in css_analysis.keys():
                            if key in css_result:
                                css_analysis[key].extend(css_result[key])

        except Exception as e:
            logger.error(f"Error analyzing CSS files: {e}")

        return css_analysis

    def _build_css_analysis_prompt(self, css_content: str, element_analysis: ElementAnalysis) -> str:
        """构建CSS分析prompt"""
        # 限制内容长度
        if len(css_content) > 10000:
            css_content = css_content[:10000] + "\n... [truncated]"

        element_classes = element_analysis.element_classes

        prompt = f"""
Analyze this CSS/LESS code to understand the selectors and styling patterns for the target element.

**Target Element Info:**
- Type: {element_analysis.element_type}
- Classes: {element_classes}
- Attributes: {element_analysis.element_attributes}

**CSS/LESS Code:**
```css
{css_content}
```

**Task:**
Analyze the CSS code and identify relevant selectors, especially state-related selectors.

**Response Format:**
Provide a JSON response with this structure:
{{
    "relevant_selectors": [".class", "#id", "element", "etc"],
    "state_selectors": [".class--expanded", ".class.active", "etc"],
    "pseudo_selectors": [":hover", ":focus", ":active", "etc"],
    "attribute_selectors": ["[aria-expanded='true']", "[data-state='open']", "etc"],
    "class_selectors": [".dropdown", ".button", ".active", "etc"]
}}

**Focus on:**
1. Selectors that match the target element classes
2. State-related selectors (expanded, active, selected, etc.)
3. Attribute selectors (aria-*, data-*, etc.)
4. Pseudo-selectors for interactive states
5. BEM-style modifiers (--expanded, --active, etc.)

Respond with ONLY the JSON object, no additional text.
"""
        return prompt.strip()

    def _generate_selector_strategy(self, element_analysis: ElementAnalysis,
                                   js_analysis: Dict, css_analysis: Dict,
                                   target_description: str) -> SelectorStrategy:
        """生成选择器策略"""

        # 使用Coder生成最佳选择器策略
        strategy_prompt = self._build_strategy_prompt(element_analysis, js_analysis, css_analysis, target_description)
        strategy_result = self._call_coder_api(strategy_prompt)

        if strategy_result:
            return SelectorStrategy(
                primary_selector=strategy_result.get("primary_selector", ""),
                fallback_selectors=strategy_result.get("fallback_selectors", []),
                state_selectors=strategy_result.get("state_selectors", {}),
                confidence=float(strategy_result.get("confidence", 0.5)),
                reasoning=strategy_result.get("reasoning", "")
            )
        else:
            # 后备策略生成
            return self._fallback_selector_strategy(element_analysis, js_analysis, css_analysis)

    def _build_strategy_prompt(self, element_analysis: ElementAnalysis,
                              js_analysis: Dict, css_analysis: Dict,
                              target_description: str) -> str:
        """构建策略生成prompt"""

        prompt = f"""
Based on the analysis results, generate the best CSS selector strategy for targeting and styling the element.

**Target Element:** {target_description}

**Element Analysis:**
- Type: {element_analysis.element_type}
- Classes: {element_analysis.element_classes}
- Attributes: {element_analysis.element_attributes}
- State Management: {element_analysis.state_management_type}
- State Patterns: {element_analysis.state_patterns}

**JavaScript Analysis:**
- State Classes: {js_analysis.get('state_classes', [])}
- CSS Class Operations: {js_analysis.get('css_class_operations', [])}
- Event Handlers: {js_analysis.get('event_handlers', [])}

**CSS Analysis:**
- State Selectors: {css_analysis.get('state_selectors', [])}
- Relevant Selectors: {css_analysis.get('relevant_selectors', [])}
- Attribute Selectors: {css_analysis.get('attribute_selectors', [])}

**Task:**
Generate the optimal CSS selector strategy for styling this element, especially for different states.

**Response Format:**
Provide a JSON response with this structure:
{{
    "primary_selector": ".best-selector-for-element",
    "fallback_selectors": [".fallback1", ".fallback2"],
    "state_selectors": {{
        "expanded": ".element--expanded",
        "active": ".element.active",
        "selected": ".element[aria-selected='true']"
    }},
    "confidence": 0.95,
    "reasoning": "Explanation of the selector strategy"
}}

**Guidelines:**
1. Choose selectors that work with the identified state management pattern
2. Prefer CSS class-based selectors if JavaScript uses classList operations
3. Use attribute selectors if JavaScript manages ARIA attributes
4. Provide fallback selectors for robustness
5. Consider specificity and maintainability
6. Match the existing CSS patterns found in the codebase

Respond with ONLY the JSON object, no additional text.
"""
        return prompt.strip()

    def _fallback_selector_strategy(self, element_analysis: ElementAnalysis,
                                   js_analysis: Dict, css_analysis: Dict) -> SelectorStrategy:
        """后备选择器策略生成"""

        # 基于分析结果生成基本策略
        primary_selector = ""
        fallback_selectors = []
        state_selectors = {}

        # 构建主选择器
        if element_analysis.element_classes:
            primary_selector = "." + element_analysis.element_classes[0]
            fallback_selectors = ["." + cls for cls in element_analysis.element_classes[1:3]]
        elif element_analysis.element_type:
            primary_selector = element_analysis.element_type

        # 构建状态选择器
        state_classes = js_analysis.get('state_classes', [])
        for state in state_classes:
            if element_analysis.element_classes:
                base_class = element_analysis.element_classes[0]
                state_selectors[state] = f".{base_class}--{state}"

        return SelectorStrategy(
            primary_selector=primary_selector,
            fallback_selectors=fallback_selectors,
            state_selectors=state_selectors,
            confidence=0.6,
            reasoning="Fallback strategy based on element analysis"
        )

    def _generate_recommendations(self, element_analysis: ElementAnalysis,
                                 js_analysis: Dict, css_analysis: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于状态管理类型的建议
        if element_analysis.state_management_type == "css_class":
            recommendations.append("Use CSS class-based selectors for state management")
            recommendations.append("Ensure CSS classes are toggled consistently in JavaScript")
        elif element_analysis.state_management_type == "aria_attribute":
            recommendations.append("Use attribute selectors for ARIA-based state management")
            recommendations.append("Consider accessibility implications of state changes")
        elif element_analysis.state_management_type == "mixed":
            recommendations.append("Consider standardizing on one state management approach")
            recommendations.append("Ensure CSS and JavaScript state management are synchronized")

        # 基于JavaScript分析的建议
        if js_analysis.get('css_class_operations'):
            recommendations.append("CSS modifications should target the classes managed by JavaScript")

        # 基于CSS分析的建议
        if css_analysis.get('state_selectors'):
            recommendations.append("Leverage existing state selectors in the codebase")

        return recommendations

    def save_analysis_result(self, result: SelectorAnalysisResult, output_file: str):
        """保存分析结果到文件"""
        try:
            result_dict = {
                "target_element": result.target_element,
                "element_analysis": {
                    "element_type": result.element_analysis.element_type,
                    "element_classes": result.element_analysis.element_classes,
                    "element_attributes": result.element_analysis.element_attributes,
                    "parent_context": result.element_analysis.parent_context,
                    "sibling_context": result.element_analysis.sibling_context,
                    "state_management_type": result.element_analysis.state_management_type,
                    "state_patterns": result.element_analysis.state_patterns,
                    "javascript_handlers": result.element_analysis.javascript_handlers
                },
                "selector_strategy": {
                    "primary_selector": result.selector_strategy.primary_selector,
                    "fallback_selectors": result.selector_strategy.fallback_selectors,
                    "state_selectors": result.selector_strategy.state_selectors,
                    "confidence": result.selector_strategy.confidence,
                    "reasoning": result.selector_strategy.reasoning
                },
                "javascript_analysis": result.javascript_analysis,
                "css_analysis": result.css_analysis,
                "recommendations": result.recommendations
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"Analysis result saved to: {output_file}")

        except Exception as e:
            logger.error(f"Error saving analysis result: {e}")


def main():
    """命令行入口"""
    import argparse

    parser = argparse.ArgumentParser(description="Dynamic Selector Analyzer - 动态CSS选择器分析")
    parser.add_argument("--container", required=True, help="Docker容器名称")
    parser.add_argument("--url", required=True, help="目标URL")
    parser.add_argument("--element", required=True, help="目标元素描述")
    parser.add_argument("--js_files", nargs="*", help="相关JavaScript文件列表")
    parser.add_argument("--css_files", nargs="*", help="相关CSS文件列表")
    parser.add_argument("--output", help="输出分析结果文件路径")
    parser.add_argument("--coder_url", default="http://localhost:8001", help="Coder API地址")

    args = parser.parse_args()

    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建分析器
    analyzer = DynamicSelectorAnalyzer(coder_base_url=args.coder_url)

    # 执行分析
    result = analyzer.analyze_element_selectors(
        args.container, args.url, args.element,
        relevant_js_files=args.js_files,
        relevant_css_files=args.css_files
    )

    # 输出结果
    print(f"\n=== SELECTOR ANALYSIS RESULTS ===")
    print(f"Target Element: {result.target_element}")
    print(f"Element Type: {result.element_analysis.element_type}")
    print(f"Element Classes: {result.element_analysis.element_classes}")
    print(f"State Management: {result.element_analysis.state_management_type}")

    print(f"\n=== SELECTOR STRATEGY ===")
    print(f"Primary Selector: {result.selector_strategy.primary_selector}")
    print(f"Fallback Selectors: {result.selector_strategy.fallback_selectors}")
    print(f"State Selectors: {result.selector_strategy.state_selectors}")
    print(f"Confidence: {result.selector_strategy.confidence:.2f}")
    print(f"Reasoning: {result.selector_strategy.reasoning}")

    print(f"\n=== RECOMMENDATIONS ===")
    for i, rec in enumerate(result.recommendations, 1):
        print(f"{i}. {rec}")

    # 保存结果
    if args.output:
        analyzer.save_analysis_result(result, args.output)
        print(f"\nResult saved to: {args.output}")


if __name__ == "__main__":
    main()
