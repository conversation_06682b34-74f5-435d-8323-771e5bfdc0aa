#!/usr/bin/env python3
"""
Design Coder - Generates code modifications based on design feedback.

This module takes design feedback from design_judge.py and generates actual code
modifications using a coder LLM to fix UI/UX issues.
"""

import os
import json
import glob
import logging
import argparse
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any
from textwrap import dedent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QwenCoderClient:
    """Client for Qwen Coder model via vLLM API."""
    
    def __init__(self, base_url: str = "http://localhost:8001", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json"
        }
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
    
    def generate_code_fix(self, prompt: str, max_tokens: int = 2000) -> Dict[str, Any]:
        """Generate code fix using Qwen Coder model."""
        try:
            payload = {
                "model": "Qwen/Qwen2.5-Coder-32B-Instruct",
                "messages": [
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": 0.1,
                "top_p": 0.9
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "content": result["choices"][0]["message"]["content"],
                    "usage": result.get("usage", {})
                }
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return {"success": False, "error": f"API error: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Error calling Qwen Coder API: {e}")
            return {"success": False, "error": str(e)}


class DesignCoder:
    """Main class for generating code fixes from design feedback."""
    
    def __init__(self, base_url: str = "http://localhost:8001", api_key: str = None):
        self.client = QwenCoderClient(base_url, api_key)
    
    def process_design_feedback(self, feedback: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a single design feedback and generate code fixes."""
        if not feedback.get("is_ui_design_issue", False):
            logger.debug("Skipping non-UI design issue")
            return None

        # Extract key information
        problematic_element = feedback.get("problematic_element", "")
        primary_reason = feedback.get("primary_reason", "")
        primary_improvement = feedback.get("primary_improvement", "")

        # Support both old format (code_snippets) and new format (code_segments)
        code_segments = feedback.get("code_segments", [])
        code_snippets = feedback.get("code_snippets", [])

        source_files = feedback.get("source_files", [])
        url = feedback.get("url", "")
        html_analysis = feedback.get("html_analysis", {})

        # Use code_segments if available, otherwise fall back to code_snippets
        if code_segments:
            logger.info(f"Processing {len(code_segments)} code segments")
            code_fixes = []
            for segment in code_segments:
                fix = self._generate_code_fix_for_segment(
                    segment, problematic_element, primary_reason, primary_improvement,
                    url, html_analysis
                )
                if fix:
                    code_fixes.append(fix)
        elif code_snippets:
            logger.info(f"Processing {len(code_snippets)} code snippets (legacy format)")
            code_fixes = []
            for snippet in code_snippets:
                fix = self._generate_code_fix_for_snippet(
                    snippet, problematic_element, primary_reason, primary_improvement,
                    url, html_analysis, source_files
                )
                if fix:
                    code_fixes.append(fix)
        else:
            logger.debug("No code segments or snippets available for modification")
            return None

        if not code_fixes:
            return None

        return {
            "problematic_element": problematic_element,
            "primary_reason": primary_reason,
            "primary_improvement": primary_improvement,
            "url": url,
            "html_analysis": html_analysis,
            "code_fixes": code_fixes,
            "source_files": source_files
        }

    def _generate_code_fix_for_segment(self, segment: Dict[str, Any],
                                     problematic_element: str,
                                     primary_reason: str,
                                     primary_improvement: str,
                                     url: str = "",
                                     html_analysis: Dict = None) -> Optional[Dict[str, Any]]:
        """Generate a code fix for a code segment (new format from design judge)."""
        file_path = segment.get("file_path", "")
        if not file_path:
            logger.warning("Code segment missing file_path")
            return None

        # Read the entire file content from the container
        try:
            cmd = f"docker exec forum cat {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                logger.error(f"Failed to read file {file_path}: {result.stderr}")
                return None

            file_content = result.stdout
            if not file_content.strip():
                logger.warning(f"File {file_path} is empty")
                return None

        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None

        # Build the prompt for the coder with the full file content and segment metadata
        prompt = self._build_segment_coder_prompt(
            file_path, file_content, segment, problematic_element,
            primary_reason, primary_improvement, url, html_analysis or {}
        )

        # Call the coder model
        response = self.client.generate_code_fix(prompt, max_tokens=4000)

        if not response.get("success", False):
            logger.error(f"Failed to generate code fix for {file_path}: {response.get('error', 'Unknown error')}")
            return None

        # Parse the response and extract the code
        code_content = response["content"]

        # Extract code from markdown code blocks if present
        import re
        code_match = re.search(r'```(?:css|less|scss)?\s*\n(.*?)\n```', code_content, re.DOTALL)
        if code_match:
            code_content = code_match.group(1).strip()

        return {
            "file_path": file_path,
            "original_content": file_content,
            "modified_content": code_content,
            "segment_metadata": segment,
            "url": url,
            "html_analysis": html_analysis,
            "model_usage": response.get("usage", {})
        }

    def _generate_code_fix_for_snippet(self, snippet: Dict[str, str],
                                     problematic_element: str,
                                     primary_reason: str,
                                     primary_improvement: str,
                                     url: str = "",
                                     html_analysis: Dict = None,
                                     source_files: List[str] = None) -> Optional[Dict[str, Any]]:
        """Generate a code fix for a specific code snippet using HTML analysis and JavaScript state analysis."""

        # Analyze JavaScript state management if HTML analysis is available
        js_analysis = {}
        if html_analysis and source_files:
            element_classes = html_analysis.get('css_classes', [])
            js_analysis = self._analyze_javascript_state_management(source_files, element_classes)

        # Build the prompt for the coder with HTML and JavaScript analysis
        prompt = self._build_coder_prompt(
            snippet, problematic_element, primary_reason, primary_improvement,
            url, html_analysis or {}, js_analysis
        )

        # Call the coder model
        response = self.client.generate_code_fix(prompt)

        if not response.get("success", False):
            logger.error(f"Failed to generate code fix: {response.get('error', 'Unknown error')}")
            return None

        # Parse the response
        code_content = response["content"]

        return {
            "file": snippet["file"],
            "line_number": snippet["line_number"],
            "original_code": snippet["code"],
            "modified_code": code_content,
            "matched_term": snippet.get("matched_term", ""),
            "match_type": snippet.get("match_type", ""),
            "url": url,
            "html_analysis": html_analysis,
            "model_usage": response.get("usage", {})
        }
    
    def _build_coder_prompt(self, snippet: Dict[str, str],
                          problematic_element: str,
                          primary_reason: str,
                          primary_improvement: str,
                          url: str = "",
                          html_analysis: Dict = None,
                          js_analysis: Dict = None) -> str:
        """Build the prompt for the coder model with HTML and JavaScript analysis."""

        # Build HTML analysis section
        html_info = ""
        if html_analysis:
            html_info = f"""
        **HTML Element Analysis (from actual DOM):**
        - URL: {url}
        - Element Type: {html_analysis.get('element_type', 'N/A')}
        - Element Text: {html_analysis.get('element_text', 'N/A')}
        - CSS Classes: {html_analysis.get('css_classes', [])}
        - Accurate CSS Selectors: {html_analysis.get('css_selectors', [])}
        - Parent Chain: {html_analysis.get('parent_chain', [])}
        - Existing States: {html_analysis.get('existing_states', [])}
        - ARIA Attributes: {html_analysis.get('aria_attributes', {})}

        **HTML Context:**
        ```html
        {html_analysis.get('html_context', '')}
        ```
        """

        # Build JavaScript analysis section
        js_info = ""
        if js_analysis and js_analysis.get('relevant_js_files'):
            js_info = f"""
        **JavaScript State Management Analysis:**
        - Relevant JS Files: {js_analysis.get('relevant_js_files', [])}
        - State Classes Found: {js_analysis.get('state_classes', [])}
        - Event Handlers: {js_analysis.get('event_handlers', [])}
        - DOM Manipulations: {js_analysis.get('dom_manipulations', [])}

        **CRITICAL**: Use CSS class-based selectors (e.g., .dropdown--expanded) instead of ARIA attribute selectors when JavaScript manages state via CSS classes.
        """

        prompt = dedent(f"""
        You are an expert frontend developer specializing in CSS/LESS and UI/UX improvements.

        **Context:**
        A web agent failed to complete a task due to a UI design issue. We have analyzed the actual HTML structure and JavaScript state management to fix this issue.
        {html_info}
        {js_info}
        **Problem Analysis:**
        - Problematic Element: {problematic_element}
        - Issue: {primary_reason}
        - Required Improvement: {primary_improvement}

        **Current Code:**
        File: {snippet['file']}
        Line: {snippet['line_number']}
        Matched Term: {snippet.get('matched_term', 'N/A')} ({snippet.get('match_type', 'N/A')})

        ```css
        {snippet['code']}
        ```

        **Task:**
        Modify the above CSS/LESS code to implement the required improvement. Use the HTML analysis to:
        1. Target the correct element using accurate selectors
        2. Consider the actual HTML structure and existing classes
        3. Work with the real DOM hierarchy and parent/child relationships
        4. Use existing state patterns if available
        5. Fix the specific UI issue described
        6. Maintain existing functionality
        7. Follow CSS best practices
        8. Ensure the change is minimal and targeted
        9. **IMPORTANT**: Analyze JavaScript state management - look for CSS classes that are dynamically added/removed by JavaScript (e.g., 'expanded', 'active', 'open', etc.)
        10. **IMPORTANT**: Use CSS class-based selectors instead of ARIA attribute selectors when JavaScript manages state via CSS classes

        **CRITICAL REQUIREMENTS - MUST FOLLOW EXACTLY:**
        1. **EXACT REPLACEMENT**: Your output must be a complete, valid replacement for the original code snippet
        2. **PRESERVE STRUCTURE**: Keep ALL opening braces, closing braces, selectors, and nesting levels EXACTLY as in the original
        3. **NO TRUNCATION**: Do not cut off any part of the original code structure
        4. **SAME START/END**: Start and end at exactly the same logical positions as the original snippet
        5. **VALID SYNTAX**: The output must compile without errors when replacing the original
        6. **NO ADDITIONAL COMMENTS**: Do not add any comments to the output
        7. **USE ACTUAL SELECTORS**: Base your CSS selectors on the actual HTML structure provided

        **Output Format:**
        Provide ONLY the complete, modified CSS/LESS code in a ```css code block.
        The output must be syntactically correct and ready for direct replacement.
        """).strip()

        return prompt

    def _build_segment_coder_prompt(self, file_path: str, file_content: str, segment: Dict[str, Any],
                                  problematic_element: str, primary_reason: str, primary_improvement: str,
                                  url: str = "", html_analysis: Dict = None) -> str:
        """Build the prompt for the coder model using code segment metadata."""

        # Build HTML analysis section
        html_info = ""
        if html_analysis:
            html_info = f"""
        **HTML Element Analysis (from actual DOM):**
        - URL: {url}
        - Element Type: {html_analysis.get('element_type', 'N/A')}
        - Element Text: {html_analysis.get('element_text', 'N/A')}
        - CSS Classes: {html_analysis.get('css_classes', [])}
        - Accurate CSS Selectors: {html_analysis.get('css_selectors', [])}
        - Parent Chain: {html_analysis.get('parent_chain', [])}
        - ARIA Attributes: {html_analysis.get('aria_attributes', {})}

        **HTML Context:**
        ```html
        {html_analysis.get('html_context', '')}
        ```
        """

        # Build segment metadata section
        segment_info = f"""
        **Code Segment Analysis:**
        - File: {file_path}
        - Functionality: {segment.get('functionality', 'N/A')}
        - UI Components: {segment.get('ui_components', [])}
        - CSS Selectors in file: {segment.get('css_selectors', [])[:10]}
        - Symbols: {segment.get('symbols', [])[:10]}
        """

        prompt = dedent(f"""
        You are an expert frontend developer specializing in CSS/LESS and UI/UX improvements.

        **Context:**
        A web agent failed to complete a task due to a UI design issue. We have analyzed the actual HTML structure and identified relevant CSS files to fix this issue.
        {html_info}
        {segment_info}

        **Problem Analysis:**
        - Problematic Element: {problematic_element}
        - Issue: {primary_reason}
        - Required Improvement: {primary_improvement}

        **Current File Content:**
        File: {file_path}

        ```css
        {file_content}
        ```

        **Task:**
        Modify the above CSS/LESS file to implement the required improvement. Focus on:
        1. **Visual Layout Optimization**: Improve element visibility, saliency, and visual feedback
        2. **Target the correct element**: Use the HTML analysis to identify the exact selectors needed
        3. **Consider the actual HTML structure**: Work with the real DOM hierarchy and existing classes
        4. **Maintain existing functionality**: Don't break existing styles
        5. **Follow CSS best practices**: Use efficient, maintainable CSS
        6. **Minimal and targeted changes**: Only modify what's necessary to fix the issue
        7. **Visual indicators**: Add clear visual feedback for user interactions (hover, active, selected states)

        **CRITICAL REQUIREMENTS - MUST FOLLOW EXACTLY:**
        1. **COMPLETE FILE OUTPUT**: Your output must be the COMPLETE, MODIFIED file content
        2. **PRESERVE ALL EXISTING CODE**: Keep all existing CSS rules that are not being modified
        3. **VALID SYNTAX**: The output must compile without errors
        4. **NO TRUNCATION**: Do not cut off any part of the file
        5. **SAME STRUCTURE**: Maintain the same file structure, imports, and organization
        6. **NO ADDITIONAL COMMENTS**: Do not add explanatory comments to the output
        7. **DIRECT REPLACEMENT**: The output should be ready for direct file replacement by the applier

        **Output Format:**
        Provide ONLY the complete, modified CSS/LESS file content in a ```css code block.
        The output must be syntactically correct and ready for direct file replacement.
        """).strip()

        return prompt

    def _analyze_javascript_state_management(self, source_files: List[str], element_classes: List[str]) -> Dict[str, Any]:
        """Analyze JavaScript files to understand state management patterns."""
        js_analysis = {
            "state_classes": [],
            "event_handlers": [],
            "dom_manipulations": [],
            "relevant_js_files": []
        }

        try:
            # Find JavaScript files from source files
            js_files = [f for f in source_files if f.endswith('.js')]

            for js_file in js_files:
                # Read JavaScript file content
                cmd = f"docker exec forum cat {js_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    js_content = result.stdout

                    # Look for state management patterns
                    state_patterns = self._extract_state_patterns(js_content, element_classes)
                    if state_patterns:
                        js_analysis["relevant_js_files"].append(js_file)
                        js_analysis["state_classes"].extend(state_patterns.get("state_classes", []))
                        js_analysis["event_handlers"].extend(state_patterns.get("event_handlers", []))
                        js_analysis["dom_manipulations"].extend(state_patterns.get("dom_manipulations", []))

        except Exception as e:
            logger.debug(f"Error analyzing JavaScript state management: {e}")

        logger.debug(f"JavaScript analysis result: {js_analysis}")
        return js_analysis

    def _extract_state_patterns(self, js_content: str, element_classes: List[str]) -> Dict[str, List[str]]:
        """Extract state management patterns from JavaScript content."""
        patterns = {
            "state_classes": [],
            "event_handlers": [],
            "dom_manipulations": []
        }

        # Look for class toggle/add/remove operations
        import re

        # Find classList operations
        class_operations = re.findall(r'classList\.(add|remove|toggle)\([\'"]([^\'"]+)[\'"]', js_content)
        for operation, class_name in class_operations:
            # Include if it matches element classes OR contains common state indicators
            if (any(cls in class_name for cls in element_classes) or
                any(state in class_name for state in ['expanded', 'active', 'open', 'closed', 'selected', 'current'])):
                patterns["state_classes"].append(f"{operation}: {class_name}")

        # Find className assignments
        classname_assignments = re.findall(r'className\s*[=+]\s*[\'"]([^\'"]*(?:expanded|active|open|closed)[^\'"]*)[\'"]', js_content, re.IGNORECASE)
        patterns["state_classes"].extend(classname_assignments)

        # Find event handlers related to our element classes
        for element_class in element_classes:
            event_handlers = re.findall(rf'\.addEventListener\([\'"](\w+)[\'"].*{element_class}', js_content)
            patterns["event_handlers"].extend(event_handlers)

        # Find DOM manipulations
        dom_methods = re.findall(r'\.(setAttribute|getAttribute|toggleAttribute)\([\'"]([^\'"]+)[\'"]', js_content)
        patterns["dom_manipulations"].extend([f"{method}: {attr}" for method, attr in dom_methods])

        return patterns


def clear_existing_code_fixes(run_dir: str):
    """Clear all existing code_fixes.json files."""
    logger.info("Clearing existing code fixes files...")

    code_fixes_files = glob.glob(os.path.join(run_dir, "graph", "**", "code_fixes.json"), recursive=True)

    for code_fixes_file in code_fixes_files:
        try:
            os.remove(code_fixes_file)
            logger.debug(f"Removed {code_fixes_file}")
        except Exception as e:
            logger.error(f"Error removing {code_fixes_file}: {e}")

    logger.info(f"Cleared {len(code_fixes_files)} existing code fixes files")


def process_feedback_file(feedback_file: Path, coder: DesignCoder) -> Optional[Dict[str, Any]]:
    """Process a single design feedback file."""
    try:
        with open(feedback_file, 'r') as f:
            feedback = json.load(f)

        logger.info(f"Processing feedback file: {feedback_file}")

        # Generate code fixes
        result = coder.process_design_feedback(feedback)

        if result:
            logger.info(f"Generated {len(result['code_fixes'])} code fixes")
            return result
        else:
            logger.debug("No code fixes generated")
            return None

    except Exception as e:
        logger.error(f"Error processing feedback file {feedback_file}: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="Generate code fixes from design feedback")
    parser.add_argument("--run_dir", required=True, help="Run directory containing design feedback")
    parser.add_argument("--base_url", default="http://localhost:8001", help="Coder model API base URL")
    parser.add_argument("--api_key", help="API key for the coder model")
    parser.add_argument("--output_dir", help="Directory to save code fixes (default: {run_dir}/code_fixes)")

    args = parser.parse_args()

    # Initialize coder
    coder = DesignCoder(args.base_url, args.api_key)

    # Set default output directory if not specified
    run_dir = Path(args.run_dir)
    if not args.output_dir:
        args.output_dir = str(run_dir / "code_fixes")
        logger.info(f"Using default output directory: {args.output_dir}")

    # Clear existing code fixes files (like design_judge does)
    clear_existing_code_fixes(str(run_dir))

    # Find all design feedback files
    feedback_files = list(run_dir.rglob("design_feedback.json"))
    feedback_files.extend(list(run_dir.rglob("design_feedback_improved.json")))

    logger.info(f"Found {len(feedback_files)} design feedback files")
    
    # Process each feedback file
    total_fixes = 0
    for feedback_file in feedback_files:
        result = process_feedback_file(feedback_file, coder)
        if result:
            total_fixes += len(result['code_fixes'])

            # Save code_fixes.json directly in the same directory as design_feedback.json
            step_dir = feedback_file.parent
            code_fixes_file = step_dir / "code_fixes.json"

            with open(code_fixes_file, 'w') as f:
                json.dump(result, f, indent=2)

            logger.info(f"Saved code fixes to: {code_fixes_file}")

    logger.info(f"Generated {total_fixes} total code fixes")


if __name__ == "__main__":
    main()
