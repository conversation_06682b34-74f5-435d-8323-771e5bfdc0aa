#!/usr/bin/env python3
"""
Perform Revision - 集成的Web Agent修订流程

这个模块集成了整个revision流程：
1. Design Judge - 分析UI问题
2. Codebase Indexer - 构建代码库索引
3. Intelligent Source Mapper - 智能文件映射
4. Dynamic Selector Analyzer - 动态选择器分析
5. Revision Coder - 生成修复代码
6. Docker Code Applier - 应用修复

使用方式：
python -m webexp.revision.perform_revision --run_dir runs/reddit_qwen_01 --container forum
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add the current directory to sys.path to import modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from design_judge import DesignJudge, find_negative_trajectories, process_negative_trajectory
from codebase_indexer import CodebaseIndexer
from intelligent_source_mapper import IntelligentSourceMapper
from dynamic_selector_analyzer import DynamicSelectorAnalyzer
from revision_coder import DesignCoder
from docker_code_applier import DockerCodeApplier

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RevisionPipeline:
    """完整的revision流程管道"""
    
    def __init__(self, run_dir: str, container_name: str, 
                 vl_base_url: str = "http://localhost:8000",
                 coder_base_url: str = "http://localhost:8001",
                 api_key: Optional[str] = None):
        """
        初始化revision流程
        
        Args:
            run_dir: Go-Browse运行目录
            container_name: Docker容器名称
            vl_base_url: Qwen VL模型API地址
            coder_base_url: Qwen Coder模型API地址
            api_key: API密钥（如果需要）
        """
        self.run_dir = Path(run_dir)
        self.container_name = container_name
        self.vl_base_url = vl_base_url
        self.coder_base_url = coder_base_url
        self.api_key = api_key
        
        # 确保输出目录存在
        self.run_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个组件
        self.design_judge = DesignJudge(
            model_name="Qwen/Qwen2.5-VL-72B-Instruct",
            base_url=vl_base_url,
            api_key=api_key
        )
        self.codebase_indexer = CodebaseIndexer(coder_base_url=coder_base_url)
        self.source_mapper = IntelligentSourceMapper(coder_base_url=coder_base_url)
        self.selector_analyzer = DynamicSelectorAnalyzer(coder_base_url=coder_base_url)
        self.revision_coder = DesignCoder(base_url=coder_base_url, api_key=api_key)
        self.code_applier = DockerCodeApplier(container_name)
        
        logger.info(f"Initialized revision pipeline for run: {run_dir}")
        logger.info(f"Container: {container_name}")
        logger.info(f"VL Model: {vl_base_url}")
        logger.info(f"Coder Model: {coder_base_url}")
    
    def run_complete_pipeline(self, skip_design_judge: bool = False) -> Dict[str, Any]:
        """
        运行完整的revision流程
        
        Args:
            skip_design_judge: 是否跳过design judge步骤（如果已经有分析结果）
            
        Returns:
            流程执行结果
        """
        results = {
            "pipeline_success": False,
            "steps_completed": [],
            "steps_failed": [],
            "outputs": {}
        }
        
        try:
            # 步骤1: Design Judge - 分析UI问题
            if not skip_design_judge:
                logger.info("=== Step 1: Running Design Judge ===")
                design_result = self._run_design_judge()
                if design_result["success"]:
                    results["steps_completed"].append("design_judge")
                    results["outputs"]["design_judge"] = design_result
                    logger.info(f"Design Judge completed: {design_result['feedback_count']} feedbacks generated")
                else:
                    results["steps_failed"].append("design_judge")
                    logger.error(f"Design Judge failed: {design_result.get('error', 'Unknown error')}")
                    return results
            else:
                logger.info("=== Step 1: Skipping Design Judge (using existing results) ===")
                results["steps_completed"].append("design_judge")
            
            # 步骤2: Codebase Indexer - 构建代码库索引
            logger.info("=== Step 2: Building Codebase Index ===")
            index_result = self._run_codebase_indexer()
            if index_result["success"]:
                results["steps_completed"].append("codebase_indexer")
                results["outputs"]["codebase_indexer"] = index_result
                logger.info(f"Codebase indexing completed: {index_result['files_indexed']} files indexed")
            else:
                results["steps_failed"].append("codebase_indexer")
                logger.error(f"Codebase indexing failed: {index_result.get('error', 'Unknown error')}")
                return results
            
            # 步骤3: Intelligent Source Mapper - 智能文件映射
            logger.info("=== Step 3: Running Intelligent Source Mapper ===")
            mapping_result = self._run_source_mapper()
            if mapping_result["success"]:
                results["steps_completed"].append("source_mapper")
                results["outputs"]["source_mapper"] = mapping_result
                logger.info(f"Source mapping completed: {mapping_result['mappings_created']} mappings created")
            else:
                results["steps_failed"].append("source_mapper")
                logger.error(f"Source mapping failed: {mapping_result.get('error', 'Unknown error')}")
                return results
            
            # 步骤4: Dynamic Selector Analyzer - 动态选择器分析
            logger.info("=== Step 4: Running Dynamic Selector Analyzer ===")
            selector_result = self._run_selector_analyzer()
            if selector_result["success"]:
                results["steps_completed"].append("selector_analyzer")
                results["outputs"]["selector_analyzer"] = selector_result
                logger.info(f"Selector analysis completed: {selector_result['analyses_created']} analyses created")
            else:
                results["steps_failed"].append("selector_analyzer")
                logger.error(f"Selector analysis failed: {selector_result.get('error', 'Unknown error')}")
                return results
            
            # 步骤5: Revision Coder - 生成修复代码
            logger.info("=== Step 5: Running Revision Coder ===")
            coder_result = self._run_revision_coder()
            if coder_result["success"]:
                results["steps_completed"].append("revision_coder")
                results["outputs"]["revision_coder"] = coder_result
                logger.info(f"Code generation completed: {coder_result['fixes_generated']} fixes generated")
            else:
                results["steps_failed"].append("revision_coder")
                logger.error(f"Code generation failed: {coder_result.get('error', 'Unknown error')}")
                return results
            
            # 步骤6: Docker Code Applier - 应用修复
            logger.info("=== Step 6: Applying Code Fixes ===")
            apply_result = self._run_code_applier()
            if apply_result["success"]:
                results["steps_completed"].append("code_applier")
                results["outputs"]["code_applier"] = apply_result
                logger.info(f"Code application completed: {apply_result['successful_fixes']} fixes applied")
            else:
                results["steps_failed"].append("code_applier")
                logger.error(f"Code application failed: {apply_result.get('error', 'Unknown error')}")
                return results
            
            results["pipeline_success"] = True
            logger.info("=== Revision Pipeline Completed Successfully ===")
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            results["pipeline_error"] = str(e)
        
        return results
    
    def _run_design_judge(self) -> Dict[str, Any]:
        """运行Design Judge步骤"""
        try:
            # 查找negative trajectories
            negative_traj_paths = find_negative_trajectories(str(self.run_dir))
            if not negative_traj_paths:
                return {"success": False, "error": "No negative trajectories found"}
            
            logger.info(f"Found {len(negative_traj_paths)} negative trajectories")
            
            # 处理每个negative trajectory
            total_feedback_count = 0
            processed_trajs = 0
            
            for traj_path in negative_traj_paths:
                try:
                    feedback_count = process_negative_trajectory(
                        traj_path, self.design_judge, source_mapping=None
                    )
                    total_feedback_count += feedback_count
                    processed_trajs += 1
                    logger.info(f"Processed trajectory {processed_trajs}/{len(negative_traj_paths)}: "
                               f"{feedback_count} feedbacks generated")
                except Exception as e:
                    logger.error(f"Error processing trajectory {traj_path}: {e}")
            
            return {
                "success": True,
                "feedback_count": total_feedback_count,
                "processed_trajectories": processed_trajs,
                "total_trajectories": len(negative_traj_paths)
            }
            
        except Exception as e:
            logger.error(f"Design Judge execution failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _run_codebase_indexer(self) -> Dict[str, Any]:
        """运行Codebase Indexer步骤"""
        try:
            # 构建代码库索引
            codebase_index = self.codebase_indexer.index_codebase(self.container_name)
            
            # 保存索引到文件
            index_file = self.run_dir / "codebase_index.json"
            self.codebase_indexer._save_index(codebase_index, str(index_file))
            
            return {
                "success": True,
                "files_indexed": len(codebase_index.indexed_files),
                "framework_detected": codebase_index.framework_info.get("framework", "unknown"),
                "index_file": str(index_file)
            }
            
        except Exception as e:
            logger.error(f"Codebase indexing failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _run_source_mapper(self) -> Dict[str, Any]:
        """运行Intelligent Source Mapper步骤"""
        try:
            # 加载代码库索引
            index_file = self.run_dir / "codebase_index.json"
            if not index_file.exists():
                return {"success": False, "error": "Codebase index not found"}
            
            codebase_index = self.codebase_indexer.load_index(str(index_file))
            
            # 查找design feedback文件
            design_feedback_files = list(self.run_dir.rglob("design_feedback.json"))
            if not design_feedback_files:
                return {"success": False, "error": "No design feedback files found"}
            
            mappings_created = 0
            
            # 为每个design feedback创建source mapping
            for feedback_file in design_feedback_files:
                try:
                    with open(feedback_file, 'r') as f:
                        feedback = json.load(f)
                    
                    # 提取URL和问题描述
                    url = feedback.get("url", "")
                    issue_description = feedback.get("primary_reason", "")
                    
                    if url and issue_description:
                        # 执行智能映射
                        mapping_result = self.source_mapper.map_url_to_sources(
                            self.container_name, url, issue_description, codebase_index
                        )
                        
                        # 保存映射结果
                        mapping_file = feedback_file.parent / "source_mapping.json"
                        self.source_mapper.save_mapping_result(mapping_result, str(mapping_file))
                        mappings_created += 1
                        
                        logger.info(f"Created source mapping for {feedback_file.parent.name}")
                    
                except Exception as e:
                    logger.error(f"Error processing feedback file {feedback_file}: {e}")
            
            return {
                "success": True,
                "mappings_created": mappings_created,
                "total_feedback_files": len(design_feedback_files)
            }
            
        except Exception as e:
            logger.error(f"Source mapping failed: {e}")
            return {"success": False, "error": str(e)}

    def _run_selector_analyzer(self) -> Dict[str, Any]:
        """运行Dynamic Selector Analyzer步骤"""
        try:
            # 查找source mapping文件
            mapping_files = list(self.run_dir.rglob("source_mapping.json"))
            if not mapping_files:
                return {"success": False, "error": "No source mapping files found"}

            analyses_created = 0

            # 为每个source mapping创建selector analysis
            for mapping_file in mapping_files:
                try:
                    # 加载source mapping
                    mapping_result = self.source_mapper.load_mapping_result(str(mapping_file))

                    # 加载对应的design feedback
                    feedback_file = mapping_file.parent / "design_feedback.json"
                    if not feedback_file.exists():
                        logger.warning(f"No design feedback found for {mapping_file}")
                        continue

                    with open(feedback_file, 'r') as f:
                        feedback = json.load(f)

                    # 执行selector analysis
                    analysis_result = self.selector_analyzer.analyze_element_selectors(
                        self.container_name,
                        mapping_result.url,
                        feedback.get("problematic_element", ""),
                        mapping_result.matched_files,
                        feedback.get("html_content", "")
                    )

                    # 保存分析结果
                    analysis_file = mapping_file.parent / "selector_analysis.json"
                    with open(analysis_file, 'w') as f:
                        json.dump(analysis_result.__dict__, f, indent=2)

                    analyses_created += 1
                    logger.info(f"Created selector analysis for {mapping_file.parent.name}")

                except Exception as e:
                    logger.error(f"Error processing mapping file {mapping_file}: {e}")

            return {
                "success": True,
                "analyses_created": analyses_created,
                "total_mapping_files": len(mapping_files)
            }

        except Exception as e:
            logger.error(f"Selector analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def _run_revision_coder(self) -> Dict[str, Any]:
        """运行Revision Coder步骤"""
        try:
            # 查找design feedback文件
            feedback_files = list(self.run_dir.rglob("design_feedback.json"))
            if not feedback_files:
                return {"success": False, "error": "No design feedback files found"}

            # 确保code_fixes目录存在
            code_fixes_dir = self.run_dir / "code_fixes"
            code_fixes_dir.mkdir(exist_ok=True)

            fixes_generated = 0
            all_code_fixes = []

            # 为每个design feedback生成code fix
            for feedback_file in feedback_files:
                try:
                    with open(feedback_file, 'r') as f:
                        feedback = json.load(f)

                    # 检查是否有对应的source mapping和selector analysis
                    mapping_file = feedback_file.parent / "source_mapping.json"
                    analysis_file = feedback_file.parent / "selector_analysis.json"

                    source_mapping = None
                    selector_analysis = None

                    if mapping_file.exists():
                        source_mapping = self.source_mapper.load_mapping_result(str(mapping_file))

                    if analysis_file.exists():
                        with open(analysis_file, 'r') as f:
                            selector_analysis = json.load(f)

                    # 生成code fix
                    result = self.revision_coder.process_design_feedback(feedback)

                    if result and result.get("code_fixes"):
                        code_fixes = result["code_fixes"]

                        # 保存code fixes到step目录
                        step_fixes_file = feedback_file.parent / "code_fixes.json"
                        with open(step_fixes_file, 'w') as f:
                            json.dump(code_fixes, f, indent=2)

                        all_code_fixes.extend(code_fixes)
                        fixes_generated += len(code_fixes)

                        logger.info(f"Generated {len(code_fixes)} code fixes for {feedback_file.parent.name}")

                except Exception as e:
                    logger.error(f"Error processing feedback file {feedback_file}: {e}")

            # 保存所有code fixes到总文件
            if all_code_fixes:
                all_fixes_file = code_fixes_dir / "all_code_fixes.json"
                with open(all_fixes_file, 'w') as f:
                    json.dump(all_code_fixes, f, indent=2)

                logger.info(f"Saved all {len(all_code_fixes)} code fixes to {all_fixes_file}")

            return {
                "success": True,
                "fixes_generated": fixes_generated,
                "total_feedback_files": len(feedback_files),
                "code_fixes_file": str(code_fixes_dir / "all_code_fixes.json") if all_code_fixes else None
            }

        except Exception as e:
            logger.error(f"Code generation failed: {e}")
            return {"success": False, "error": str(e)}

    def _run_code_applier(self) -> Dict[str, Any]:
        """运行Docker Code Applier步骤"""
        try:
            # 查找code fixes文件
            code_fixes_file = self.run_dir / "code_fixes" / "all_code_fixes.json"
            if not code_fixes_file.exists():
                return {"success": False, "error": "No code fixes file found"}

            # 加载code fixes
            with open(code_fixes_file, 'r') as f:
                code_fixes = json.load(f)

            if not code_fixes:
                return {"success": False, "error": "No code fixes to apply"}

            # 应用code fixes
            results = []
            for i, code_fix in enumerate(code_fixes):
                logger.info(f"Applying code fix {i+1}/{len(code_fixes)}")
                result = self.code_applier.apply_code_fix(code_fix)
                results.append(result)

                if not result["success"]:
                    logger.error(f"Failed to apply code fix {i+1}: {result.get('error', 'Unknown error')}")

            successful_fixes = sum(1 for r in results if r["success"])

            # 保存应用结果
            apply_results_file = self.run_dir / "code_fixes" / "apply_results.json"
            with open(apply_results_file, 'w') as f:
                json.dump({
                    "total_fixes": len(code_fixes),
                    "successful_fixes": successful_fixes,
                    "results": results,
                    "applier_summary": self.code_applier.get_modified_files_summary()
                }, f, indent=2)

            return {
                "success": successful_fixes > 0,
                "total_fixes": len(code_fixes),
                "successful_fixes": successful_fixes,
                "results": results,
                "apply_results_file": str(apply_results_file)
            }

        except Exception as e:
            logger.error(f"Code application failed: {e}")
            return {"success": False, "error": str(e)}


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description="Perform complete revision pipeline for Go-Browse negative trajectories",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # 运行完整流程
  python -m webexp.revision.perform_revision --run_dir runs/reddit_qwen_01 --container forum

  # 跳过design judge（使用现有结果）
  python -m webexp.revision.perform_revision --run_dir runs/reddit_qwen_01 --container forum --skip_design_judge

  # 使用自定义模型端点
  python -m webexp.revision.perform_revision --run_dir runs/reddit_qwen_01 --container forum --vl_base_url http://localhost:8000 --coder_base_url http://localhost:8001
        """
    )

    parser.add_argument("--run_dir", required=True,
                       help="Go-Browse run directory containing trajectory data")
    parser.add_argument("--container", required=True,
                       help="Docker container name")
    parser.add_argument("--vl_base_url", default="http://localhost:8000",
                       help="Qwen VL model API base URL (default: http://localhost:8000)")
    parser.add_argument("--coder_base_url", default="http://localhost:8001",
                       help="Qwen Coder model API base URL (default: http://localhost:8001)")
    parser.add_argument("--api_key",
                       help="API key for model endpoints (if required)")
    parser.add_argument("--skip_design_judge", action="store_true",
                       help="Skip design judge step (use existing design feedback)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 验证输入
    run_dir = Path(args.run_dir)
    if not run_dir.exists():
        logger.error(f"Run directory does not exist: {run_dir}")
        sys.exit(1)

    # 初始化pipeline
    try:
        pipeline = RevisionPipeline(
            run_dir=str(run_dir),
            container_name=args.container,
            vl_base_url=args.vl_base_url,
            coder_base_url=args.coder_base_url,
            api_key=args.api_key
        )
    except Exception as e:
        logger.error(f"Failed to initialize revision pipeline: {e}")
        sys.exit(1)

    # 运行pipeline
    logger.info("Starting revision pipeline...")
    results = pipeline.run_complete_pipeline(skip_design_judge=args.skip_design_judge)

    # 输出结果
    if results["pipeline_success"]:
        logger.info("🎉 Revision pipeline completed successfully!")
        logger.info(f"✅ Steps completed: {', '.join(results['steps_completed'])}")

        # 输出各步骤的详细结果
        for step, output in results["outputs"].items():
            if step == "design_judge":
                logger.info(f"  📊 Design Judge: {output['feedback_count']} feedbacks from {output['processed_trajectories']} trajectories")
            elif step == "codebase_indexer":
                logger.info(f"  📁 Codebase Indexer: {output['files_indexed']} files indexed ({output['framework_detected']} framework)")
            elif step == "source_mapper":
                logger.info(f"  🎯 Source Mapper: {output['mappings_created']} mappings created")
            elif step == "selector_analyzer":
                logger.info(f"  🔍 Selector Analyzer: {output['analyses_created']} analyses created")
            elif step == "revision_coder":
                logger.info(f"  💻 Revision Coder: {output['fixes_generated']} code fixes generated")
            elif step == "code_applier":
                logger.info(f"  🚀 Code Applier: {output['successful_fixes']}/{output['total_fixes']} fixes applied successfully")

        logger.info(f"📂 Results saved in: {run_dir}")
        sys.exit(0)
    else:
        logger.error("❌ Revision pipeline failed!")
        if results["steps_failed"]:
            logger.error(f"❌ Failed steps: {', '.join(results['steps_failed'])}")
        if "pipeline_error" in results:
            logger.error(f"❌ Pipeline error: {results['pipeline_error']}")
        sys.exit(1)


if __name__ == "__main__":
    main()
