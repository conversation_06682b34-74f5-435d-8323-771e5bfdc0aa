{"container_name": "forum", "indexed_files": {"/var/www/html/assets/css/_card/dropdown-card.less": {"file_path": "/var/www/html/assets/css/_card/dropdown-card.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a dropdown card component, setting its background color and box shadow using CSS variables.", "symbols": ["dropdown-card"], "ui_components": ["dropdown"], "css_selectors": [".dropdown-card"], "imports_exports": [], "dependencies": ["--dropdown-card-bg", "--card-bg", "--dropdown-card-shadow", "--card-shadow"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_form/_mixins.less": {"file_path": "/var/www/html/assets/css/_form/_mixins.less", "file_type": "css", "language": "css", "functionality": "This file contains LESS mixins for styling form controls, including common styles, text input styles, focus states, disabled states, read-only states, and undecorated styles.", "symbols": [".form-control-common", ".form-control-text", ".form-control-focus", ".form-control-disabled", ".form-control-readonly", ".form-control-undecorated", ".form-control-undecorated-focus"], "ui_components": ["form"], "css_selectors": [], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "form-styling"]}, "/var/www/html/assets/css/_form/compound-form-widget.less": {"file_path": "/var/www/html/assets/css/_form/compound-form-widget.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a compound form widget, ensuring that its child elements are displayed in a flexible layout with specific padding and margin adjustments. It also manages the z-index to bring focused elements to the front.", "symbols": [], "ui_components": ["form"], "css_selectors": [".compound-form-widget", ".compound-form-widget > *"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "form"]}, "/var/www/html/assets/css/_form/decorated-form-control.less": {"file_path": "/var/www/html/assets/css/_form/decorated-form-control.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a decorated form control, including common styles, focus states, disabled and read-only states, and specific text styling. It also includes a widget component within the form control.", "symbols": ["form-control-common", "form-control-focus", "form-control-disabled", "form-control-readonly", "form-control-text"], "ui_components": ["form"], "css_selectors": [".decorated-form-control", ".decorated-form-control--disabled", ".decorated-form-control--read-only", ".decorated-form-control--text", ".decorated-form-control__widget"], "imports_exports": ["_mixins"], "dependencies": ["_mixins"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_form/fieldset.less": {"file_path": "/var/www/html/assets/css/_form/fieldset.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a fieldset element, including its border, padding, and the styling of its legend child element.", "symbols": ["fieldset", "legend"], "ui_components": ["form"], "css_selectors": [".fieldset", ".fieldset > legend"], "imports_exports": [], "dependencies": ["--border", "--text-muted"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_form/form-error-list.less": {"file_path": "/var/www/html/assets/css/_form/form-error-list.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a form error list, setting the padding, color, font weight, and list style position for the list items.", "symbols": [], "ui_components": ["form"], "css_selectors": [".form-error-list", ".form-error-list > li"], "imports_exports": [], "dependencies": ["--fg-red (CSS variable)"], "semantic_tags": ["form", "error-handling"]}, "/var/www/html/assets/css/_form/form-flex.less": {"file_path": "/var/www/html/assets/css/_form/form-flex.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a flexible form layout using CSS Flexbox. It includes different configurations for stacking and aligning form elements based on screen size.", "symbols": ["form-flex", "form-flex--stretch", "form-flex--single-line", "form-flex__align", "form-flex--no-collapse"], "ui_components": ["form"], "css_selectors": [".form-flex", ".form-flex--stretch", ".form-flex--single-line", ".form-flex__align", ".form-flex--no-collapse"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component", "responsive-design"]}, "/var/www/html/assets/css/_form/form-tabs.less": {"file_path": "/var/www/html/assets/css/_form/form-tabs.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for form tabs, including active states for discreet and regular tabs, and visibility control for tab content based on checkbox states.", "symbols": ["form-tabs", "tab-active", "discreet-tab-active"], "ui_components": ["form", "tabs"], "css_selectors": [".form-tabs", ".form-tabs__checkbox", ".tab", ".discreet-tab", ".form-tabs__content"], "imports_exports": ["../_widgets/discreet-tab", "../_widgets/tab"], "dependencies": ["../_widgets/discreet-tab", "../_widgets/tab"], "semantic_tags": ["ui-component", "form-element"]}, "/var/www/html/assets/css/_form/form.less": {"file_path": "/var/www/html/assets/css/_form/form.less", "file_type": "css", "language": "css", "functionality": "This file contains styles for form elements, specifically targeting layout adjustments for different screen sizes.", "symbols": ["form", "form__row", "form__button-row", "desktop-min-width"], "ui_components": ["form", "button"], "css_selectors": [".form", ".form__row", ".form__button-row"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component", "responsive-design"]}, "/var/www/html/assets/css/_form/formatting-help.less": {"file_path": "/var/www/html/assets/css/_form/formatting-help.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for a formatting help section, specifically styling a table with class 'formatting-help' to have collapsed borders and setting margins, borders, and padding for table cells.", "symbols": [], "ui_components": ["form"], "css_selectors": [".formatting-help", ".formatting-help td"], "imports_exports": [], "dependencies": ["--border (CSS variable)"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_form/markdown-preview.less": {"file_path": "/var/www/html/assets/css/_form/markdown-preview.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for a markdown preview component, including responsive design and styling for different parts of the component.", "symbols": ["markdown-preview", "markdown-preview__title", "markdown-preview__inner"], "ui_components": ["markdown preview"], "css_selectors": [".markdown-preview", ".markdown-preview__title", ".markdown-preview__inner"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_form/unstylable-widget.less": {"file_path": "/var/www/html/assets/css/_form/unstylable-widget.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for customizing the appearance of 'unstylable' form widgets such as select, radio, and checkbox elements. It ensures these elements have a consistent look and feel across different browsers by removing default styling and adding custom icons and states.", "symbols": ["unstylable-widget", "caret", "check", "circle", "fg-grey", "font-size", "shrink"], "ui_components": ["dropdown", "checkbox", "radio"], "css_selectors": [".unstylable-widget", ".unstylable-widget__caret", ".unstylable-widget__check", ".unstylable-widget__circle"], "imports_exports": [], "dependencies": [], "semantic_tags": ["form", "ui-component", "custom-styling"]}, "/var/www/html/assets/css/_global.less": {"file_path": "/var/www/html/assets/css/_global.less", "file_type": "css", "language": "css", "functionality": "This file contains global CSS styles for a web application, including typography, layout, and basic UI component styling.", "symbols": ["--font-size", "--line-height", "--mono-font-family", "--mono-font-size", "--bg-page", "--bg-image", "--text", "--font-family", "--primary-alt", "--primary-fg", "--link", "--border", "--text-muted", "--border-light"], "ui_components": ["button", "form", "blockquote", "pre", "code", "hr", "summary"], "css_selectors": ["html", "*", "*::before", "*::after", "::selection", "body", "[hidden]", "a", "a:hover", "a[href=\"#s\"]", "a[href=\"#spoiler\"]", "h1", "h2", "h3", "h4", "h5", "h6", "p", "dl", "ol", "ul", "blockquote", "pre", "button", "input", "optgroup", "option", "select", "textarea", "pre code", "hr", "summary"], "imports_exports": ["~typeface-roboto-mono", "_variables"], "dependencies": ["typeface-roboto-mono", "_variables"], "semantic_tags": ["typography", "layout", "ui-component"]}, "/var/www/html/assets/css/_layout/content-container.less": {"file_path": "/var/www/html/assets/css/_layout/content-container.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a content container, centering it horizontally and setting a maximum width based on a variable unless the root element has a 'full-width' class.", "symbols": ["content-container", "max-content-width"], "ui_components": [], "css_selectors": [".content-container", ":root:not(.full-width) .content-container"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["layout", "container"]}, "/var/www/html/assets/css/_layout/flow.less": {"file_path": "/var/www/html/assets/css/_layout/flow.less", "file_type": "css", "language": "css", "functionality": "This file defines a mixin for CSS that applies consistent vertical spacing between elements within a container, with exceptions for legends.", "symbols": ["flow-mixin", "flow", "flow-slim"], "ui_components": [], "css_selectors": [".flow", ".flow-slim", "> * + *", "> legend + *"], "imports_exports": [], "dependencies": [], "semantic_tags": ["layout", "spacing"]}, "/var/www/html/assets/css/_layout/sidebar.less": {"file_path": "/var/www/html/assets/css/_layout/sidebar.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a sidebar component, including layout, responsiveness, and visual elements like background color, shadow, and typography.", "symbols": ["sidebar", "sidebar__section", "sidebar__title", "sidebar__no-padding"], "ui_components": ["sidebar"], "css_selectors": [".sidebar", ".sidebar__section", ".sidebar__title", ".sidebar__no-padding"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["navigation", "ui-component"]}, "/var/www/html/assets/css/_layout/site-accessibility-nav.less": {"file_path": "/var/www/html/assets/css/_layout/site-accessibility-nav.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for an accessibility navigation link that is typically hidden unless focused, ensuring it is accessible to keyboard-only users.", "symbols": ["site-accessibility-nav", "link"], "ui_components": ["link"], "css_selectors": [".site-accessibility-nav", ".site-accessibility-nav__link"], "imports_exports": [], "dependencies": ["--card-bg", "--text"], "semantic_tags": ["accessibility", "navigation", "ui-component"]}, "/var/www/html/assets/css/_layout/site-alerts.less": {"file_path": "/var/www/html/assets/css/_layout/site-alerts.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for site alerts, including positioning, animations, and dismiss functionality. It handles both JavaScript-enabled and non-JavaScript environments, with considerations for reduced motion preferences.", "symbols": ["site-alerts", "site-alerts-slide", "site-alerts-slide-no-js", "site-alerts-fade", "site-alerts-fade-no-js"], "ui_components": ["alert", "dismiss button"], "css_selectors": [".site-alerts", ".site-alerts__alert", ".site-alerts__dismiss", ".js:root .site-alerts__alert", ".no-js:root .site-alerts__alert", ".js:root .site-alerts__dismiss", ".no-js:root .site-alerts__dismiss"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "animation", "accessibility"]}, "/var/www/html/assets/css/_layout/site-content.less": {"file_path": "/var/www/html/assets/css/_layout/site-content.less", "file_type": "css", "language": "css", "functionality": "This file defines the layout styles for the site content, including body and sidebar sections, with responsive design adjustments for different screen sizes.", "symbols": ["site-content", "site-content__body", "site-content__sidebar", "desktop-min-width"], "ui_components": ["body", "sidebar"], "css_selectors": [".site-content", ".site-content__body", ".site-content__sidebar"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["layout", "responsive-design", "ui-component"]}, "/var/www/html/assets/css/_layout/site-footer.less": {"file_path": "/var/www/html/assets/css/_layout/site-footer.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a site footer, setting its margin, padding, and text alignment.", "symbols": ["site-footer"], "ui_components": ["footer"], "css_selectors": [".site-footer"], "imports_exports": [], "dependencies": [], "semantic_tags": ["footer", "ui-component"]}, "/var/www/html/assets/css/_layout/site-nav.less": {"file_path": "/var/www/html/assets/css/_layout/site-nav.less", "file_type": "css", "language": "css", "functionality": "This file defines the styles for a site navigation bar, including layout, colors, hover effects, and responsive design adjustments.", "symbols": ["--site-nav-bg", "--primary", "--primary-fg", "--primary-alt", "--notification", "@import"], "ui_components": ["dropdown", "search-input", "search-label", "search-row", "list", "item", "link"], "css_selectors": [".site-nav", ".site-nav__container", ".site-nav__link", ".site-nav__has-notifications", ".site-nav__search-input", ".site-nav__search-label", ".site-nav__search-row", ".site-nav__list", ".site-nav__item", ".site-nav__item--search", ".site-nav__item--user", ".icon", ".text", ".no-js:root", ".js:root", ".dropdown__toggle", ".dropdown--expanded"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["navigation", "ui-component", "responsive-design"]}, "/var/www/html/assets/css/_layout/text-flow.less": {"file_path": "/var/www/html/assets/css/_layout/text-flow.less", "file_type": "css", "language": "less", "functionality": "This file defines a mixin for controlling the spacing between text elements in a layout, ensuring consistent margins and handling nested elements appropriately.", "symbols": ["text-flow-mixin", "text-flow", "text-flow-slim"], "ui_components": [], "css_selectors": [".text-flow", ".text-flow-slim", "h1", "h2", "h3", "h4", "h5", "h6", "p", "blockquote", "pre", "hr", "dl", "ol", "ul", "li", "img.inserted-image"], "imports_exports": [], "dependencies": [], "semantic_tags": ["layout", "spacing", "text-elements"]}, "/var/www/html/assets/css/_resets/unbuttonize.less": {"file_path": "/var/www/html/assets/css/_resets/unbuttonize.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class .unbuttonize to style button elements to appear as regular links, removing default button styling and adding hover effects.", "symbols": ["unbuttonize"], "ui_components": ["button"], "css_selectors": [".unbuttonize"], "imports_exports": [], "dependencies": ["--link variable"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_resets/undecorate.less": {"file_path": "/var/www/html/assets/css/_resets/undecorate.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class named 'undecorate' that removes text decoration from elements it is applied to.", "symbols": ["undecorate"], "ui_components": [], "css_selectors": [".undecorate"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-styling"]}, "/var/www/html/assets/css/_resets/unheaderize.less": {"file_path": "/var/www/html/assets/css/_resets/unheaderize.less", "file_type": "css", "language": "css", "functionality": "This file removes default styles from <h1-6> HTML heading tags by setting their font weight and size to inherit.", "symbols": ["unheaderize"], "ui_components": [], "css_selectors": [".unheaderize"], "imports_exports": [], "dependencies": [], "semantic_tags": []}, "/var/www/html/assets/css/_resets/unlistify.less": {"file_path": "/var/www/html/assets/css/_resets/unlistify.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class named 'unlistify' that removes default list styles from ordered (ol) and unordered (ul) lists, specifically targeting the padding and list style type of list items (li).", "symbols": ["unlistify"], "ui_components": [], "css_selectors": [".unlistify", ".unlistify > li"], "imports_exports": [], "dependencies": [], "semantic_tags": ["list-styling", "css-reset"]}, "/var/www/html/assets/css/_things/alert.less": {"file_path": "/var/www/html/assets/css/_things/alert.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for an alert component, including alignment, display type, padding, and specific styling for an icon within the alert.", "symbols": [], "ui_components": ["alert"], "css_selectors": [".alert", ".alert__icon"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/border-list.less": {"file_path": "/var/www/html/assets/css/_things/border-list.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class .border-list that applies top and bottom borders to an element and adds a top border to each subsequent child element within it.", "symbols": ["--border-light"], "ui_components": [], "css_selectors": [".border-list", ".border-list > * + *"], "imports_exports": [], "dependencies": ["--border-light (CSS variable)"], "semantic_tags": ["ui-component", "styling"]}, "/var/www/html/assets/css/_things/columns.less": {"file_path": "/var/www/html/assets/css/_things/columns.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS styles for a multi-column layout using the .columns class, setting the number of columns to auto and each column's width to 12em.", "symbols": [], "ui_components": [], "css_selectors": [".columns"], "imports_exports": [], "dependencies": [], "semantic_tags": ["layout", "multi-column"]}, "/var/www/html/assets/css/_things/comment.less": {"file_path": "/var/www/html/assets/css/_things/comment.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for a comment section on a webpage, including layout, borders, padding, and media queries for responsiveness.", "symbols": ["@border-thin", "@border-thick", "@vote-width"], "ui_components": ["form"], "css_selectors": [".comment", ".comment__row", ".comment__vote", ".comment__main", ".comment__reply-link-disabled", ".comment__replies", ".comment__header", ".comment__info", ".comment__info-link", ".comment__content", ".comment__body", ".comment__nav", ".comment--nested:last-child", ".comment-form", ".hideable__checkbox:not(:checked) ~ .comment__row .comment__header", ".hideable__checkbox:not(:checked) ~ .comment__row .comment__info", ".hideable__checkbox:focus ~ .comment__row .comment__hide-toggle"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/definition-list.less": {"file_path": "/var/www/html/assets/css/_things/definition-list.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a definition list, setting up a grid layout with specific gaps and column configurations, and styling the direct child elements <dt> and <dd>.", "symbols": [], "ui_components": ["definition-list"], "css_selectors": [".definition-list", ".definition-list > dt", ".definition-list > dd"], "imports_exports": [], "dependencies": [], "semantic_tags": ["list", "grid-layout", "styling"]}, "/var/www/html/assets/css/_things/drop-zone.less": {"file_path": "/var/www/html/assets/css/_things/drop-zone.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a drop zone UI component, including its layout, colors, and active state appearance.", "symbols": ["drop-zone", "drop-zone--active"], "ui_components": ["drop zone"], "css_selectors": [".drop-zone", ".drop-zone--active"], "imports_exports": [], "dependencies": ["--bg-page", "--border-light", "--border"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/empty.less": {"file_path": "/var/www/html/assets/css/_things/empty.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for an empty state component, which includes layout properties and styling for an emoji and text within a centered container.", "symbols": [], "ui_components": ["empty state"], "css_selectors": [".empty", ".empty__emoji", ".empty__text"], "imports_exports": [], "dependencies": ["--fg-grey", "--text-muted"], "semantic_tags": ["ui-component", "layout"]}, "/var/www/html/assets/css/_things/flair.less": {"file_path": "/var/www/html/assets/css/_things/flair.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a UI component named 'flair', which includes styling for labels and buttons within it. It uses CSS variables for colors and transitions for opacity effects.", "symbols": ["flair", "flair__label", "flair__buttons", "flair__button"], "ui_components": ["button"], "css_selectors": [".flair", ".flair__label", ".flair__buttons", ".flair__button"], "imports_exports": [], "dependencies": ["--accent", "--accent-alt", "--accent-fg"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/flex.less": {"file_path": "/var/www/html/assets/css/_things/flex.less", "file_type": "css", "language": "less", "functionality": "This file defines a set of LESS mixins and classes for creating flexible layouts using CSS Flexbox. It includes options for alignment, direction, wrapping, gutters, and item growth/shrink behavior.", "symbols": ["flex", "flex--align-center", "flex--no-wrap", "flex--column", "flex--row", "flex--inline", "flex--guttered", "flex--slim-gutters", "flex__grow", "flex__shrink"], "ui_components": [], "css_selectors": [".flex", ".flex-desktop"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["layout", "flexbox", "responsive-design"]}, "/var/www/html/assets/css/_things/heading-permalink.less": {"file_path": "/var/www/html/assets/css/_things/heading-permalink.less", "file_type": "css", "language": "css", "functionality": "This CSS file defines styles for a heading permalink that is initially hidden and becomes visible when the parent heading (h1 to h6) is hovered over.", "symbols": [], "ui_components": ["permalink"], "css_selectors": [".heading-permalink", "h1 .heading-permalink", "h2 .heading-permalink", "h3 .heading-permalink", "h4 .heading-permalink", "h5 .heading-permalink", "h6 .heading-permalink", "h1:hover .heading-permalink", "h2:hover .heading-permalink", "h3:hover .heading-permalink", "h4:hover .heading-permalink", "h5:hover .heading-permalink", "h6:hover .heading-permalink"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "interaction"]}, "/var/www/html/assets/css/_things/hideable.less": {"file_path": "/var/www/html/assets/css/_things/hideable.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS styles for a hideable component that can be toggled using a checkbox without JavaScript. It includes styles for hiding and showing elements based on the checkbox state, as well as styling for the toggle button and indicator.", "symbols": ["hideable", "checkbox", "toggle", "indicator"], "ui_components": ["toggle", "checkbox", "indicator"], "css_selectors": [".hideable", ".hideable__checkbox", ".hideable__hide", ".hideable__toggle", ".hideable__indicator"], "imports_exports": ["../_utilities/hidden"], "dependencies": ["../_utilities/hidden"], "semantic_tags": ["ui-component", "toggle", "visibility"]}, "/var/www/html/assets/css/_things/icon.less": {"file_path": "/var/www/html/assets/css/_things/icon.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for icons, including their alignment, spinning animations, pulsing effects, and circled appearance.", "symbols": ["icon-spin"], "ui_components": ["icon"], "css_selectors": [".icon", ".icon svg", ".icon:not(&--no-align):not(&--circled) svg", ".icon--spin svg", ".icon--pulse svg", ".icon--circled"], "imports_exports": [], "dependencies": [], "semantic_tags": ["icon", "animation", "style"]}, "/var/www/html/assets/css/_things/message.less": {"file_path": "/var/www/html/assets/css/_things/message.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a message component, including border, padding, and nested body styling.", "symbols": ["message", "message__body"], "ui_components": ["message"], "css_selectors": [".message", ".message__body"], "imports_exports": [], "dependencies": ["--border-light"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/submission-meta.less": {"file_path": "/var/www/html/assets/css/_things/submission-meta.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a submission meta component, specifically styling a short URL element within it.", "symbols": ["submission-meta", "short-url"], "ui_components": [], "css_selectors": [".submission-meta", ".submission-meta__short-url"], "imports_exports": [], "dependencies": ["--fg-broken", "--text-invert"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/submission.less": {"file_path": "/var/www/html/assets/css/_things/submission.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for a submission component, including layout, typography, colors, and responsive design adjustments.", "symbols": ["submission", "submission__row", "submission__inner", "submission__title-row", "submission__link", "submission__host", "submission__content", "submission__image-link", "submission__image", "submission__flairs", "submission__thumb"], "ui_components": ["link", "image", "text"], "css_selectors": [".submission", ".submission__row", ".submission__inner", ".submission__title-row", ".submission__link", ".submission__host", ".submission__content", ".submission__image-link", ".submission__image", ".submission__flairs", ".submission__thumb"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component", "responsive-design"]}, "/var/www/html/assets/css/_things/table-of-contents.less": {"file_path": "/var/www/html/assets/css/_things/table-of-contents.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a table of contents element, setting its border, display type, width, padding, and removing default margins from paragraphs, ordered lists, and unordered lists within it.", "symbols": [".table-of-contents", "--border"], "ui_components": ["table of contents"], "css_selectors": [".table-of-contents", ".table-of-contents p", ".table-of-contents ol", ".table-of-contents ul"], "imports_exports": [], "dependencies": ["--border variable (likely defined elsewhere)"], "semantic_tags": ["navigation", "ui-component"]}, "/var/www/html/assets/css/_things/table.less": {"file_path": "/var/www/html/assets/css/_things/table.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a table component, including border settings, padding, and responsive design adjustments.", "symbols": ["table", "table__shrink", "desktop-min-width", "accent-alt", "border-light", "accent", "accent-fg"], "ui_components": ["table"], "css_selectors": [".table", ".table__shrink", ".table thead tr:last-child th", ".table thead tr:last-child td", ".table tbody tr:not(:last-child) td", ".table tbody tr:not(:last-child) th", ".table tfoot tr:not(:last-child) td", ".table tfoot tr:not(:last-child) th", ".table thead tr th", ".table tbody tr th", ".table tfoot tr th", ".table thead tr th", ".table tbody tr th", ".table tfoot tr th", ".table thead tr td", ".table tbody tr td", ".table tfoot tr td"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["table", "responsive-design"]}, "/var/www/html/assets/css/_things/user-flag.less": {"file_path": "/var/www/html/assets/css/_things/user-flag.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS styles for a user flag element, adding square brackets before and after the element using the ::before and ::after pseudo-elements.", "symbols": ["user-flag"], "ui_components": [], "css_selectors": [".user-flag"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/vote.less": {"file_path": "/var/www/html/assets/css/_things/vote.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a voting component, including states for loading, user upvoting, downvoting, and failed states.", "symbols": ["@vote-width", ".vote", ".vote--loading", ".vote__net-score", ".vote__spinner", ".vote__button", ".vote--user-upvoted", ".vote__up", ".vote--user-downvoted", ".vote__down", ".vote--failed"], "ui_components": ["button"], "css_selectors": [".vote", ".vote--loading", ".vote__net-score", ".vote__spinner", ".vote__button", ".vote--user-upvoted", ".vote__up", ".vote--user-downvoted", ".vote__down", ".vote--failed"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component", "interaction"]}, "/var/www/html/assets/css/_things/wiki-article.less": {"file_path": "/var/www/html/assets/css/_things/wiki-article.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a wiki article component, specifically setting the title's font size for larger screens and adding a bottom border.", "symbols": ["@desktop-min-width", ".wiki-article", ".wiki-article__title"], "ui_components": [], "css_selectors": [".wiki-article", ".wiki-article__title"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_things/wiki-lock-notice.less": {"file_path": "/var/www/html/assets/css/_things/wiki-lock-notice.less", "file_type": "css", "language": "css", "functionality": "This file defines the styling for a wiki lock notice component, which is likely used to inform users that a wiki page is locked. It includes styles for layout, borders, padding, and typography.", "symbols": [], "ui_components": ["wiki lock notice"], "css_selectors": [".wiki-lock-notice", ".wiki-lock-notice > *", ".wiki-lock-notice > :first-child", ".wiki-lock-notice > :last-child"], "imports_exports": [], "dependencies": ["--border-light", "--text-muted"], "semantic_tags": ["ui-component", "notification"]}, "/var/www/html/assets/css/_utilities/block.less": {"file_path": "/var/www/html/assets/css/_utilities/block.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class named 'block' that sets the display property to 'block' with high priority using '!important'.", "symbols": ["block"], "ui_components": [], "css_selectors": [".block"], "imports_exports": [], "dependencies": [], "semantic_tags": ["layout", "utility"]}, "/var/www/html/assets/css/_utilities/break-text.less": {"file_path": "/var/www/html/assets/css/_utilities/break-text.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS rules to ensure that long words within a block of text are broken up to prevent overflow and maintain layout integrity.", "symbols": [], "ui_components": [], "css_selectors": [".break-text"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-formatting", "layout-management"]}, "/var/www/html/assets/css/_utilities/colors.less": {"file_path": "/var/www/html/assets/css/_utilities/colors.less", "file_type": "less", "language": "less", "functionality": "This file defines utility classes for setting background and foreground colors in CSS using variables imported from another file.", "symbols": ["@background-colors", "@foreground-colors", ".bg-@{name}", ".fg-@{name}"], "ui_components": [], "css_selectors": [".bg-@{name}", ".fg-@{name}"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["utility-classes", "color-management"]}, "/var/www/html/assets/css/_utilities/font-weight.less": {"file_path": "/var/www/html/assets/css/_utilities/font-weight.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class to set the font weight to normal with high priority.", "symbols": ["fw-normal"], "ui_components": [], "css_selectors": [".fw-normal"], "imports_exports": [], "dependencies": [], "semantic_tags": []}, "/var/www/html/assets/css/_utilities/hidden.less": {"file_path": "/var/www/html/assets/css/_utilities/hidden.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class .hidden that hides elements by setting their opacity to 0, positioning them absolutely, and placing them behind other content with a negative z-index.", "symbols": [".hidden"], "ui_components": [], "css_selectors": [".hidden"], "imports_exports": [], "dependencies": [], "semantic_tags": ["utility", "visibility"]}, "/var/www/html/assets/css/_utilities/inline.less": {"file_path": "/var/www/html/assets/css/_utilities/inline.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class named 'inline' that sets the display property to 'inline', which is used to make elements appear in a line without breaking the flow.", "symbols": ["inline"], "ui_components": [], "css_selectors": [".inline"], "imports_exports": [], "dependencies": [], "semantic_tags": ["layout", "display"]}, "/var/www/html/assets/css/_utilities/monospace.less": {"file_path": "/var/www/html/assets/css/_utilities/monospace.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class named .monospace that applies a monospace font family and size to elements using custom CSS variables.", "symbols": ["--mono-font-family", "--mono-font-size"], "ui_components": [], "css_selectors": [".monospace"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-formatting", "font-style"]}, "/var/www/html/assets/css/_utilities/night-mode.less": {"file_path": "/var/www/html/assets/css/_utilities/night-mode.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS rules to manage the visibility of elements based on the night mode setting. It hides elements with the class 'dark-mode-only' in light mode and vice versa. In auto mode, it uses the system's color scheme preference to determine which elements to show.", "symbols": [], "ui_components": [], "css_selectors": [".dark-mode-only", ".light-mode-only"], "imports_exports": [], "dependencies": [], "semantic_tags": ["night-mode", "visibility", "color-scheme"]}, "/var/www/html/assets/css/_utilities/no-desktop.less": {"file_path": "/var/www/html/assets/css/_utilities/no-desktop.less", "file_type": "css", "language": "css", "functionality": "This file contains a CSS utility class that hides elements on screens wider than the defined desktop minimum width.", "symbols": ["no-desktop", "desktop-min-width"], "ui_components": [], "css_selectors": [".no-desktop"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["responsive-design", "utility-class"]}, "/var/www/html/assets/css/_utilities/no-mobile.less": {"file_path": "/var/www/html/assets/css/_utilities/no-mobile.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS that hides elements with the class 'no-mobile' on screens wider than the defined desktop minimum width.", "symbols": ["no-mobile", "desktop-min-width"], "ui_components": [], "css_selectors": [".no-mobile"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["responsive-design", "media-query"]}, "/var/www/html/assets/css/_utilities/no-select.less": {"file_path": "/var/www/html/assets/css/_utilities/no-select.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class to prevent text selection on elements.", "symbols": [], "ui_components": [], "css_selectors": [".no-select"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-selection", "utility"]}, "/var/www/html/assets/css/_utilities/no-underline.less": {"file_path": "/var/www/html/assets/css/_utilities/no-underline.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class to remove underlines from links on hover, except for elements with a specific modifier class.", "symbols": ["no-underline", "no-underline__exempt"], "ui_components": [], "css_selectors": [".no-underline", ".no-underline__exempt"], "imports_exports": [], "dependencies": [], "semantic_tags": ["link-style", "hover-effect"]}, "/var/www/html/assets/css/_utilities/no-visibility.less": {"file_path": "/var/www/html/assets/css/_utilities/no-visibility.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class that hides elements from view while still occupying space in the layout.", "symbols": [], "ui_components": [], "css_selectors": [".no-visibility"], "imports_exports": [], "dependencies": [], "semantic_tags": ["utility", "visibility"]}, "/var/www/html/assets/css/_utilities/no-wrap.less": {"file_path": "/var/www/html/assets/css/_utilities/no-wrap.less", "file_type": "css", "language": "css", "functionality": "This file defines a CSS class to prevent text from wrapping within an element.", "symbols": ["no-wrap"], "ui_components": [], "css_selectors": [".no-wrap"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-formatting", "layout"]}, "/var/www/html/assets/css/_utilities/pad.less": {"file_path": "/var/www/html/assets/css/_utilities/pad.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS classes for vertical padding utility styles.", "symbols": [], "ui_components": [], "css_selectors": [".pad-v", ".pad-v-slim"], "imports_exports": [], "dependencies": [], "semantic_tags": ["utility-styles", "padding"]}, "/var/www/html/assets/css/_utilities/page-shadow.less": {"file_path": "/var/www/html/assets/css/_utilities/page-shadow.less", "file_type": "css", "language": "less", "functionality": "This file defines a LESS mixin for applying a page shadow effect using text-shadow properties. It iterates over a range of x and y values to create a subtle shadow effect around elements with the class 'page-shadow'.", "symbols": ["page-shadow", "range", "text-shadow+"], "ui_components": [], "css_selectors": [".page-shadow"], "imports_exports": [], "dependencies": [], "semantic_tags": ["shadow-effect", "styling"]}, "/var/www/html/assets/css/_utilities/text-align.less": {"file_path": "/var/www/html/assets/css/_utilities/text-align.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS utility classes for text alignment, allowing elements to be aligned to the left, center, or right.", "symbols": [], "ui_components": [], "css_selectors": [".text-align-left", ".text-align-center", ".text-align-right"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-alignment", "utility-classes"]}, "/var/www/html/assets/css/_utilities/text-size.less": {"file_path": "/var/www/html/assets/css/_utilities/text-size.less", "file_type": "css", "language": "css", "functionality": "This file defines CSS classes for setting different text sizes in a web application.", "symbols": [], "ui_components": [], "css_selectors": [".text-sm", ".text-xs", ".text-md", ".text-lg", ".text-xl"], "imports_exports": [], "dependencies": [], "semantic_tags": ["text-sizing", "utility-classes"]}, "/var/www/html/assets/css/_vendor/hljs.less": {"file_path": "/var/www/html/assets/css/_vendor/hljs.less", "file_type": "css", "language": "css", "functionality": "This file defines basic styling for elements with the class 'hljs', setting font size, line height, margin, and padding using CSS variables.", "symbols": [], "ui_components": [], "css_selectors": [".hljs"], "imports_exports": [], "dependencies": [], "semantic_tags": []}, "/var/www/html/assets/css/_vendor/select2.less": {"file_path": "/var/www/html/assets/css/_vendor/select2.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS rules to apply dark mode styling to Select2 components based on the data attribute 'data-night-mode' and the user's preferred color scheme.", "symbols": ["select2-dark-mode-fix"], "ui_components": ["dropdown"], "css_selectors": [".select2-container"], "imports_exports": [], "dependencies": ["Select2 library"], "semantic_tags": ["ui-component", "dark-mode"]}, "/var/www/html/assets/css/_vendor/tippy.less": {"file_path": "/var/www/html/assets/css/_vendor/tippy.less", "file_type": "css", "language": "css", "functionality": "This file contains CSS styles for customizing the appearance of tooltips created using the tippy.js library, specifically for a theme named 'postmill'. It defines styles for the tooltip box, arrow, and placement-specific adjustments.", "symbols": ["--dropdown-card-bg", "--card-bg", "--border-light", "--main"], "ui_components": ["tooltip"], "css_selectors": [".tippy-box", ".tippy-arrow::before", ".tippy-svg-arrow"], "imports_exports": ["~tippy.js/dist/tippy.css"], "dependencies": ["tippy.js"], "semantic_tags": ["ui-component", "styling"]}, "/var/www/html/assets/css/_widgets/_mixins.less": {"file_path": "/var/www/html/assets/css/_widgets/_mixins.less", "file_type": "css", "language": "css", "functionality": "This file defines a set of LESS mixins for styling buttons in a web application. It includes styles for common button states such as active, small, secondary, transparent, and disabled.", "symbols": ["button-common", "button-active", "button-small", "button-secondary", "button-transparent", "button-disabled"], "ui_components": ["button"], "css_selectors": [], "imports_exports": [], "dependencies": ["--primary", "--primary-bright", "--primary-fg", "--accent", "--accent-alt", "--accent-fg"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_widgets/button.less": {"file_path": "/var/www/html/assets/css/_widgets/button.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a button component, including common styles, focus/hover effects, size variations, secondary styling, transparency, disabled state, and flexbox layout options.", "symbols": ["button-common", "button-active", "button-small", "button-secondary", "button-transparent", "button-disabled"], "ui_components": ["button"], "css_selectors": [".button", ".button--small", ".button--secondary", ".button--transparent", ".button--flex", ".button--flex > * + *"], "imports_exports": ["_mixins"], "dependencies": ["_mixins"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_widgets/clear-notification-button.less": {"file_path": "/var/www/html/assets/css/_widgets/clear-notification-button.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a clear notification button, setting its font size and positioning it to the right.", "symbols": ["--font-size", "clear-notification-button"], "ui_components": ["button"], "css_selectors": [".clear-notification-button"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_widgets/discreet-tab.less": {"file_path": "/var/www/html/assets/css/_widgets/discreet-tab.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a discreet tab UI component, including hover effects and active state styling.", "symbols": ["discreet-tab-active"], "ui_components": ["tab"], "css_selectors": [".discreet-tab", ".discreet-tab--active"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "navigation"]}, "/var/www/html/assets/css/_widgets/dropdown.less": {"file_path": "/var/www/html/assets/css/_widgets/dropdown.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a dropdown widget, including the appearance of the dropdown arrow, the visibility of the dropdown menu on different devices, and the behavior when the dropdown is expanded or hovered over.", "symbols": ["dropdown-menu"], "ui_components": ["dropdown"], "css_selectors": [".dropdown", ".dropdown__arrow", ".dropdown--expanded", ".dropdown__toggle", ".dropdown__menu", ".dropdown--mobile-only", ".dropdown--right"], "imports_exports": ["../_variables"], "dependencies": ["../_variables"], "semantic_tags": ["ui-component", "navigation"]}, "/var/www/html/assets/css/_widgets/menu-item.less": {"file_path": "/var/www/html/assets/css/_widgets/menu-item.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a menu item component, including active state styling, hover effects for links and buttons, and focus styling.", "symbols": ["menu-item", "menu-item--active", "text", "accent", "accent-fg", "accent-alt"], "ui_components": ["button"], "css_selectors": [".menu-item", ".menu-item--active", ".menu-item--active::before", ".menu-item:any-link", "input[type='submit'].menu-item", "input[type='reset'].menu-item", "input[type='button'].menu-item", "button.menu-item", ".menu-item:not(:disabled):hover", ".menu-item:focus"], "imports_exports": [], "dependencies": ["--text", "--accent", "--accent-fg", "--accent-alt"], "semantic_tags": ["navigation", "ui-component"]}, "/var/www/html/assets/css/_widgets/subscribe-button.less": {"file_path": "/var/www/html/assets/css/_widgets/subscribe-button.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a subscribe button widget, including its label, subscriber count, and different states like focus, hover, and disabled.", "symbols": ["button-common", "button-active", "button-disabled", "button-secondary"], "ui_components": ["button"], "css_selectors": [".subscribe-button", ".subscribe-button__label", ".subscribe-button__subscriber-count", ".subscribe-button__dummy-label", ".subscribe-button--subscribe &__label", ".subscribe-button--subscribe &__subscriber-count"], "imports_exports": ["_mixins"], "dependencies": ["_mixins"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/css/_widgets/tab.less": {"file_path": "/var/www/html/assets/css/_widgets/tab.less", "file_type": "css", "language": "css", "functionality": "This file defines styles for a tab component, including its active state and hover effects within a dropdown context.", "symbols": ["tab-active"], "ui_components": ["tab", "dropdown"], "css_selectors": [".tab", ".tab--active", ".no-js:root .dropdown:hover > .tab", ".js:root .dropdown--expanded > .tab"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "navigation"]}, "/var/www/html/assets/css/themes/_modern-night.less": {"file_path": "/var/www/html/assets/css/themes/_modern-night.less", "file_type": "css", "language": "less", "functionality": "This file defines a LESS mixin for a modern dark theme, setting various CSS variables for colors, backgrounds, text, borders, and shadows used in UI components.", "symbols": ["modern-dark"], "ui_components": ["card", "dropdown", "form", "notification", "site-nav"], "css_selectors": [], "imports_exports": ["~highlight.js/styles/darcula.css"], "dependencies": ["highlight.js"], "semantic_tags": ["theme", "ui-component", "color-scheme"]}, "/var/www/html/assets/css/themes/_modern.less": {"file_path": "/var/www/html/assets/css/themes/_modern.less", "file_type": "css", "language": "less", "functionality": "Defines a LESS mixin for a modern light theme, setting various CSS variables for colors, backgrounds, text, and other UI elements.", "symbols": ["modern-light"], "ui_components": ["card", "form", "notification", "site-nav"], "css_selectors": [], "imports_exports": ["~highlight.js/styles/tomorrow.css"], "dependencies": ["highlight.js"], "semantic_tags": ["theme", "ui-component", "color-scheme"]}, "/var/www/html/assets/css/themes/postmill/_dark.less": {"file_path": "/var/www/html/assets/css/themes/postmill/_dark.less", "file_type": "css", "language": "less", "functionality": "This file defines a LESS mixin for a dark theme, importing styles from _modern-night and setting various CSS variables for colors used in the theme.", "symbols": ["dark"], "ui_components": [], "css_selectors": [], "imports_exports": ["../_modern-night"], "dependencies": ["../_modern-night"], "semantic_tags": ["theme", "colors", "mixin"]}, "/var/www/html/assets/css/themes/postmill/_light.less": {"file_path": "/var/www/html/assets/css/themes/postmill/_light.less", "file_type": "css", "language": "less", "functionality": "This file defines a LESS mixin for a light theme, importing styles from a modern theme and setting color variables for primary colors and notifications.", "symbols": ["light"], "ui_components": [], "css_selectors": [], "imports_exports": ["../_modern"], "dependencies": ["../_modern"], "semantic_tags": ["theme", "color-variables"]}, "/var/www/html/assets/css/themes/postmill/index.less": {"file_path": "/var/www/html/assets/css/themes/postmill/index.less", "file_type": "css", "language": "css", "functionality": "This file manages the theme switching for a web application, applying light or dark themes based on the user's preference or system settings.", "symbols": ["light", "dark"], "ui_components": [], "css_selectors": [":root[data-night-mode=\"light\"]", ":root[data-night-mode=\"dark\"]", ":root[data-night-mode=\"auto\"]"], "imports_exports": ["_light", "_dark"], "dependencies": ["_light.less", "_dark.less"], "semantic_tags": ["theme-switching", "night-mode"]}, "/var/www/html/assets/icons/icons.svg": {"file_path": "/var/www/html/assets/icons/icons.svg", "file_type": "svg", "language": "svg", "functionality": "This SVG file contains a collection of vector icons that can be used in web applications for various UI components such as buttons, menus, and other graphical elements.", "symbols": ["attention", "block", "brush", "cancel-circled", "cancel", "ccw", "clock", "cog", "down", "envelope-open", "envelope", "file-image", "filter", "fire", "forward", "hammer", "heart", "help-circled", "home", "info-circled", "left-small", "link", "lock-open", "lock", "logout", "mail", "menu", "moon-inv", "ok-circled", "ok", "pencil", "pin-outline", "pin", "plus", "rss-squared", "search", "settings", "sort", "spinner", "star", "sun-inv", "tag", "trash", "unlink", "up", "user-times", "user", "wrench"], "ui_components": ["icon"], "css_selectors": [], "imports_exports": [], "dependencies": [], "semantic_tags": ["icons", "ui-elements", "vector-graphics"]}, "/var/www/html/assets/js/comment-count.js": {"file_path": "/var/www/html/assets/js/comment-count.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file updates the UI to display the number of new comments for submissions since the user's last visit. It also updates the local storage with the current comment counts when necessary.", "symbols": ["translator", "formatNumber", "submissionId", "currentCount", "lastCount", "newComments", "commentCount"], "ui_components": [], "css_selectors": [".js-display-new-comments", ".js-update-comment-count"], "imports_exports": ["translator from 'bazinga-translator'", "formatNumber from './lib/intl'"], "dependencies": ["bazi<PERSON>-translator", "./lib/intl"], "semantic_tags": ["ui-component", "local-storage", "translation"]}, "/var/www/html/assets/js/commenting.js": {"file_path": "/var/www/html/assets/js/commenting.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file handles the dynamic loading of comment forms via AJAX when a user clicks on a reply link. It also manages the display of these forms and error messages.", "symbols": ["createErrorMessage", "handleClick", "translator", "fetch", "ok", "escapeHtml", "parseHtml"], "ui_components": ["form"], "css_selectors": [".comment__main", ".comment-form", ".comment__error", ".comment__reply-link", ".comment__reply-link-disabled"], "imports_exports": ["translator", "fetch", "ok", "escapeHtml", "parseHtml"], "dependencies": ["./lib/http", "./lib/html", "bazi<PERSON>-translator"], "semantic_tags": ["ui-component", "ajax", "error-handling"]}, "/var/www/html/assets/js/controller/alert-controller.js": {"file_path": "/var/www/html/assets/js/controller/alert-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling alert dismissals. It includes a method to disable the close button and animate the alert's removal using a fade-out effect.", "symbols": ["close", "Controller"], "ui_components": ["button"], "css_selectors": [], "imports_exports": ["fadeOutAndRemove from '../lib/animation'", "Controller from 'stimulus'", "default export of the AlertController class"], "dependencies": ["../lib/animation", "stimulus"], "semantic_tags": ["ui-component", "alert-dismissal"]}, "/var/www/html/assets/js/controller/dialog-controller.js": {"file_path": "/var/www/html/assets/js/controller/dialog-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling dialog confirmations. It uses a value to display a confirmation dialog and prevents an event if the user cancels the dialog.", "symbols": ["confirm", "DialogController"], "ui_components": ["modal"], "css_selectors": [], "imports_exports": ["stimulus", "default"], "dependencies": ["stimulus"], "semantic_tags": ["ui-component", "interaction"]}, "/var/www/html/assets/js/controller/diff-time-controller.js": {"file_path": "/var/www/html/assets/js/controller/diff-time-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller that calculates the time difference between two dates and updates the inner text of an HTML element with a formatted string indicating whether the second date is earlier or later than the first.", "symbols": ["connect", "Controller", "locale", "timeA", "timeB", "relativeTime", "format"], "ui_components": [], "css_selectors": [], "imports_exports": {"imported_modules": ["translator from 'bazinga-translator'", "Controller from 'stimulus'", "formatDistanceStrict, isBefore, parseISO from 'date-fns'", "loadDateFnsLocale from '../lib/time'"], "exported_symbols": ["default class"]}, "dependencies": {"referenced_files": ["../lib/time"], "external_libraries": ["bazi<PERSON>-translator", "stimulus", "date-fns"]}, "semantic_tags": ["time-difference", "date-formatting", "stimulus-controller"]}, "/var/www/html/assets/js/controller/fetch-titles-controller.js": {"file_path": "/var/www/html/assets/js/controller/fetch-titles-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller that fetches the title of a webpage from a given URL and displays it in a destination input field.", "symbols": ["Controller", "fetchTitle", "initialize", "endpoint", "sourceTarget", "destinationTarget", "url", "body", "response", "title"], "ui_components": ["form", "input"], "css_selectors": [], "imports_exports": ["Controller", "fetch", "ok", "routing", "default"], "dependencies": ["stimulus", "../lib/http", "fosjsrouting"], "semantic_tags": ["webpage-title-fetcher", "ui-component"]}, "/var/www/html/assets/js/controller/file-drop-controller.js": {"file_path": "/var/www/html/assets/js/controller/file-drop-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling file drop events on an HTML element. It manages drag-and-drop interactions, specifically for image files (JPEG, GIF, PNG), and updates the UI accordingly by showing/hiding an overlay and handling file input.", "symbols": ["isFileEvent", "Controller"], "ui_components": ["overlay", "fileInput", "toggle"], "css_selectors": [".activeOverlay"], "imports_exports": [["stimulus"], ["default"]], "dependencies": ["stimulus"], "semantic_tags": ["file-upload", "drag-and-drop", "ui-component"]}, "/var/www/html/assets/js/controller/markdown-controller.js": {"file_path": "/var/www/html/assets/js/controller/markdown-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling Markdown input and rendering a live preview. It uses markdown-it for parsing Markdown and lodash-es for debouncing the preview updates.", "symbols": ["debounce", "Controller", "escapeHtml", "DEBOUNCE_RATE", "parser", "loadParser", "updatePreview", "connect", "preview"], "ui_components": ["input", "preview", "previewContainer"], "css_selectors": [], "imports_exports": {"imported_modules": ["debounce from 'lodash-es'", "Controller from 'stimulus'", "escapeHtml from '../lib/html'", "default as md from 'markdown-it'"], "exported_symbols": ["default class"]}, "dependencies": {"referenced_files": ["../lib/html"], "external_libraries": ["lodash-es", "stimulus", "markdown-it"]}, "semantic_tags": ["markdown-parser", "live-preview", "debounce"]}, "/var/www/html/assets/js/controller/relative-time-controller.js": {"file_path": "/var/www/html/assets/js/controller/relative-time-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller that calculates and displays the relative time difference between a given date and the current time, using the date-fns library for date manipulation and bazinga-translator for localization.", "symbols": ["connect", "loadDateFnsLocale", "parseISO", "formatDistanceStrict", "Controller"], "ui_components": [], "css_selectors": [], "imports_exports": {"imported_modules": ["translator from 'bazinga-translator'", "Controller from 'stimulus'", "formatDistanceStrict, parseISO from 'date-fns'", "loadDateFnsLocale from '../lib/time'"], "exported_symbols": ["default class"]}, "dependencies": {"referenced_files": ["../lib/time"], "external_libraries": ["bazi<PERSON>-translator", "stimulus", "date-fns"]}, "semantic_tags": ["date-manipulation", "localization", "stimulus-controller"]}, "/var/www/html/assets/js/controller/reload-captcha-controller.js": {"file_path": "/var/www/html/assets/js/controller/reload-captcha-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for reloading a CAPTCHA image. It updates the image's source URL to include a timestamp parameter, effectively reloading the image when the `reload` method is called.", "symbols": ["connect", "reload", "Controller"], "ui_components": ["image"], "css_selectors": [], "imports_exports": ["stimulus/Controller", "default class"], "dependencies": ["stimulus"], "semantic_tags": ["<PERSON><PERSON>a", "image-reloading", "ui-component"]}, "/var/www/html/assets/js/controller/remaining-chars-controller.js": {"file_path": "/var/www/html/assets/js/controller/remaining-chars-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller that counts the number of grapheme clusters in a text input field and validates whether the count exceeds a specified maximum value. If it does, it sets a custom validity message using a translation service.", "symbols": ["countCharacters", "Controller", "splitter", "validate", "connect", "maxValue"], "ui_components": ["form"], "css_selectors": [], "imports_exports": ["Controller from 'stimulus'", "translator from 'bazinga-translator'", "GraphemeSplitter from 'grapheme-splitter'", "default export of the controller class"], "dependencies": ["stimulus", "bazi<PERSON>-translator", "grapheme-splitter"], "semantic_tags": ["validation", "text-input", "internationalization"]}, "/var/www/html/assets/js/controller/subscribe-button-controller.js": {"file_path": "/var/www/html/assets/js/controller/subscribe-button-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling the subscribe/unsubscribe functionality on a web page. It manages the state of subscription status, updates the UI accordingly, and communicates with the server to update subscription data.", "symbols": ["subscribe", "connect", "loadingValueChanged", "subscribedValueChanged", "subscribersValueChanged", "Controller"], "ui_components": ["button", "form"], "css_selectors": [".subscribe-form", ".subscribe", ".unsubscribe"], "imports_exports": {"imported_modules": ["router", "translator", "Controller", "fetch", "ok", "formatNumber"], "exported_symbols": ["default"]}, "dependencies": {"referenced_files": ["../lib/http", "../lib/intl"], "external_libraries": ["fosjsrouting", "bazi<PERSON>-translator", "stimulus"]}, "semantic_tags": ["ui-component", "data-fetching", "state-management"]}, "/var/www/html/assets/js/controller/syntax-highlight-controller.js": {"file_path": "/var/www/html/assets/js/controller/syntax-highlight-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for syntax highlighting. It dynamically imports the appropriate language definition based on a provided language value and applies syntax highlighting to the associated DOM element using the Highlight.js library.", "symbols": ["Controller", "languageAliases", "connect", "highlight", "hljs", "definition"], "ui_components": [], "css_selectors": [], "imports_exports": ["Controller", "default"], "dependencies": ["stimulus", "highlight.js/lib/core", "highlight.js/lib/languages/*.js"], "semantic_tags": ["syntax-highlighting", "dynamic-imports", "asynchronous-operations"]}, "/var/www/html/assets/js/controller/toggle-night-mode-controller.js": {"file_path": "/var/www/html/assets/js/controller/toggle-night-mode-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for toggling night mode on a webpage. It handles user interactions to switch between light and dark themes, updates the document's data attribute accordingly, and sends an asynchronous request to update the preference on the server.", "symbols": ["initialize", "lighten", "darken", "applyPreference", "sendRequest", "Controller", "LIGHT", "DARK"], "ui_components": ["form"], "css_selectors": ["[data-night-mode]"], "imports_exports": ["router from 'fosjsrouting'", "{ fetch } from '../lib/http'", "{ Controller } from 'stimulus'", "export default class"], "dependencies": ["fosjsrouting", "../lib/http", "stimulus"], "semantic_tags": ["theme-switching", "user-preference", "asynchronous-request"]}, "/var/www/html/assets/js/controller/vote-controller.js": {"file_path": "/var/www/html/assets/js/controller/vote-controller.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a Stimulus controller for handling voting functionality on a webpage. It manages upvotes, downvotes, and displays the current score. It also handles UI updates based on the voting state, such as adding/removing CSS classes and disabling/enabling buttons.", "symbols": ["Controller", "fetch", "ok", "router", "translator", "VOTE_UP", "VOTE_NONE", "VOTE_DOWN", "up", "down", "vote", "choice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingValueChanged", "scoreValueChanged"], "ui_components": ["button", "form"], "css_selectors": [".upvoted", ".downvoted", ".loading", ".error"], "imports_exports": {"imported_modules": ["stimulus", "../lib/http", "fosjsrouting", "bazi<PERSON>-translator"], "exported_symbols": ["default"]}, "dependencies": ["stimulus", "fosjsrouting", "bazi<PERSON>-translator"], "semantic_tags": ["ui-component", "interaction", "state-management"]}, "/var/www/html/assets/js/dropdowns.js": {"file_path": "/var/www/html/assets/js/dropdowns.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file enhances the accessibility and interactivity of dropdown menus on a webpage. It handles toggling dropdown visibility, managing focus within dropdowns, and responding to keyboard events to navigate through dropdown items.", "symbols": ["toggle", "getFocusableElements", "moveInDropdown", "handleKeyDown", "FOCUSABLE_ELEMENTS", "MENU_ACTIONS"], "ui_components": ["dropdown", "button", "link"], "css_selectors": [".dropdown--expanded", ".dropdown__toggle", ".dropdown__menu a[href]", ".dropdown__menu button:not([disabled])", ".dropdown__menu input:not([type=\"hidden\"]):not([disabled])", ".dropdown__menu button[type=\"submit\"]", ".dropdown__menu button:not([type])"], "imports_exports": [], "dependencies": [], "semantic_tags": ["ui-component", "accessibility", "interactivity"]}, "/var/www/html/assets/js/lib/html.js": {"file_path": "/var/www/html/assets/js/lib/html.js", "file_type": "js", "language": "javascript", "functionality": "This file contains utility functions for parsing HTML strings into DocumentFragments and escaping HTML text to prevent XSS attacks.", "symbols": ["parseHtml", "escapeHtml"], "ui_components": [], "css_selectors": [], "imports_exports": ["exported_symbols"], "dependencies": [], "semantic_tags": []}, "/var/www/html/assets/js/lib/http.js": {"file_path": "/var/www/html/assets/js/lib/http.js", "file_type": "js", "language": "javascript", "functionality": "This file provides utility functions for making HTTP requests and handling responses. It includes a `fetch` function that wraps the native `window.fetch` method with additional options handling and an `ok` function to check if a response is successful.", "symbols": ["fetch", "ok"], "ui_components": [], "css_selectors": [], "imports_exports": {"imported_modules": ["isObject from lodash-es"], "exported_symbols": ["fetch", "ok"]}, "dependencies": ["lodash-es"], "semantic_tags": ["http-request", "response-handling"]}, "/var/www/html/assets/js/lib/icon.js": {"file_path": "/var/www/html/assets/js/lib/icon.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file defines a function to generate an HTML string for rendering an icon using an SVG sprite sheet. It takes an icon name and optional options for extra classes.", "symbols": ["icon"], "ui_components": ["icon"], "css_selectors": [".icon"], "imports_exports": {"imported_modules": ["../../icons/icons.svg", "./html"], "exported_symbols": ["icon"]}, "dependencies": ["../../icons/icons.svg", "./html"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/js/lib/intl.js": {"file_path": "/var/www/html/assets/js/lib/intl.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file provides a function to format numbers according to the language specified in the document's HTML element. It uses the Intl.NumberFormat API for formatting.", "symbols": ["formatNumber", "numberF<PERSON>atter"], "ui_components": [], "css_selectors": [], "imports_exports": ["exported_symbols", "formatNumber"], "dependencies": ["external_libraries", "Intl.NumberFormat"], "semantic_tags": []}, "/var/www/html/assets/js/lib/time.js": {"file_path": "/var/www/html/assets/js/lib/time.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file exports an asynchronous function that attempts to load a locale for the date-fns library based on the provided language code. If the specified language is not found, it tries to load a more generic version of the language (e.g., 'en-US' becomes 'en'). If all attempts fail, it throws an error.", "symbols": ["loadDateFnsLocale", "lang", "locale", "newLang", "e", "i"], "ui_components": [], "css_selectors": [], "imports_exports": ["date-fns/locale/${lang}/index.js", "loadDateFnsLocale"], "dependencies": ["date-fns/locale/${lang}/index.js"], "semantic_tags": []}, "/var/www/html/assets/js/main.js": {"file_path": "/var/www/html/assets/js/main.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file initializes a Stimulus application, loads controller definitions from a specified directory, and imports several other JavaScript modules that likely handle specific UI functionalities such as comment counting, commenting, dropdowns, select2 integration, form unloading, and user popper.", "symbols": ["Application", "definitionsFromContext", "application", "context"], "ui_components": ["dropdown", "form"], "css_selectors": [], "imports_exports": {"imported_modules": ["stimulus/Application", "stimulus/webpack-helpers", "./controller/*.js", "./comment-count", "./commenting", "./dropdowns", "./select2", "./unload-forms", "./user-popper"], "exported_symbols": []}, "dependencies": {"referenced_files": ["./controller/*.js", "./comment-count", "./commenting", "./dropdowns", "./select2", "./unload-forms", "./user-popper"], "external_libraries": ["stimulus"]}, "semantic_tags": ["ui-component", "javascript-module"]}, "/var/www/html/assets/js/select2.js": {"file_path": "/var/www/html/assets/js/select2.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file initializes Select2 dropdowns on elements with the class 'select2'. It customizes the template for displaying forum selectors based on data attributes, including icons for subscribed and featured forums.", "symbols": ["forumSelectorTemplate"], "ui_components": ["dropdown"], "css_selectors": [".select2", ".flex__grow", ".flex"], "imports_exports": ["./lib/icon", "j<PERSON>y", "select2", "select2/dist/css/select2.css"], "dependencies": ["./lib/icon", "j<PERSON>y", "select2", "select2/dist/css/select2.css"], "semantic_tags": ["ui-component"]}, "/var/www/html/assets/js/unload-forms.js": {"file_path": "/var/www/html/assets/js/unload-forms.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file is designed to warn users when they attempt to navigate away from a webpage without saving changes made in specific form fields. It listens for changes in form fields and prompts the user before unloading the page if any changes have been detected.", "symbols": ["onBeforeUnload", "onChange", "onSubmit", "FIELDS", "widgetsChanged"], "ui_components": ["form"], "css_selectors": [".form textarea", ".form input:not([type])", ".form input[type=\"text\"]", ".form input[type=\"url\"]", ".form input[type=\"file\"]"], "imports_exports": [], "dependencies": [], "semantic_tags": ["form-handling", "user-warning", "event-listening"]}, "/var/www/html/assets/js/user-popper.js": {"file_path": "/var/www/html/assets/js/user-popper.js", "file_type": "js", "language": "javascript", "functionality": "This JavaScript file initializes tooltips for user profile links using the tippy.js library. It fetches HTML content for these tooltips via AJAX requests and caches the responses to improve performance.", "symbols": ["fetchPopperHtml", "BASE_URL", "TARGETS", "htmlCache", "elements"], "ui_components": ["tooltip"], "css_selectors": [".js-poppers-enabled .submission__submitter", ".js-poppers-enabled .submission__body a[href*=\"/user/\"]", ".js-poppers-enabled .comment__body a[href*=\"/user/\"]", ".js-poppers-enabled .comment__context a[href*=\"/user/\"]", ".js-poppers-enabled .comment__info a[href*=\"/user/\"]", ".js-poppers-enabled .message__head a[href*=\"/user/\"]", ".js-poppers-enabled .message__body a[href*=\"/user/\"]"], "imports_exports": ["tippy", "router", "fetch", "ok"], "dependencies": ["tippy.js", "fosjsrouting", "./lib/http"], "semantic_tags": ["ui-component", "ajax", "caching"]}}, "language_stats": {"css": 73, "less": 8, "svg": 1, "javascript": 25}, "symbol_map": {"dropdown-card": ["/var/www/html/assets/css/_card/dropdown-card.less"], ".form-control-common": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-text": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-focus": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-disabled": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-readonly": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-undecorated": ["/var/www/html/assets/css/_form/_mixins.less"], ".form-control-undecorated-focus": ["/var/www/html/assets/css/_form/_mixins.less"], "form-control-common": ["/var/www/html/assets/css/_form/decorated-form-control.less"], "form-control-focus": ["/var/www/html/assets/css/_form/decorated-form-control.less"], "form-control-disabled": ["/var/www/html/assets/css/_form/decorated-form-control.less"], "form-control-readonly": ["/var/www/html/assets/css/_form/decorated-form-control.less"], "form-control-text": ["/var/www/html/assets/css/_form/decorated-form-control.less"], "fieldset": ["/var/www/html/assets/css/_form/fieldset.less"], "legend": ["/var/www/html/assets/css/_form/fieldset.less"], "form-flex": ["/var/www/html/assets/css/_form/form-flex.less"], "form-flex--stretch": ["/var/www/html/assets/css/_form/form-flex.less"], "form-flex--single-line": ["/var/www/html/assets/css/_form/form-flex.less"], "form-flex__align": ["/var/www/html/assets/css/_form/form-flex.less"], "form-flex--no-collapse": ["/var/www/html/assets/css/_form/form-flex.less"], "form-tabs": ["/var/www/html/assets/css/_form/form-tabs.less"], "tab-active": ["/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_widgets/tab.less"], "discreet-tab-active": ["/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_widgets/discreet-tab.less"], "form": ["/var/www/html/assets/css/_form/form.less"], "form__row": ["/var/www/html/assets/css/_form/form.less"], "form__button-row": ["/var/www/html/assets/css/_form/form.less"], "desktop-min-width": ["/var/www/html/assets/css/_form/form.less", "/var/www/html/assets/css/_layout/site-content.less", "/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_utilities/no-desktop.less", "/var/www/html/assets/css/_utilities/no-mobile.less"], "markdown-preview": ["/var/www/html/assets/css/_form/markdown-preview.less"], "markdown-preview__title": ["/var/www/html/assets/css/_form/markdown-preview.less"], "markdown-preview__inner": ["/var/www/html/assets/css/_form/markdown-preview.less"], "unstylable-widget": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "caret": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "check": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "circle": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "fg-grey": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "font-size": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "shrink": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "--font-size": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_widgets/clear-notification-button.less"], "--line-height": ["/var/www/html/assets/css/_global.less"], "--mono-font-family": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_utilities/monospace.less"], "--mono-font-size": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_utilities/monospace.less"], "--bg-page": ["/var/www/html/assets/css/_global.less"], "--bg-image": ["/var/www/html/assets/css/_global.less"], "--text": ["/var/www/html/assets/css/_global.less"], "--font-family": ["/var/www/html/assets/css/_global.less"], "--primary-alt": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/site-nav.less"], "--primary-fg": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/site-nav.less"], "--link": ["/var/www/html/assets/css/_global.less"], "--border": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_things/table-of-contents.less"], "--text-muted": ["/var/www/html/assets/css/_global.less"], "--border-light": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_things/border-list.less", "/var/www/html/assets/css/_vendor/tippy.less"], "content-container": ["/var/www/html/assets/css/_layout/content-container.less"], "max-content-width": ["/var/www/html/assets/css/_layout/content-container.less"], "flow-mixin": ["/var/www/html/assets/css/_layout/flow.less"], "flow": ["/var/www/html/assets/css/_layout/flow.less"], "flow-slim": ["/var/www/html/assets/css/_layout/flow.less"], "sidebar": ["/var/www/html/assets/css/_layout/sidebar.less"], "sidebar__section": ["/var/www/html/assets/css/_layout/sidebar.less"], "sidebar__title": ["/var/www/html/assets/css/_layout/sidebar.less"], "sidebar__no-padding": ["/var/www/html/assets/css/_layout/sidebar.less"], "site-accessibility-nav": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less"], "link": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less", "/var/www/html/assets/icons/icons.svg"], "site-alerts": ["/var/www/html/assets/css/_layout/site-alerts.less"], "site-alerts-slide": ["/var/www/html/assets/css/_layout/site-alerts.less"], "site-alerts-slide-no-js": ["/var/www/html/assets/css/_layout/site-alerts.less"], "site-alerts-fade": ["/var/www/html/assets/css/_layout/site-alerts.less"], "site-alerts-fade-no-js": ["/var/www/html/assets/css/_layout/site-alerts.less"], "site-content": ["/var/www/html/assets/css/_layout/site-content.less"], "site-content__body": ["/var/www/html/assets/css/_layout/site-content.less"], "site-content__sidebar": ["/var/www/html/assets/css/_layout/site-content.less"], "site-footer": ["/var/www/html/assets/css/_layout/site-footer.less"], "--site-nav-bg": ["/var/www/html/assets/css/_layout/site-nav.less"], "--primary": ["/var/www/html/assets/css/_layout/site-nav.less"], "--notification": ["/var/www/html/assets/css/_layout/site-nav.less"], "@import": ["/var/www/html/assets/css/_layout/site-nav.less"], "text-flow-mixin": ["/var/www/html/assets/css/_layout/text-flow.less"], "text-flow": ["/var/www/html/assets/css/_layout/text-flow.less"], "text-flow-slim": ["/var/www/html/assets/css/_layout/text-flow.less"], "unbuttonize": ["/var/www/html/assets/css/_resets/unbuttonize.less"], "undecorate": ["/var/www/html/assets/css/_resets/undecorate.less"], "unheaderize": ["/var/www/html/assets/css/_resets/unheaderize.less"], "unlistify": ["/var/www/html/assets/css/_resets/unlistify.less"], "@border-thin": ["/var/www/html/assets/css/_things/comment.less"], "@border-thick": ["/var/www/html/assets/css/_things/comment.less"], "@vote-width": ["/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/css/_things/vote.less"], "drop-zone": ["/var/www/html/assets/css/_things/drop-zone.less"], "drop-zone--active": ["/var/www/html/assets/css/_things/drop-zone.less"], "flair": ["/var/www/html/assets/css/_things/flair.less"], "flair__label": ["/var/www/html/assets/css/_things/flair.less"], "flair__buttons": ["/var/www/html/assets/css/_things/flair.less"], "flair__button": ["/var/www/html/assets/css/_things/flair.less"], "flex": ["/var/www/html/assets/css/_things/flex.less"], "flex--align-center": ["/var/www/html/assets/css/_things/flex.less"], "flex--no-wrap": ["/var/www/html/assets/css/_things/flex.less"], "flex--column": ["/var/www/html/assets/css/_things/flex.less"], "flex--row": ["/var/www/html/assets/css/_things/flex.less"], "flex--inline": ["/var/www/html/assets/css/_things/flex.less"], "flex--guttered": ["/var/www/html/assets/css/_things/flex.less"], "flex--slim-gutters": ["/var/www/html/assets/css/_things/flex.less"], "flex__grow": ["/var/www/html/assets/css/_things/flex.less"], "flex__shrink": ["/var/www/html/assets/css/_things/flex.less"], "hideable": ["/var/www/html/assets/css/_things/hideable.less"], "checkbox": ["/var/www/html/assets/css/_things/hideable.less"], "toggle": ["/var/www/html/assets/css/_things/hideable.less", "/var/www/html/assets/js/dropdowns.js"], "indicator": ["/var/www/html/assets/css/_things/hideable.less"], "icon-spin": ["/var/www/html/assets/css/_things/icon.less"], "message": ["/var/www/html/assets/css/_things/message.less"], "message__body": ["/var/www/html/assets/css/_things/message.less"], "submission-meta": ["/var/www/html/assets/css/_things/submission-meta.less"], "short-url": ["/var/www/html/assets/css/_things/submission-meta.less"], "submission": ["/var/www/html/assets/css/_things/submission.less"], "submission__row": ["/var/www/html/assets/css/_things/submission.less"], "submission__inner": ["/var/www/html/assets/css/_things/submission.less"], "submission__title-row": ["/var/www/html/assets/css/_things/submission.less"], "submission__link": ["/var/www/html/assets/css/_things/submission.less"], "submission__host": ["/var/www/html/assets/css/_things/submission.less"], "submission__content": ["/var/www/html/assets/css/_things/submission.less"], "submission__image-link": ["/var/www/html/assets/css/_things/submission.less"], "submission__image": ["/var/www/html/assets/css/_things/submission.less"], "submission__flairs": ["/var/www/html/assets/css/_things/submission.less"], "submission__thumb": ["/var/www/html/assets/css/_things/submission.less"], ".table-of-contents": ["/var/www/html/assets/css/_things/table-of-contents.less"], "table": ["/var/www/html/assets/css/_things/table.less"], "table__shrink": ["/var/www/html/assets/css/_things/table.less"], "accent-alt": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_widgets/menu-item.less"], "border-light": ["/var/www/html/assets/css/_things/table.less"], "accent": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_widgets/menu-item.less"], "accent-fg": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_widgets/menu-item.less"], "user-flag": ["/var/www/html/assets/css/_things/user-flag.less"], ".vote": ["/var/www/html/assets/css/_things/vote.less"], ".vote--loading": ["/var/www/html/assets/css/_things/vote.less"], ".vote__net-score": ["/var/www/html/assets/css/_things/vote.less"], ".vote__spinner": ["/var/www/html/assets/css/_things/vote.less"], ".vote__button": ["/var/www/html/assets/css/_things/vote.less"], ".vote--user-upvoted": ["/var/www/html/assets/css/_things/vote.less"], ".vote__up": ["/var/www/html/assets/css/_things/vote.less"], ".vote--user-downvoted": ["/var/www/html/assets/css/_things/vote.less"], ".vote__down": ["/var/www/html/assets/css/_things/vote.less"], ".vote--failed": ["/var/www/html/assets/css/_things/vote.less"], "@desktop-min-width": ["/var/www/html/assets/css/_things/wiki-article.less"], ".wiki-article": ["/var/www/html/assets/css/_things/wiki-article.less"], ".wiki-article__title": ["/var/www/html/assets/css/_things/wiki-article.less"], "block": ["/var/www/html/assets/css/_utilities/block.less", "/var/www/html/assets/icons/icons.svg"], "@background-colors": ["/var/www/html/assets/css/_utilities/colors.less"], "@foreground-colors": ["/var/www/html/assets/css/_utilities/colors.less"], ".bg-@{name}": ["/var/www/html/assets/css/_utilities/colors.less"], ".fg-@{name}": ["/var/www/html/assets/css/_utilities/colors.less"], "fw-normal": ["/var/www/html/assets/css/_utilities/font-weight.less"], ".hidden": ["/var/www/html/assets/css/_utilities/hidden.less"], "inline": ["/var/www/html/assets/css/_utilities/inline.less"], "no-desktop": ["/var/www/html/assets/css/_utilities/no-desktop.less"], "no-mobile": ["/var/www/html/assets/css/_utilities/no-mobile.less"], "no-underline": ["/var/www/html/assets/css/_utilities/no-underline.less"], "no-underline__exempt": ["/var/www/html/assets/css/_utilities/no-underline.less"], "no-wrap": ["/var/www/html/assets/css/_utilities/no-wrap.less"], "page-shadow": ["/var/www/html/assets/css/_utilities/page-shadow.less"], "range": ["/var/www/html/assets/css/_utilities/page-shadow.less"], "text-shadow+": ["/var/www/html/assets/css/_utilities/page-shadow.less"], "select2-dark-mode-fix": ["/var/www/html/assets/css/_vendor/select2.less"], "--dropdown-card-bg": ["/var/www/html/assets/css/_vendor/tippy.less"], "--card-bg": ["/var/www/html/assets/css/_vendor/tippy.less"], "--main": ["/var/www/html/assets/css/_vendor/tippy.less"], "button-common": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/subscribe-button.less"], "button-active": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/subscribe-button.less"], "button-small": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less"], "button-secondary": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/subscribe-button.less"], "button-transparent": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less"], "button-disabled": ["/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/subscribe-button.less"], "clear-notification-button": ["/var/www/html/assets/css/_widgets/clear-notification-button.less"], "dropdown-menu": ["/var/www/html/assets/css/_widgets/dropdown.less"], "menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], "menu-item--active": ["/var/www/html/assets/css/_widgets/menu-item.less"], "text": ["/var/www/html/assets/css/_widgets/menu-item.less"], "modern-dark": ["/var/www/html/assets/css/themes/_modern-night.less"], "modern-light": ["/var/www/html/assets/css/themes/_modern.less"], "dark": ["/var/www/html/assets/css/themes/postmill/_dark.less", "/var/www/html/assets/css/themes/postmill/index.less"], "light": ["/var/www/html/assets/css/themes/postmill/_light.less", "/var/www/html/assets/css/themes/postmill/index.less"], "attention": ["/var/www/html/assets/icons/icons.svg"], "brush": ["/var/www/html/assets/icons/icons.svg"], "cancel-circled": ["/var/www/html/assets/icons/icons.svg"], "cancel": ["/var/www/html/assets/icons/icons.svg"], "ccw": ["/var/www/html/assets/icons/icons.svg"], "clock": ["/var/www/html/assets/icons/icons.svg"], "cog": ["/var/www/html/assets/icons/icons.svg"], "down": ["/var/www/html/assets/icons/icons.svg", "/var/www/html/assets/js/controller/vote-controller.js"], "envelope-open": ["/var/www/html/assets/icons/icons.svg"], "envelope": ["/var/www/html/assets/icons/icons.svg"], "file-image": ["/var/www/html/assets/icons/icons.svg"], "filter": ["/var/www/html/assets/icons/icons.svg"], "fire": ["/var/www/html/assets/icons/icons.svg"], "forward": ["/var/www/html/assets/icons/icons.svg"], "hammer": ["/var/www/html/assets/icons/icons.svg"], "heart": ["/var/www/html/assets/icons/icons.svg"], "help-circled": ["/var/www/html/assets/icons/icons.svg"], "home": ["/var/www/html/assets/icons/icons.svg"], "info-circled": ["/var/www/html/assets/icons/icons.svg"], "left-small": ["/var/www/html/assets/icons/icons.svg"], "lock-open": ["/var/www/html/assets/icons/icons.svg"], "lock": ["/var/www/html/assets/icons/icons.svg"], "logout": ["/var/www/html/assets/icons/icons.svg"], "mail": ["/var/www/html/assets/icons/icons.svg"], "menu": ["/var/www/html/assets/icons/icons.svg"], "moon-inv": ["/var/www/html/assets/icons/icons.svg"], "ok-circled": ["/var/www/html/assets/icons/icons.svg"], "ok": ["/var/www/html/assets/icons/icons.svg", "/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/vote-controller.js", "/var/www/html/assets/js/lib/http.js"], "pencil": ["/var/www/html/assets/icons/icons.svg"], "pin-outline": ["/var/www/html/assets/icons/icons.svg"], "pin": ["/var/www/html/assets/icons/icons.svg"], "plus": ["/var/www/html/assets/icons/icons.svg"], "rss-squared": ["/var/www/html/assets/icons/icons.svg"], "search": ["/var/www/html/assets/icons/icons.svg"], "settings": ["/var/www/html/assets/icons/icons.svg"], "sort": ["/var/www/html/assets/icons/icons.svg"], "spinner": ["/var/www/html/assets/icons/icons.svg"], "star": ["/var/www/html/assets/icons/icons.svg"], "sun-inv": ["/var/www/html/assets/icons/icons.svg"], "tag": ["/var/www/html/assets/icons/icons.svg"], "trash": ["/var/www/html/assets/icons/icons.svg"], "unlink": ["/var/www/html/assets/icons/icons.svg"], "up": ["/var/www/html/assets/icons/icons.svg", "/var/www/html/assets/js/controller/vote-controller.js"], "user-times": ["/var/www/html/assets/icons/icons.svg"], "user": ["/var/www/html/assets/icons/icons.svg"], "wrench": ["/var/www/html/assets/icons/icons.svg"], "translator": ["/var/www/html/assets/js/comment-count.js", "/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/vote-controller.js"], "formatNumber": ["/var/www/html/assets/js/comment-count.js", "/var/www/html/assets/js/lib/intl.js"], "submissionId": ["/var/www/html/assets/js/comment-count.js"], "currentCount": ["/var/www/html/assets/js/comment-count.js"], "lastCount": ["/var/www/html/assets/js/comment-count.js"], "newComments": ["/var/www/html/assets/js/comment-count.js"], "commentCount": ["/var/www/html/assets/js/comment-count.js"], "createErrorMessage": ["/var/www/html/assets/js/commenting.js"], "handleClick": ["/var/www/html/assets/js/commenting.js"], "fetch": ["/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/vote-controller.js", "/var/www/html/assets/js/lib/http.js"], "escapeHtml": ["/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/markdown-controller.js", "/var/www/html/assets/js/lib/html.js"], "parseHtml": ["/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/lib/html.js"], "close": ["/var/www/html/assets/js/controller/alert-controller.js"], "Controller": ["/var/www/html/assets/js/controller/alert-controller.js", "/var/www/html/assets/js/controller/diff-time-controller.js", "/var/www/html/assets/js/controller/fetch-titles-controller.js", "/var/www/html/assets/js/controller/file-drop-controller.js", "/var/www/html/assets/js/controller/markdown-controller.js", "/var/www/html/assets/js/controller/relative-time-controller.js", "/var/www/html/assets/js/controller/reload-captcha-controller.js", "/var/www/html/assets/js/controller/remaining-chars-controller.js", "/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/syntax-highlight-controller.js", "/var/www/html/assets/js/controller/toggle-night-mode-controller.js", "/var/www/html/assets/js/controller/vote-controller.js"], "confirm": ["/var/www/html/assets/js/controller/dialog-controller.js"], "DialogController": ["/var/www/html/assets/js/controller/dialog-controller.js"], "connect": ["/var/www/html/assets/js/controller/diff-time-controller.js", "/var/www/html/assets/js/controller/markdown-controller.js", "/var/www/html/assets/js/controller/relative-time-controller.js", "/var/www/html/assets/js/controller/reload-captcha-controller.js", "/var/www/html/assets/js/controller/remaining-chars-controller.js", "/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "locale": ["/var/www/html/assets/js/controller/diff-time-controller.js", "/var/www/html/assets/js/lib/time.js"], "timeA": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "timeB": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "relativeTime": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "format": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "fetchTitle": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "initialize": ["/var/www/html/assets/js/controller/fetch-titles-controller.js", "/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "endpoint": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "sourceTarget": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "destinationTarget": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "url": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "body": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "response": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "title": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "isFileEvent": ["/var/www/html/assets/js/controller/file-drop-controller.js"], "debounce": ["/var/www/html/assets/js/controller/markdown-controller.js"], "DEBOUNCE_RATE": ["/var/www/html/assets/js/controller/markdown-controller.js"], "parser": ["/var/www/html/assets/js/controller/markdown-controller.js"], "loadParser": ["/var/www/html/assets/js/controller/markdown-controller.js"], "updatePreview": ["/var/www/html/assets/js/controller/markdown-controller.js"], "preview": ["/var/www/html/assets/js/controller/markdown-controller.js"], "loadDateFnsLocale": ["/var/www/html/assets/js/controller/relative-time-controller.js", "/var/www/html/assets/js/lib/time.js"], "parseISO": ["/var/www/html/assets/js/controller/relative-time-controller.js"], "formatDistanceStrict": ["/var/www/html/assets/js/controller/relative-time-controller.js"], "reload": ["/var/www/html/assets/js/controller/reload-captcha-controller.js"], "countCharacters": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "splitter": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "validate": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "maxValue": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "subscribe": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], "loadingValueChanged": ["/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/vote-controller.js"], "subscribedValueChanged": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], "subscribersValueChanged": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], "languageAliases": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "highlight": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "hljs": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "definition": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "lighten": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "darken": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "applyPreference": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "sendRequest": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "LIGHT": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "DARK": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "router": ["/var/www/html/assets/js/controller/vote-controller.js"], "VOTE_UP": ["/var/www/html/assets/js/controller/vote-controller.js"], "VOTE_NONE": ["/var/www/html/assets/js/controller/vote-controller.js"], "VOTE_DOWN": ["/var/www/html/assets/js/controller/vote-controller.js"], "vote": ["/var/www/html/assets/js/controller/vote-controller.js"], "choiceValueChanged": ["/var/www/html/assets/js/controller/vote-controller.js"], "errorValueChanged": ["/var/www/html/assets/js/controller/vote-controller.js"], "scoreValueChanged": ["/var/www/html/assets/js/controller/vote-controller.js"], "getFocusableElements": ["/var/www/html/assets/js/dropdowns.js"], "moveInDropdown": ["/var/www/html/assets/js/dropdowns.js"], "handleKeyDown": ["/var/www/html/assets/js/dropdowns.js"], "FOCUSABLE_ELEMENTS": ["/var/www/html/assets/js/dropdowns.js"], "MENU_ACTIONS": ["/var/www/html/assets/js/dropdowns.js"], "icon": ["/var/www/html/assets/js/lib/icon.js"], "numberFormatter": ["/var/www/html/assets/js/lib/intl.js"], "lang": ["/var/www/html/assets/js/lib/time.js"], "newLang": ["/var/www/html/assets/js/lib/time.js"], "e": ["/var/www/html/assets/js/lib/time.js"], "i": ["/var/www/html/assets/js/lib/time.js"], "Application": ["/var/www/html/assets/js/main.js"], "definitionsFromContext": ["/var/www/html/assets/js/main.js"], "application": ["/var/www/html/assets/js/main.js"], "context": ["/var/www/html/assets/js/main.js"], "forumSelectorTemplate": ["/var/www/html/assets/js/select2.js"], "onBeforeUnload": ["/var/www/html/assets/js/unload-forms.js"], "onChange": ["/var/www/html/assets/js/unload-forms.js"], "onSubmit": ["/var/www/html/assets/js/unload-forms.js"], "FIELDS": ["/var/www/html/assets/js/unload-forms.js"], "widgetsChanged": ["/var/www/html/assets/js/unload-forms.js"], "fetchPopperHtml": ["/var/www/html/assets/js/user-popper.js"], "BASE_URL": ["/var/www/html/assets/js/user-popper.js"], "TARGETS": ["/var/www/html/assets/js/user-popper.js"], "htmlCache": ["/var/www/html/assets/js/user-popper.js"], "elements": ["/var/www/html/assets/js/user-popper.js"]}, "ui_component_map": {"dropdown": ["/var/www/html/assets/css/_card/dropdown-card.less", "/var/www/html/assets/css/_form/unstylable-widget.less", "/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_vendor/select2.less", "/var/www/html/assets/css/_widgets/dropdown.less", "/var/www/html/assets/css/_widgets/tab.less", "/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/js/dropdowns.js", "/var/www/html/assets/js/main.js", "/var/www/html/assets/js/select2.js"], "form": ["/var/www/html/assets/css/_form/_mixins.less", "/var/www/html/assets/css/_form/compound-form-widget.less", "/var/www/html/assets/css/_form/decorated-form-control.less", "/var/www/html/assets/css/_form/fieldset.less", "/var/www/html/assets/css/_form/form-error-list.less", "/var/www/html/assets/css/_form/form-flex.less", "/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_form/form.less", "/var/www/html/assets/css/_form/formatting-help.less", "/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less", "/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/fetch-titles-controller.js", "/var/www/html/assets/js/controller/remaining-chars-controller.js", "/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/toggle-night-mode-controller.js", "/var/www/html/assets/js/controller/vote-controller.js", "/var/www/html/assets/js/main.js", "/var/www/html/assets/js/unload-forms.js"], "tabs": ["/var/www/html/assets/css/_form/form-tabs.less"], "button": ["/var/www/html/assets/css/_form/form.less", "/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_resets/unbuttonize.less", "/var/www/html/assets/css/_things/flair.less", "/var/www/html/assets/css/_things/vote.less", "/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/clear-notification-button.less", "/var/www/html/assets/css/_widgets/menu-item.less", "/var/www/html/assets/css/_widgets/subscribe-button.less", "/var/www/html/assets/js/controller/alert-controller.js", "/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/vote-controller.js", "/var/www/html/assets/js/dropdowns.js"], "markdown preview": ["/var/www/html/assets/css/_form/markdown-preview.less"], "checkbox": ["/var/www/html/assets/css/_form/unstylable-widget.less", "/var/www/html/assets/css/_things/hideable.less"], "radio": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "blockquote": ["/var/www/html/assets/css/_global.less"], "pre": ["/var/www/html/assets/css/_global.less"], "code": ["/var/www/html/assets/css/_global.less"], "hr": ["/var/www/html/assets/css/_global.less"], "summary": ["/var/www/html/assets/css/_global.less"], "sidebar": ["/var/www/html/assets/css/_layout/sidebar.less", "/var/www/html/assets/css/_layout/site-content.less"], "link": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less", "/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_things/submission.less", "/var/www/html/assets/js/dropdowns.js"], "alert": ["/var/www/html/assets/css/_layout/site-alerts.less", "/var/www/html/assets/css/_things/alert.less"], "dismiss button": ["/var/www/html/assets/css/_layout/site-alerts.less"], "body": ["/var/www/html/assets/css/_layout/site-content.less"], "footer": ["/var/www/html/assets/css/_layout/site-footer.less"], "search-input": ["/var/www/html/assets/css/_layout/site-nav.less"], "search-label": ["/var/www/html/assets/css/_layout/site-nav.less"], "search-row": ["/var/www/html/assets/css/_layout/site-nav.less"], "list": ["/var/www/html/assets/css/_layout/site-nav.less"], "item": ["/var/www/html/assets/css/_layout/site-nav.less"], "definition-list": ["/var/www/html/assets/css/_things/definition-list.less"], "drop zone": ["/var/www/html/assets/css/_things/drop-zone.less"], "empty state": ["/var/www/html/assets/css/_things/empty.less"], "permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "toggle": ["/var/www/html/assets/css/_things/hideable.less", "/var/www/html/assets/js/controller/file-drop-controller.js"], "indicator": ["/var/www/html/assets/css/_things/hideable.less"], "icon": ["/var/www/html/assets/css/_things/icon.less", "/var/www/html/assets/icons/icons.svg", "/var/www/html/assets/js/lib/icon.js"], "message": ["/var/www/html/assets/css/_things/message.less"], "image": ["/var/www/html/assets/css/_things/submission.less", "/var/www/html/assets/js/controller/reload-captcha-controller.js"], "text": ["/var/www/html/assets/css/_things/submission.less"], "table of contents": ["/var/www/html/assets/css/_things/table-of-contents.less"], "table": ["/var/www/html/assets/css/_things/table.less"], "wiki lock notice": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], "tooltip": ["/var/www/html/assets/css/_vendor/tippy.less", "/var/www/html/assets/js/user-popper.js"], "tab": ["/var/www/html/assets/css/_widgets/discreet-tab.less", "/var/www/html/assets/css/_widgets/tab.less"], "card": ["/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less"], "notification": ["/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less"], "site-nav": ["/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less"], "modal": ["/var/www/html/assets/js/controller/dialog-controller.js"], "input": ["/var/www/html/assets/js/controller/fetch-titles-controller.js", "/var/www/html/assets/js/controller/markdown-controller.js"], "overlay": ["/var/www/html/assets/js/controller/file-drop-controller.js"], "fileInput": ["/var/www/html/assets/js/controller/file-drop-controller.js"], "preview": ["/var/www/html/assets/js/controller/markdown-controller.js"], "previewContainer": ["/var/www/html/assets/js/controller/markdown-controller.js"]}, "css_selector_map": {".dropdown-card": ["/var/www/html/assets/css/_card/dropdown-card.less"], ".compound-form-widget": ["/var/www/html/assets/css/_form/compound-form-widget.less"], ".compound-form-widget > *": ["/var/www/html/assets/css/_form/compound-form-widget.less"], ".decorated-form-control": ["/var/www/html/assets/css/_form/decorated-form-control.less"], ".decorated-form-control--disabled": ["/var/www/html/assets/css/_form/decorated-form-control.less"], ".decorated-form-control--read-only": ["/var/www/html/assets/css/_form/decorated-form-control.less"], ".decorated-form-control--text": ["/var/www/html/assets/css/_form/decorated-form-control.less"], ".decorated-form-control__widget": ["/var/www/html/assets/css/_form/decorated-form-control.less"], ".fieldset": ["/var/www/html/assets/css/_form/fieldset.less"], ".fieldset > legend": ["/var/www/html/assets/css/_form/fieldset.less"], ".form-error-list": ["/var/www/html/assets/css/_form/form-error-list.less"], ".form-error-list > li": ["/var/www/html/assets/css/_form/form-error-list.less"], ".form-flex": ["/var/www/html/assets/css/_form/form-flex.less"], ".form-flex--stretch": ["/var/www/html/assets/css/_form/form-flex.less"], ".form-flex--single-line": ["/var/www/html/assets/css/_form/form-flex.less"], ".form-flex__align": ["/var/www/html/assets/css/_form/form-flex.less"], ".form-flex--no-collapse": ["/var/www/html/assets/css/_form/form-flex.less"], ".form-tabs": ["/var/www/html/assets/css/_form/form-tabs.less"], ".form-tabs__checkbox": ["/var/www/html/assets/css/_form/form-tabs.less"], ".tab": ["/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_widgets/tab.less"], ".discreet-tab": ["/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_widgets/discreet-tab.less"], ".form-tabs__content": ["/var/www/html/assets/css/_form/form-tabs.less"], ".form": ["/var/www/html/assets/css/_form/form.less"], ".form__row": ["/var/www/html/assets/css/_form/form.less"], ".form__button-row": ["/var/www/html/assets/css/_form/form.less"], ".formatting-help": ["/var/www/html/assets/css/_form/formatting-help.less"], ".formatting-help td": ["/var/www/html/assets/css/_form/formatting-help.less"], ".markdown-preview": ["/var/www/html/assets/css/_form/markdown-preview.less"], ".markdown-preview__title": ["/var/www/html/assets/css/_form/markdown-preview.less"], ".markdown-preview__inner": ["/var/www/html/assets/css/_form/markdown-preview.less"], ".unstylable-widget": ["/var/www/html/assets/css/_form/unstylable-widget.less"], ".unstylable-widget__caret": ["/var/www/html/assets/css/_form/unstylable-widget.less"], ".unstylable-widget__check": ["/var/www/html/assets/css/_form/unstylable-widget.less"], ".unstylable-widget__circle": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "html": ["/var/www/html/assets/css/_global.less"], "*": ["/var/www/html/assets/css/_global.less"], "*::before": ["/var/www/html/assets/css/_global.less"], "*::after": ["/var/www/html/assets/css/_global.less"], "::selection": ["/var/www/html/assets/css/_global.less"], "body": ["/var/www/html/assets/css/_global.less"], "[hidden]": ["/var/www/html/assets/css/_global.less"], "a": ["/var/www/html/assets/css/_global.less"], "a:hover": ["/var/www/html/assets/css/_global.less"], "a[href=\"#s\"]": ["/var/www/html/assets/css/_global.less"], "a[href=\"#spoiler\"]": ["/var/www/html/assets/css/_global.less"], "h1": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "h2": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "h3": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "h4": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "h5": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "h6": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "p": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "dl": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "ol": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "ul": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "blockquote": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "pre": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "button": ["/var/www/html/assets/css/_global.less"], "input": ["/var/www/html/assets/css/_global.less"], "optgroup": ["/var/www/html/assets/css/_global.less"], "option": ["/var/www/html/assets/css/_global.less"], "select": ["/var/www/html/assets/css/_global.less"], "textarea": ["/var/www/html/assets/css/_global.less"], "pre code": ["/var/www/html/assets/css/_global.less"], "hr": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/text-flow.less"], "summary": ["/var/www/html/assets/css/_global.less"], ".content-container": ["/var/www/html/assets/css/_layout/content-container.less"], ":root:not(.full-width) .content-container": ["/var/www/html/assets/css/_layout/content-container.less"], ".flow": ["/var/www/html/assets/css/_layout/flow.less"], ".flow-slim": ["/var/www/html/assets/css/_layout/flow.less"], "> * + *": ["/var/www/html/assets/css/_layout/flow.less"], "> legend + *": ["/var/www/html/assets/css/_layout/flow.less"], ".sidebar": ["/var/www/html/assets/css/_layout/sidebar.less"], ".sidebar__section": ["/var/www/html/assets/css/_layout/sidebar.less"], ".sidebar__title": ["/var/www/html/assets/css/_layout/sidebar.less"], ".sidebar__no-padding": ["/var/www/html/assets/css/_layout/sidebar.less"], ".site-accessibility-nav": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less"], ".site-accessibility-nav__link": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less"], ".site-alerts": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".site-alerts__alert": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".site-alerts__dismiss": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".js:root .site-alerts__alert": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".no-js:root .site-alerts__alert": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".js:root .site-alerts__dismiss": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".no-js:root .site-alerts__dismiss": ["/var/www/html/assets/css/_layout/site-alerts.less"], ".site-content": ["/var/www/html/assets/css/_layout/site-content.less"], ".site-content__body": ["/var/www/html/assets/css/_layout/site-content.less"], ".site-content__sidebar": ["/var/www/html/assets/css/_layout/site-content.less"], ".site-footer": ["/var/www/html/assets/css/_layout/site-footer.less"], ".site-nav": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__container": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__link": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__has-notifications": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__search-input": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__search-label": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__search-row": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__list": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__item": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__item--search": ["/var/www/html/assets/css/_layout/site-nav.less"], ".site-nav__item--user": ["/var/www/html/assets/css/_layout/site-nav.less"], ".icon": ["/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_things/icon.less", "/var/www/html/assets/js/lib/icon.js"], ".text": ["/var/www/html/assets/css/_layout/site-nav.less"], ".no-js:root": ["/var/www/html/assets/css/_layout/site-nav.less"], ".js:root": ["/var/www/html/assets/css/_layout/site-nav.less"], ".dropdown__toggle": ["/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_widgets/dropdown.less", "/var/www/html/assets/js/dropdowns.js"], ".dropdown--expanded": ["/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_widgets/dropdown.less", "/var/www/html/assets/js/dropdowns.js"], ".text-flow": ["/var/www/html/assets/css/_layout/text-flow.less"], ".text-flow-slim": ["/var/www/html/assets/css/_layout/text-flow.less"], "li": ["/var/www/html/assets/css/_layout/text-flow.less"], "img.inserted-image": ["/var/www/html/assets/css/_layout/text-flow.less"], ".unbuttonize": ["/var/www/html/assets/css/_resets/unbuttonize.less"], ".undecorate": ["/var/www/html/assets/css/_resets/undecorate.less"], ".unheaderize": ["/var/www/html/assets/css/_resets/unheaderize.less"], ".unlistify": ["/var/www/html/assets/css/_resets/unlistify.less"], ".unlistify > li": ["/var/www/html/assets/css/_resets/unlistify.less"], ".alert": ["/var/www/html/assets/css/_things/alert.less"], ".alert__icon": ["/var/www/html/assets/css/_things/alert.less"], ".border-list": ["/var/www/html/assets/css/_things/border-list.less"], ".border-list > * + *": ["/var/www/html/assets/css/_things/border-list.less"], ".columns": ["/var/www/html/assets/css/_things/columns.less"], ".comment": ["/var/www/html/assets/css/_things/comment.less"], ".comment__row": ["/var/www/html/assets/css/_things/comment.less"], ".comment__vote": ["/var/www/html/assets/css/_things/comment.less"], ".comment__main": ["/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/js/commenting.js"], ".comment__reply-link-disabled": ["/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/js/commenting.js"], ".comment__replies": ["/var/www/html/assets/css/_things/comment.less"], ".comment__header": ["/var/www/html/assets/css/_things/comment.less"], ".comment__info": ["/var/www/html/assets/css/_things/comment.less"], ".comment__info-link": ["/var/www/html/assets/css/_things/comment.less"], ".comment__content": ["/var/www/html/assets/css/_things/comment.less"], ".comment__body": ["/var/www/html/assets/css/_things/comment.less"], ".comment__nav": ["/var/www/html/assets/css/_things/comment.less"], ".comment--nested:last-child": ["/var/www/html/assets/css/_things/comment.less"], ".comment-form": ["/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/js/commenting.js"], ".hideable__checkbox:not(:checked) ~ .comment__row .comment__header": ["/var/www/html/assets/css/_things/comment.less"], ".hideable__checkbox:not(:checked) ~ .comment__row .comment__info": ["/var/www/html/assets/css/_things/comment.less"], ".hideable__checkbox:focus ~ .comment__row .comment__hide-toggle": ["/var/www/html/assets/css/_things/comment.less"], ".definition-list": ["/var/www/html/assets/css/_things/definition-list.less"], ".definition-list > dt": ["/var/www/html/assets/css/_things/definition-list.less"], ".definition-list > dd": ["/var/www/html/assets/css/_things/definition-list.less"], ".drop-zone": ["/var/www/html/assets/css/_things/drop-zone.less"], ".drop-zone--active": ["/var/www/html/assets/css/_things/drop-zone.less"], ".empty": ["/var/www/html/assets/css/_things/empty.less"], ".empty__emoji": ["/var/www/html/assets/css/_things/empty.less"], ".empty__text": ["/var/www/html/assets/css/_things/empty.less"], ".flair": ["/var/www/html/assets/css/_things/flair.less"], ".flair__label": ["/var/www/html/assets/css/_things/flair.less"], ".flair__buttons": ["/var/www/html/assets/css/_things/flair.less"], ".flair__button": ["/var/www/html/assets/css/_things/flair.less"], ".flex": ["/var/www/html/assets/css/_things/flex.less", "/var/www/html/assets/js/select2.js"], ".flex-desktop": ["/var/www/html/assets/css/_things/flex.less"], ".heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h1 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h2 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h3 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h4 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h5 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h6 .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h1:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h2:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h3:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h4:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h5:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], "h6:hover .heading-permalink": ["/var/www/html/assets/css/_things/heading-permalink.less"], ".hideable": ["/var/www/html/assets/css/_things/hideable.less"], ".hideable__checkbox": ["/var/www/html/assets/css/_things/hideable.less"], ".hideable__hide": ["/var/www/html/assets/css/_things/hideable.less"], ".hideable__toggle": ["/var/www/html/assets/css/_things/hideable.less"], ".hideable__indicator": ["/var/www/html/assets/css/_things/hideable.less"], ".icon svg": ["/var/www/html/assets/css/_things/icon.less"], ".icon:not(&--no-align):not(&--circled) svg": ["/var/www/html/assets/css/_things/icon.less"], ".icon--spin svg": ["/var/www/html/assets/css/_things/icon.less"], ".icon--pulse svg": ["/var/www/html/assets/css/_things/icon.less"], ".icon--circled": ["/var/www/html/assets/css/_things/icon.less"], ".message": ["/var/www/html/assets/css/_things/message.less"], ".message__body": ["/var/www/html/assets/css/_things/message.less"], ".submission-meta": ["/var/www/html/assets/css/_things/submission-meta.less"], ".submission-meta__short-url": ["/var/www/html/assets/css/_things/submission-meta.less"], ".submission": ["/var/www/html/assets/css/_things/submission.less"], ".submission__row": ["/var/www/html/assets/css/_things/submission.less"], ".submission__inner": ["/var/www/html/assets/css/_things/submission.less"], ".submission__title-row": ["/var/www/html/assets/css/_things/submission.less"], ".submission__link": ["/var/www/html/assets/css/_things/submission.less"], ".submission__host": ["/var/www/html/assets/css/_things/submission.less"], ".submission__content": ["/var/www/html/assets/css/_things/submission.less"], ".submission__image-link": ["/var/www/html/assets/css/_things/submission.less"], ".submission__image": ["/var/www/html/assets/css/_things/submission.less"], ".submission__flairs": ["/var/www/html/assets/css/_things/submission.less"], ".submission__thumb": ["/var/www/html/assets/css/_things/submission.less"], ".table-of-contents": ["/var/www/html/assets/css/_things/table-of-contents.less"], ".table-of-contents p": ["/var/www/html/assets/css/_things/table-of-contents.less"], ".table-of-contents ol": ["/var/www/html/assets/css/_things/table-of-contents.less"], ".table-of-contents ul": ["/var/www/html/assets/css/_things/table-of-contents.less"], ".table": ["/var/www/html/assets/css/_things/table.less"], ".table__shrink": ["/var/www/html/assets/css/_things/table.less"], ".table thead tr:last-child th": ["/var/www/html/assets/css/_things/table.less"], ".table thead tr:last-child td": ["/var/www/html/assets/css/_things/table.less"], ".table tbody tr:not(:last-child) td": ["/var/www/html/assets/css/_things/table.less"], ".table tbody tr:not(:last-child) th": ["/var/www/html/assets/css/_things/table.less"], ".table tfoot tr:not(:last-child) td": ["/var/www/html/assets/css/_things/table.less"], ".table tfoot tr:not(:last-child) th": ["/var/www/html/assets/css/_things/table.less"], ".table thead tr th": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_things/table.less"], ".table tbody tr th": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_things/table.less"], ".table tfoot tr th": ["/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_things/table.less"], ".table thead tr td": ["/var/www/html/assets/css/_things/table.less"], ".table tbody tr td": ["/var/www/html/assets/css/_things/table.less"], ".table tfoot tr td": ["/var/www/html/assets/css/_things/table.less"], ".user-flag": ["/var/www/html/assets/css/_things/user-flag.less"], ".vote": ["/var/www/html/assets/css/_things/vote.less"], ".vote--loading": ["/var/www/html/assets/css/_things/vote.less"], ".vote__net-score": ["/var/www/html/assets/css/_things/vote.less"], ".vote__spinner": ["/var/www/html/assets/css/_things/vote.less"], ".vote__button": ["/var/www/html/assets/css/_things/vote.less"], ".vote--user-upvoted": ["/var/www/html/assets/css/_things/vote.less"], ".vote__up": ["/var/www/html/assets/css/_things/vote.less"], ".vote--user-downvoted": ["/var/www/html/assets/css/_things/vote.less"], ".vote__down": ["/var/www/html/assets/css/_things/vote.less"], ".vote--failed": ["/var/www/html/assets/css/_things/vote.less"], ".wiki-article": ["/var/www/html/assets/css/_things/wiki-article.less"], ".wiki-article__title": ["/var/www/html/assets/css/_things/wiki-article.less"], ".wiki-lock-notice": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], ".wiki-lock-notice > *": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], ".wiki-lock-notice > :first-child": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], ".wiki-lock-notice > :last-child": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], ".block": ["/var/www/html/assets/css/_utilities/block.less"], ".break-text": ["/var/www/html/assets/css/_utilities/break-text.less"], ".bg-@{name}": ["/var/www/html/assets/css/_utilities/colors.less"], ".fg-@{name}": ["/var/www/html/assets/css/_utilities/colors.less"], ".fw-normal": ["/var/www/html/assets/css/_utilities/font-weight.less"], ".hidden": ["/var/www/html/assets/css/_utilities/hidden.less"], ".inline": ["/var/www/html/assets/css/_utilities/inline.less"], ".monospace": ["/var/www/html/assets/css/_utilities/monospace.less"], ".dark-mode-only": ["/var/www/html/assets/css/_utilities/night-mode.less"], ".light-mode-only": ["/var/www/html/assets/css/_utilities/night-mode.less"], ".no-desktop": ["/var/www/html/assets/css/_utilities/no-desktop.less"], ".no-mobile": ["/var/www/html/assets/css/_utilities/no-mobile.less"], ".no-select": ["/var/www/html/assets/css/_utilities/no-select.less"], ".no-underline": ["/var/www/html/assets/css/_utilities/no-underline.less"], ".no-underline__exempt": ["/var/www/html/assets/css/_utilities/no-underline.less"], ".no-visibility": ["/var/www/html/assets/css/_utilities/no-visibility.less"], ".no-wrap": ["/var/www/html/assets/css/_utilities/no-wrap.less"], ".pad-v": ["/var/www/html/assets/css/_utilities/pad.less"], ".pad-v-slim": ["/var/www/html/assets/css/_utilities/pad.less"], ".page-shadow": ["/var/www/html/assets/css/_utilities/page-shadow.less"], ".text-align-left": ["/var/www/html/assets/css/_utilities/text-align.less"], ".text-align-center": ["/var/www/html/assets/css/_utilities/text-align.less"], ".text-align-right": ["/var/www/html/assets/css/_utilities/text-align.less"], ".text-sm": ["/var/www/html/assets/css/_utilities/text-size.less"], ".text-xs": ["/var/www/html/assets/css/_utilities/text-size.less"], ".text-md": ["/var/www/html/assets/css/_utilities/text-size.less"], ".text-lg": ["/var/www/html/assets/css/_utilities/text-size.less"], ".text-xl": ["/var/www/html/assets/css/_utilities/text-size.less"], ".hljs": ["/var/www/html/assets/css/_vendor/hljs.less"], ".select2-container": ["/var/www/html/assets/css/_vendor/select2.less"], ".tippy-box": ["/var/www/html/assets/css/_vendor/tippy.less"], ".tippy-arrow::before": ["/var/www/html/assets/css/_vendor/tippy.less"], ".tippy-svg-arrow": ["/var/www/html/assets/css/_vendor/tippy.less"], ".button": ["/var/www/html/assets/css/_widgets/button.less"], ".button--small": ["/var/www/html/assets/css/_widgets/button.less"], ".button--secondary": ["/var/www/html/assets/css/_widgets/button.less"], ".button--transparent": ["/var/www/html/assets/css/_widgets/button.less"], ".button--flex": ["/var/www/html/assets/css/_widgets/button.less"], ".button--flex > * + *": ["/var/www/html/assets/css/_widgets/button.less"], ".clear-notification-button": ["/var/www/html/assets/css/_widgets/clear-notification-button.less"], ".discreet-tab--active": ["/var/www/html/assets/css/_widgets/discreet-tab.less"], ".dropdown": ["/var/www/html/assets/css/_widgets/dropdown.less"], ".dropdown__arrow": ["/var/www/html/assets/css/_widgets/dropdown.less"], ".dropdown__menu": ["/var/www/html/assets/css/_widgets/dropdown.less"], ".dropdown--mobile-only": ["/var/www/html/assets/css/_widgets/dropdown.less"], ".dropdown--right": ["/var/www/html/assets/css/_widgets/dropdown.less"], ".menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".menu-item--active": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".menu-item--active::before": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".menu-item:any-link": ["/var/www/html/assets/css/_widgets/menu-item.less"], "input[type='submit'].menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], "input[type='reset'].menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], "input[type='button'].menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], "button.menu-item": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".menu-item:not(:disabled):hover": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".menu-item:focus": ["/var/www/html/assets/css/_widgets/menu-item.less"], ".subscribe-button": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".subscribe-button__label": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".subscribe-button__subscriber-count": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".subscribe-button__dummy-label": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".subscribe-button--subscribe &__label": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".subscribe-button--subscribe &__subscriber-count": ["/var/www/html/assets/css/_widgets/subscribe-button.less"], ".tab--active": ["/var/www/html/assets/css/_widgets/tab.less"], ".no-js:root .dropdown:hover > .tab": ["/var/www/html/assets/css/_widgets/tab.less"], ".js:root .dropdown--expanded > .tab": ["/var/www/html/assets/css/_widgets/tab.less"], ":root[data-night-mode=\"light\"]": ["/var/www/html/assets/css/themes/postmill/index.less"], ":root[data-night-mode=\"dark\"]": ["/var/www/html/assets/css/themes/postmill/index.less"], ":root[data-night-mode=\"auto\"]": ["/var/www/html/assets/css/themes/postmill/index.less"], ".js-display-new-comments": ["/var/www/html/assets/js/comment-count.js"], ".js-update-comment-count": ["/var/www/html/assets/js/comment-count.js"], ".comment__error": ["/var/www/html/assets/js/commenting.js"], ".comment__reply-link": ["/var/www/html/assets/js/commenting.js"], ".activeOverlay": ["/var/www/html/assets/js/controller/file-drop-controller.js"], ".subscribe-form": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], ".subscribe": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], ".unsubscribe": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], "[data-night-mode]": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], ".upvoted": ["/var/www/html/assets/js/controller/vote-controller.js"], ".downvoted": ["/var/www/html/assets/js/controller/vote-controller.js"], ".loading": ["/var/www/html/assets/js/controller/vote-controller.js"], ".error": ["/var/www/html/assets/js/controller/vote-controller.js"], ".dropdown__menu a[href]": ["/var/www/html/assets/js/dropdowns.js"], ".dropdown__menu button:not([disabled])": ["/var/www/html/assets/js/dropdowns.js"], ".dropdown__menu input:not([type=\"hidden\"]):not([disabled])": ["/var/www/html/assets/js/dropdowns.js"], ".dropdown__menu button[type=\"submit\"]": ["/var/www/html/assets/js/dropdowns.js"], ".dropdown__menu button:not([type])": ["/var/www/html/assets/js/dropdowns.js"], ".select2": ["/var/www/html/assets/js/select2.js"], ".flex__grow": ["/var/www/html/assets/js/select2.js"], ".form textarea": ["/var/www/html/assets/js/unload-forms.js"], ".form input:not([type])": ["/var/www/html/assets/js/unload-forms.js"], ".form input[type=\"text\"]": ["/var/www/html/assets/js/unload-forms.js"], ".form input[type=\"url\"]": ["/var/www/html/assets/js/unload-forms.js"], ".form input[type=\"file\"]": ["/var/www/html/assets/js/unload-forms.js"], ".js-poppers-enabled .submission__submitter": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .submission__body a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .comment__body a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .comment__context a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .comment__info a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .message__head a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"], ".js-poppers-enabled .message__body a[href*=\"/user/\"]": ["/var/www/html/assets/js/user-popper.js"]}, "semantic_map": {"ui-component": ["/var/www/html/assets/css/_card/dropdown-card.less", "/var/www/html/assets/css/_form/_mixins.less", "/var/www/html/assets/css/_form/compound-form-widget.less", "/var/www/html/assets/css/_form/decorated-form-control.less", "/var/www/html/assets/css/_form/fieldset.less", "/var/www/html/assets/css/_form/form-flex.less", "/var/www/html/assets/css/_form/form-tabs.less", "/var/www/html/assets/css/_form/form.less", "/var/www/html/assets/css/_form/formatting-help.less", "/var/www/html/assets/css/_form/markdown-preview.less", "/var/www/html/assets/css/_form/unstylable-widget.less", "/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/sidebar.less", "/var/www/html/assets/css/_layout/site-accessibility-nav.less", "/var/www/html/assets/css/_layout/site-alerts.less", "/var/www/html/assets/css/_layout/site-content.less", "/var/www/html/assets/css/_layout/site-footer.less", "/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_resets/unbuttonize.less", "/var/www/html/assets/css/_things/alert.less", "/var/www/html/assets/css/_things/border-list.less", "/var/www/html/assets/css/_things/comment.less", "/var/www/html/assets/css/_things/drop-zone.less", "/var/www/html/assets/css/_things/empty.less", "/var/www/html/assets/css/_things/flair.less", "/var/www/html/assets/css/_things/heading-permalink.less", "/var/www/html/assets/css/_things/hideable.less", "/var/www/html/assets/css/_things/message.less", "/var/www/html/assets/css/_things/submission-meta.less", "/var/www/html/assets/css/_things/submission.less", "/var/www/html/assets/css/_things/table-of-contents.less", "/var/www/html/assets/css/_things/user-flag.less", "/var/www/html/assets/css/_things/vote.less", "/var/www/html/assets/css/_things/wiki-article.less", "/var/www/html/assets/css/_things/wiki-lock-notice.less", "/var/www/html/assets/css/_vendor/select2.less", "/var/www/html/assets/css/_vendor/tippy.less", "/var/www/html/assets/css/_widgets/_mixins.less", "/var/www/html/assets/css/_widgets/button.less", "/var/www/html/assets/css/_widgets/clear-notification-button.less", "/var/www/html/assets/css/_widgets/discreet-tab.less", "/var/www/html/assets/css/_widgets/dropdown.less", "/var/www/html/assets/css/_widgets/menu-item.less", "/var/www/html/assets/css/_widgets/subscribe-button.less", "/var/www/html/assets/css/_widgets/tab.less", "/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less", "/var/www/html/assets/js/comment-count.js", "/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/controller/alert-controller.js", "/var/www/html/assets/js/controller/dialog-controller.js", "/var/www/html/assets/js/controller/fetch-titles-controller.js", "/var/www/html/assets/js/controller/file-drop-controller.js", "/var/www/html/assets/js/controller/reload-captcha-controller.js", "/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/vote-controller.js", "/var/www/html/assets/js/dropdowns.js", "/var/www/html/assets/js/lib/icon.js", "/var/www/html/assets/js/main.js", "/var/www/html/assets/js/select2.js", "/var/www/html/assets/js/user-popper.js"], "form-styling": ["/var/www/html/assets/css/_form/_mixins.less"], "form": ["/var/www/html/assets/css/_form/compound-form-widget.less", "/var/www/html/assets/css/_form/form-error-list.less", "/var/www/html/assets/css/_form/unstylable-widget.less"], "error-handling": ["/var/www/html/assets/css/_form/form-error-list.less", "/var/www/html/assets/js/commenting.js"], "responsive-design": ["/var/www/html/assets/css/_form/form-flex.less", "/var/www/html/assets/css/_form/form.less", "/var/www/html/assets/css/_layout/site-content.less", "/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_things/flex.less", "/var/www/html/assets/css/_things/submission.less", "/var/www/html/assets/css/_things/table.less", "/var/www/html/assets/css/_utilities/no-desktop.less", "/var/www/html/assets/css/_utilities/no-mobile.less"], "form-element": ["/var/www/html/assets/css/_form/form-tabs.less"], "custom-styling": ["/var/www/html/assets/css/_form/unstylable-widget.less"], "typography": ["/var/www/html/assets/css/_global.less"], "layout": ["/var/www/html/assets/css/_global.less", "/var/www/html/assets/css/_layout/content-container.less", "/var/www/html/assets/css/_layout/flow.less", "/var/www/html/assets/css/_layout/site-content.less", "/var/www/html/assets/css/_layout/text-flow.less", "/var/www/html/assets/css/_things/columns.less", "/var/www/html/assets/css/_things/empty.less", "/var/www/html/assets/css/_things/flex.less", "/var/www/html/assets/css/_utilities/block.less", "/var/www/html/assets/css/_utilities/inline.less", "/var/www/html/assets/css/_utilities/no-wrap.less"], "container": ["/var/www/html/assets/css/_layout/content-container.less"], "spacing": ["/var/www/html/assets/css/_layout/flow.less", "/var/www/html/assets/css/_layout/text-flow.less"], "navigation": ["/var/www/html/assets/css/_layout/sidebar.less", "/var/www/html/assets/css/_layout/site-accessibility-nav.less", "/var/www/html/assets/css/_layout/site-nav.less", "/var/www/html/assets/css/_things/table-of-contents.less", "/var/www/html/assets/css/_widgets/discreet-tab.less", "/var/www/html/assets/css/_widgets/dropdown.less", "/var/www/html/assets/css/_widgets/menu-item.less", "/var/www/html/assets/css/_widgets/tab.less"], "accessibility": ["/var/www/html/assets/css/_layout/site-accessibility-nav.less", "/var/www/html/assets/css/_layout/site-alerts.less", "/var/www/html/assets/js/dropdowns.js"], "animation": ["/var/www/html/assets/css/_layout/site-alerts.less", "/var/www/html/assets/css/_things/icon.less"], "footer": ["/var/www/html/assets/css/_layout/site-footer.less"], "text-elements": ["/var/www/html/assets/css/_layout/text-flow.less"], "text-styling": ["/var/www/html/assets/css/_resets/undecorate.less"], "list-styling": ["/var/www/html/assets/css/_resets/unlistify.less"], "css-reset": ["/var/www/html/assets/css/_resets/unlistify.less"], "styling": ["/var/www/html/assets/css/_things/border-list.less", "/var/www/html/assets/css/_things/definition-list.less", "/var/www/html/assets/css/_utilities/page-shadow.less", "/var/www/html/assets/css/_vendor/tippy.less"], "multi-column": ["/var/www/html/assets/css/_things/columns.less"], "list": ["/var/www/html/assets/css/_things/definition-list.less"], "grid-layout": ["/var/www/html/assets/css/_things/definition-list.less"], "flexbox": ["/var/www/html/assets/css/_things/flex.less"], "interaction": ["/var/www/html/assets/css/_things/heading-permalink.less", "/var/www/html/assets/css/_things/vote.less", "/var/www/html/assets/js/controller/dialog-controller.js", "/var/www/html/assets/js/controller/vote-controller.js"], "toggle": ["/var/www/html/assets/css/_things/hideable.less"], "visibility": ["/var/www/html/assets/css/_things/hideable.less", "/var/www/html/assets/css/_utilities/hidden.less", "/var/www/html/assets/css/_utilities/night-mode.less", "/var/www/html/assets/css/_utilities/no-visibility.less"], "icon": ["/var/www/html/assets/css/_things/icon.less"], "style": ["/var/www/html/assets/css/_things/icon.less"], "table": ["/var/www/html/assets/css/_things/table.less"], "notification": ["/var/www/html/assets/css/_things/wiki-lock-notice.less"], "utility": ["/var/www/html/assets/css/_utilities/block.less", "/var/www/html/assets/css/_utilities/hidden.less", "/var/www/html/assets/css/_utilities/no-select.less", "/var/www/html/assets/css/_utilities/no-visibility.less"], "text-formatting": ["/var/www/html/assets/css/_utilities/break-text.less", "/var/www/html/assets/css/_utilities/monospace.less", "/var/www/html/assets/css/_utilities/no-wrap.less"], "layout-management": ["/var/www/html/assets/css/_utilities/break-text.less"], "utility-classes": ["/var/www/html/assets/css/_utilities/colors.less", "/var/www/html/assets/css/_utilities/text-align.less", "/var/www/html/assets/css/_utilities/text-size.less"], "color-management": ["/var/www/html/assets/css/_utilities/colors.less"], "display": ["/var/www/html/assets/css/_utilities/inline.less"], "font-style": ["/var/www/html/assets/css/_utilities/monospace.less"], "night-mode": ["/var/www/html/assets/css/_utilities/night-mode.less", "/var/www/html/assets/css/themes/postmill/index.less"], "color-scheme": ["/var/www/html/assets/css/_utilities/night-mode.less", "/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less"], "utility-class": ["/var/www/html/assets/css/_utilities/no-desktop.less"], "media-query": ["/var/www/html/assets/css/_utilities/no-mobile.less"], "text-selection": ["/var/www/html/assets/css/_utilities/no-select.less"], "link-style": ["/var/www/html/assets/css/_utilities/no-underline.less"], "hover-effect": ["/var/www/html/assets/css/_utilities/no-underline.less"], "utility-styles": ["/var/www/html/assets/css/_utilities/pad.less"], "padding": ["/var/www/html/assets/css/_utilities/pad.less"], "shadow-effect": ["/var/www/html/assets/css/_utilities/page-shadow.less"], "text-alignment": ["/var/www/html/assets/css/_utilities/text-align.less"], "text-sizing": ["/var/www/html/assets/css/_utilities/text-size.less"], "dark-mode": ["/var/www/html/assets/css/_vendor/select2.less"], "theme": ["/var/www/html/assets/css/themes/_modern-night.less", "/var/www/html/assets/css/themes/_modern.less", "/var/www/html/assets/css/themes/postmill/_dark.less", "/var/www/html/assets/css/themes/postmill/_light.less"], "colors": ["/var/www/html/assets/css/themes/postmill/_dark.less"], "mixin": ["/var/www/html/assets/css/themes/postmill/_dark.less"], "color-variables": ["/var/www/html/assets/css/themes/postmill/_light.less"], "theme-switching": ["/var/www/html/assets/css/themes/postmill/index.less", "/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "icons": ["/var/www/html/assets/icons/icons.svg"], "ui-elements": ["/var/www/html/assets/icons/icons.svg"], "vector-graphics": ["/var/www/html/assets/icons/icons.svg"], "local-storage": ["/var/www/html/assets/js/comment-count.js"], "translation": ["/var/www/html/assets/js/comment-count.js"], "ajax": ["/var/www/html/assets/js/commenting.js", "/var/www/html/assets/js/user-popper.js"], "alert-dismissal": ["/var/www/html/assets/js/controller/alert-controller.js"], "time-difference": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "date-formatting": ["/var/www/html/assets/js/controller/diff-time-controller.js"], "stimulus-controller": ["/var/www/html/assets/js/controller/diff-time-controller.js", "/var/www/html/assets/js/controller/relative-time-controller.js"], "webpage-title-fetcher": ["/var/www/html/assets/js/controller/fetch-titles-controller.js"], "file-upload": ["/var/www/html/assets/js/controller/file-drop-controller.js"], "drag-and-drop": ["/var/www/html/assets/js/controller/file-drop-controller.js"], "markdown-parser": ["/var/www/html/assets/js/controller/markdown-controller.js"], "live-preview": ["/var/www/html/assets/js/controller/markdown-controller.js"], "debounce": ["/var/www/html/assets/js/controller/markdown-controller.js"], "date-manipulation": ["/var/www/html/assets/js/controller/relative-time-controller.js"], "localization": ["/var/www/html/assets/js/controller/relative-time-controller.js"], "captcha": ["/var/www/html/assets/js/controller/reload-captcha-controller.js"], "image-reloading": ["/var/www/html/assets/js/controller/reload-captcha-controller.js"], "validation": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "text-input": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "internationalization": ["/var/www/html/assets/js/controller/remaining-chars-controller.js"], "data-fetching": ["/var/www/html/assets/js/controller/subscribe-button-controller.js"], "state-management": ["/var/www/html/assets/js/controller/subscribe-button-controller.js", "/var/www/html/assets/js/controller/vote-controller.js"], "syntax-highlighting": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "dynamic-imports": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "asynchronous-operations": ["/var/www/html/assets/js/controller/syntax-highlight-controller.js"], "user-preference": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "asynchronous-request": ["/var/www/html/assets/js/controller/toggle-night-mode-controller.js"], "interactivity": ["/var/www/html/assets/js/dropdowns.js"], "http-request": ["/var/www/html/assets/js/lib/http.js"], "response-handling": ["/var/www/html/assets/js/lib/http.js"], "javascript-module": ["/var/www/html/assets/js/main.js"], "form-handling": ["/var/www/html/assets/js/unload-forms.js"], "user-warning": ["/var/www/html/assets/js/unload-forms.js"], "event-listening": ["/var/www/html/assets/js/unload-forms.js"], "caching": ["/var/www/html/assets/js/user-popper.js"]}, "created_at": "2025-08-02T18:55:55.731705", "status": "completed"}