"""
Codebase Indexer - LLM-powered dynamic codebase analysis system
(Refactored and empowered by <PERSON>)

This module uses a language model (like <PERSON>wen Coder) to analyze functionality, UI components,
and other critical information for each file in a codebase. It intelligently discovers source
files using <PERSON><PERSON> to inspect live browser sessions, ensuring accuracy for modern web apps
built with bundlers like Webpack or Vite.
"""

import json
import logging
import subprocess
import requests
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime

# Use Playwright for robust browser automation
from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, TimeoutError

logger = logging.getLogger(__name__)


@dataclass
class FileMetadata:
    """File metadata structure for codebase indexing"""
    file_path: str
    file_type: str
    language: str
    functionality: str
    symbols: List[str]
    ui_components: List[str]
    css_selectors: List[str]
    imports_exports: List[str]
    dependencies: List[str]
    semantic_tags: List[str]


@dataclass
class CodebaseIndex:
    """Codebase index structure"""
    container_name: str
    indexed_files: Dict[str, FileMetadata]
    language_stats: Dict[str, int]
    symbol_map: Dict[str, List[str]]
    ui_component_map: Dict[str, List[str]]
    css_selector_map: Dict[str, List[str]]
    semantic_map: Dict[str, List[str]]
    created_at: str


class CodebaseIndexer:
    """LLM-powered codebase indexer with intelligent Playwright-based file discovery."""

    def __init__(self, coder_model: str = "Qwen/Qwen2.5-Coder-32B-Instruct",
                 coder_base_url: str = "http://localhost:8001"):
        self.coder_model = coder_model
        self.coder_base_url = coder_base_url.rstrip('/')
        self.project_root_cache: Dict[str, Optional[str]] = {}

    def index_codebase(self, container_name: str, run_dir: str, output_file: Optional[str] = None) -> CodebaseIndex:
        """
        Index the codebase using Playwright to discover source files from live URLs.
        """
        logger.info(f"🚀 Starting codebase indexing for container: {container_name}")

        if output_file:
            self._clear_previous_output(output_file)

        # 1. Discover source files using the robust Playwright method
        source_files = self._discover_source_files_with_playwright(container_name, run_dir)
        if not source_files:
            logger.error("🚫 No source files discovered. Halting indexing. Check if the site is running and uses source maps.")
            # Return an empty index
            return CodebaseIndex(container_name, {}, {}, {}, {}, {}, {}, datetime.now().isoformat())


        logger.info(f"✅ Found {len(source_files)} source files to analyze.")
        self._print_directory_summary(source_files)

        # 2. Initialize output file for incremental updates
        if output_file:
            self._initialize_output_file(output_file, container_name)

        # 3. Analyze each file
        indexed_files, lang_stats, sym_map, ui_map, css_map, sem_map = {}, {}, {}, {}, {}, {}

        for i, file_path in enumerate(source_files):
            progress = f"{i+1}/{len(source_files)}"
            logger.info(f"[{progress}] Analyzing: {file_path}")
            print(f"Progress: [{progress}] - {file_path}")

            try:
                metadata = self._analyze_file(container_name, file_path)
                if metadata:
                    indexed_files[file_path] = metadata
                    # Update all statistics and mappings
                    lang_stats[metadata.language] = lang_stats.get(metadata.language, 0) + 1
                    for symbol in metadata.symbols:
                        sym_map.setdefault(symbol, []).append(file_path)
                    for component in metadata.ui_components:
                        ui_map.setdefault(component, []).append(file_path)
                    for selector in metadata.css_selectors:
                        css_map.setdefault(selector, []).append(file_path)
                    for tag in metadata.semantic_tags:
                        sem_map.setdefault(tag, []).append(file_path)

                    if output_file:
                        self._append_file_result(output_file, file_path, metadata)

            except Exception as e:
                logger.warning(f"⚠️ Failed to analyze {file_path}: {e}")
                continue

        # 4. Build and finalize the complete index
        index = CodebaseIndex(
            container_name=container_name,
            indexed_files=indexed_files,
            language_stats=lang_stats,
            symbol_map=sym_map,
            ui_component_map=ui_map,
            css_selector_map=css_map,
            semantic_map=sem_map,
            created_at=datetime.now().isoformat()
        )

        if output_file:
            self._finalize_output_file(output_file, index)

        logger.info(f"🎉 Codebase indexing completed. Indexed {len(indexed_files)} files.")
        return index

    def _discover_source_files_with_playwright(self, container_name: str, run_dir: str) -> List[str]:
        """
        The core file discovery method. Uses Playwright to visit URLs, execute JS to find
        webpack/vite source map entries, and intelligently maps them to container file paths.
        """
        logger.info("🤖 Using Playwright to intelligently discover source files...")

        # 1. Get URLs from the exploration run directory
        urls = self._extract_urls_from_exploration(run_dir)
        if not urls:
            logger.warning("No localhost URLs found in the run directory. Cannot discover files.")
            return []
        logger.info(f"Found {len(urls)} unique URLs to analyze.")

        # 2. Use Playwright to visit URLs and extract webpack source paths
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            
            # THE CORRECT FIX: Create a browser context with CSP bypassed, then create a page from it.
            logger.info("⚡ Bypassing Content Security Policy (CSP) for automation at the browser context level.")
            context = browser.new_context(bypass_csp=True)
            page = context.new_page()

            try:
                webpack_sources = self._extract_sources_from_urls(page, urls)
            finally:
                context.close() # Important to close the context
                browser.close()

        if not webpack_sources:
            logger.warning("Playwright analysis did not yield any webpack source files.")
            return []
        logger.info(f"Discovered {len(webpack_sources)} potential source file paths from the browser.")

        # 3. Intelligently map webpack paths to full container paths
        project_root = self._find_project_root(container_name)
        if not project_root:
            logger.error(f"Could not determine project root in container {container_name}. Aborting.")
            return []
        
        source_files = []
        for source in webpack_sources:
            full_path = self._webpack_source_to_file_path(container_name, source, project_root)
            if full_path:
                source_files.append(full_path)

        return sorted(list(set(source_files)))

    def _extract_sources_from_urls(self, page: Page, urls: List[str]) -> List[str]:
        """
        Iterate through URLs and extract source files using multiple robust strategies.
        Now possible again thanks to bypassing CSP.
        """
        all_sources: Set[str] = set()

        # JS to clean a path string. Will be passed to the browser.
        clean_path_script = """
        (path) => {
            if (!path || typeof path !== 'string') return null;
            if (path.startsWith('webpack:///')) path = path.substring('webpack:///'.length);
            else if (path.startsWith('webpack://')) path = path.substring('webpack://'.length);
            
            if (path.startsWith('/')) path = path.substring(1);
            if (path.startsWith('./')) path = path.substring(2);
            
            const queryIndex = path.indexOf('?');
            if (queryIndex !== -1) path = path.substring(0, queryIndex);

            if (path.includes('(webpack)') || path.includes('node_modules')) return null;
            
            return path;
        }
        """

        # STRATEGY 1: Use performance.getEntries(), but wait for them to appear.
        get_sources_from_perf_script = f"""
        () => {{
            const cleanPath = {clean_path_script};
            if (!window.performance || !window.performance.getEntries) return [];
            const entries = window.performance.getEntries();
            const webpackSources = new Set();
            for (const entry of entries) {{
                if (entry.name && entry.name.startsWith('webpack://')) {{
                    const cleaned = cleanPath(entry.name);
                    if (cleaned) webpackSources.add(cleaned);
                }}
            }}
            return Array.from(webpackSources);
        }}
        """

        # STRATEGY 2: Inspect webpack's internal module cache directly. This is a robust fallback.
        get_sources_from_webpack_runtime_script = f"""
        () => {{
            const cleanPath = {clean_path_script};
            const sources = new Set();
            try {{
                // Common for Webpack 4/5
                const wpRequire = window.__webpack_require__;
                if (wpRequire && typeof wpRequire.c === 'object') {{
                    Object.keys(wpRequire.c).forEach(moduleId => {{
                        const cleaned = cleanPath(moduleId);
                        if (cleaned) sources.add(cleaned);
                    }});
                }}
                // Fallback for Nuxt.js and other frameworks
                const nuxtChunks = Object.keys(window).find(k => k.startsWith('webpackChunk'));
                if (nuxtChunks && window[nuxtChunks] && typeof window[nuxtChunks].m === 'object') {{
                        Object.keys(window[nuxtChunks].m).forEach(moduleId => {{
                        const cleaned = cleanPath(moduleId);
                        if (cleaned) sources.add(cleaned);
                    }});
                }}
            }} catch (e) {{
                // This might fail, it's okay.
            }}
            return Array.from(sources);
        }}
        """

        for url in urls:
            try:
                logger.info(f"-> Analyzing URL: {url}")
                page.goto(url, wait_until='domcontentloaded', timeout=15000)
                
                # Attempt Strategy 1: Wait for performance entries
                try:
                    # Wait up to 10 seconds for at least one webpack entry to appear
                    page.wait_for_function("() => window.performance.getEntries().some(e => e.name.startsWith('webpack://'))", timeout=10000)
                    sources = page.evaluate(get_sources_from_perf_script)
                    if sources:
                        logger.info(f"  ✅ Strategy 1 (Performance API) succeeded. Found {len(sources)} sources.")
                        all_sources.update(sources)
                        continue # Move to next URL
                except TimeoutError:
                    logger.warning(f"  ⚠️ Strategy 1 (Performance API) timed out for {url}. Trying fallback.")

                # Attempt Strategy 2: Direct webpack inspection
                # Give the app a moment to stabilize before trying this
                page.wait_for_timeout(2000) 
                sources = page.evaluate(get_sources_from_webpack_runtime_script)
                if sources:
                    logger.info(f"  ✅ Strategy 2 (Webpack Runtime) succeeded. Found {len(sources)} sources.")
                    all_sources.update(sources)
                else:
                    logger.warning(f"  ❌ Both strategies failed for URL: {url}")

            except Exception as e:
                logger.error(f"  An unexpected error occurred while analyzing URL {url}: {e}")
                continue
        
        return list(all_sources)

    def _find_project_root(self, container_name: str) -> Optional[str]:
        """
        Intelligently find the project root in the container, typically where package.json is.
        This is cached for efficiency.
        """
        if container_name in self.project_root_cache:
            return self.project_root_cache[container_name]

        logger.info(f"🔍 Searching for project root (package.json) in container '{container_name}'...")
        # Search in common web app directories
        search_dirs = ['/app', '/var/www', '/usr/src/app', '/opt/app']
        try:
            for directory in search_dirs:
                # The find command is more reliable
                cmd = f"docker exec {container_name} find {directory} -maxdepth 3 -name 'package.json' -print -quit 2>/dev/null"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=20)
                
                if result.returncode == 0 and result.stdout.strip():
                    package_json_path = result.stdout.strip()
                    project_root = str(Path(package_json_path).parent)
                    logger.info(f"💡 Found project root at: {project_root}")
                    self.project_root_cache[container_name] = project_root
                    return project_root
        except Exception as e:
            logger.error(f"Error while searching for project root: {e}")
        
        logger.warning("Could not find project root automatically. Falling back to '/app'.")
        self.project_root_cache[container_name] = '/app'
        return '/app'


    def _webpack_source_to_file_path(self, container_name: str, webpack_source: str, project_root: str) -> Optional[str]:
        """Convert a clean webpack source path to an actual file path in the container."""
        # The webpack_source is already cleaned, e.g., 'src/components/Button.js'
        potential_path = os.path.join(project_root, webpack_source)
        
        try:
            # Check if the constructed path actually exists in the container
            cmd = f"docker exec {container_name} test -f '{potential_path}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=5)
            if result.returncode == 0:
                return potential_path
        except Exception:
            return None # Timeout or other error
            
        return None

    def _extract_urls_from_exploration(self, run_dir: str) -> List[str]:
        """Extract all unique localhost URLs from exploration graph files."""
        import glob
        urls = set()
        node_pattern = os.path.join(run_dir, "graph", "node_*", "node_info.json")
        for node_file in glob.glob(node_pattern):
            try:
                with open(node_file, 'r') as f:
                    node_data = json.load(f)
                
                def is_valid_url(url):
                    return url and url.startswith('http://localhost:')

                if is_valid_url(node_data.get('url')):
                    urls.add(node_data['url'])
                for child_url in node_data.get('children', []):
                    if is_valid_url(child_url):
                        urls.add(child_url)
            except Exception as e:
                logger.warning(f"Error processing {node_file}: {e}")
        return sorted(list(urls))

    # --- LLM Analysis and File I/O Methods (Largely Unchanged) ---

    def _analyze_file(self, container_name: str, file_path: str) -> Optional[FileMetadata]:
        """Analyze a single file using the Coder LLM."""
        try:
            file_content = self._read_file_content(container_name, file_path)
            if not file_content or not file_content.strip():
                return None
            
            # Limit file size
            if len(file_content) > 20000:
                file_content = file_content[:20000] + "\n... [truncated]"

            prompt = self._build_analysis_prompt(file_path, file_content)
            analysis_result = self._call_coder_api(prompt)
            return self._parse_analysis_result(file_path, analysis_result) if analysis_result else None

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return None

    def _read_file_content(self, container_name: str, file_path: str) -> Optional[str]:
        """Read file content from the Docker container."""
        try:
            cmd = f"docker exec {container_name} cat '{file_path}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            return result.stdout if result.returncode == 0 else None
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None

    def _build_analysis_prompt(self, file_path: str, file_content: str) -> str:
        """Build the analysis prompt for the Coder LLM."""
        # This prompt is well-structured, no major changes needed
        return f"""
Analyze this source file and extract structured information about its functionality, symbols, and patterns.

File Path: {file_path}
File Content:
```
{file_content}
```

Provide a JSON response with the following structure. Be comprehensive but precise.
{{
    "file_type": "css|js|php|html|json|etc",
    "language": "javascript|css|php|html|python|etc",
    "functionality": "Brief description of what this file does",
    "symbols": ["function_names", "class_names", "variable_names"],
    "ui_components": ["dropdown", "button", "form", "modal"],
    "css_selectors": [".class-names", "#ids"],
    "imports_exports": ["imported_modules", "exported_symbols"],
    "dependencies": ["referenced_files", "external_libraries"],
    "semantic_tags": ["authentication", "navigation", "ui-component"]
}}

Respond with ONLY the JSON object, with no additional text or code block wrapping.
"""

    def _call_coder_api(self, prompt: str) -> Optional[Dict]:
        """Call the Coder API for analysis."""
        try:
            payload = {
                "model": self.coder_model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.0,
                "max_tokens": 2048,
                "response_format": {"type": "json_object"} # Use JSON mode if available
            }
            response = requests.post(f"{self.coder_base_url}/v1/chat/completions", json=payload, timeout=45)
            response.raise_for_status()
            content = response.json()["choices"][0]["message"]["content"].strip()
            # Sometimes the model still wraps the JSON in ```json ... ```
            if content.startswith("```json"):
                content = content[7:-3].strip()
            return json.loads(content)
        except Exception as e:
            logger.error(f"Coder API call failed: {e}")
            return None

    def _parse_analysis_result(self, file_path: str, res: Dict) -> Optional[FileMetadata]:
        """Parse the Coder analysis result into the FileMetadata dataclass."""
        try:
            return FileMetadata(
                file_path=file_path,
                file_type=res.get("file_type", "unknown"),
                language=res.get("language", "unknown"),
                functionality=res.get("functionality", ""),
                symbols=res.get("symbols", []),
                ui_components=res.get("ui_components", []),
                css_selectors=res.get("css_selectors", []),
                imports_exports=res.get("imports_exports", []),
                dependencies=res.get("dependencies", []),
                semantic_tags=res.get("semantic_tags", [])
            )
        except Exception as e:
            logger.error(f"Error parsing analysis result for {file_path}: {e}")
            return None

    # --- Utility and File I/O Methods ---
    def _clear_previous_output(self, output_file: str):
        if os.path.exists(output_file):
            os.remove(output_file)

    def _print_directory_summary(self, source_files: List[str]):
        dirs = {}
        for file in source_files:
            dir_path = str(Path(file).parent)
            dirs[dir_path] = dirs.get(dir_path, 0) + 1
        print("\n=== Directory Summary ===")
        for directory, count in sorted(dirs.items()):
            print(f"{directory}: {count} files")
        print("========================\n")

    def _initialize_output_file(self, output_file: str, container_name: str):
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        initial_data = {
            "container_name": container_name,
            "created_at": datetime.now().isoformat(),
            "status": "in_progress",
            "indexed_files": {}
        }
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2)

    def _append_file_result(self, output_file: str, file_path: str, metadata: FileMetadata):
        try:
            with open(output_file, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data["indexed_files"][file_path] = asdict(metadata)
                f.seek(0)
                json.dump(data, f, indent=2)
                f.truncate()
        except Exception as e:
            logger.warning(f"Error appending to output file: {e}")
            
    def _finalize_output_file(self, output_file: str, index: CodebaseIndex):
        """Finalize output file with complete index data."""
        try:
            index_dict = {
                "container_name": index.container_name,
                "indexed_files": {k: asdict(v) for k, v in index.indexed_files.items()},
                "language_stats": index.language_stats,
                "symbol_map": index.symbol_map,
                "ui_component_map": index.ui_component_map,
                "css_selector_map": index.css_selector_map,
                "semantic_map": index.semantic_map,
                "created_at": index.created_at,
                "status": "completed"
            }
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(index_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"Finalized index saved to: {output_file}")
        except Exception as e:
            logger.error(f"Error finalizing output file: {e}")


def main():
    """Command line entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="Codebase Indexer - LLM-powered codebase analysis (Gemini Edition)")
    parser.add_argument("--container", required=True, help="Docker container name to be indexed")
    parser.add_argument("--run_dir", required=True, help="Run directory containing exploration graph (e.g., runs/reddit_qwen_01)")
    parser.add_argument("--output", help="Output index file path (defaults to codebase_index_<container>.json)")
    parser.add_argument("--coder_url", default="http://localhost:8001", help="URL of the Coder LLM API endpoint")
    
    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # Create indexer
    indexer = CodebaseIndexer(coder_base_url=args.coder_url)

    # Execute indexing
    output_file = args.output or f"codebase_index_{args.container}.json"
    index = indexer.index_codebase(
        container_name=args.container,
        run_dir=args.run_dir,
        output_file=output_file,
    )

    if index.indexed_files:
        print(f"\nIndexing completed! Analyzed {len(index.indexed_files)} files.")
        print(f"Languages found: {list(index.language_stats.keys())}")
        print(f"Index saved to: {output_file}")
    else:
        print("\nIndexing finished with no files analyzed. Please check logs for errors.")

if __name__ == "__main__":
    main()
