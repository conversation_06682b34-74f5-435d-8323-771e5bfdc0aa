#!/usr/bin/env python3
"""
Docker Code Applier - Safely applies code modifications to Docker containers.

This module provides safe, atomic, and reversible code modifications to files
within Docker containers, with comprehensive backup and rollback capabilities.
"""

import json
import logging
import subprocess
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DockerCodeApplier:
    """Safely applies code modifications to Docker containers with backup and restore."""

    def __init__(self, container_name: str, backup_dir: str = "/tmp/original_backup"):
        self.container_name = container_name
        self.backup_dir = backup_dir
        self.original_files = {}  # Track original files for restore: {file_path: backup_path}

        # Ensure backup directory exists in container
        self._ensure_backup_dir()
    
    def _ensure_backup_dir(self):
        """Ensure backup directory exists in the container."""
        try:
            cmd = f"docker exec {self.container_name} mkdir -p {self.backup_dir}"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)
            logger.debug(f"Backup directory ensured: {self.backup_dir}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create backup directory: {e}")
            raise
    
    def _validate_code_fix(self, code_fix: Dict) -> bool:
        """Validate that code fix has required fields."""
        # Support both old format (file, modified_code) and new format (file_path, modified_content)
        if "file_path" in code_fix and "modified_content" in code_fix:
            # New format
            file_path = code_fix["file_path"]
        elif "file" in code_fix and "modified_code" in code_fix:
            # Old format
            file_path = code_fix["file"]
        else:
            logger.error(f"Missing required fields. Expected either (file_path, modified_content) or (file, modified_code)")
            return False

        # Check if file exists in container
        try:
            cmd = f"docker exec {self.container_name} test -f {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True)
            if result.returncode != 0:
                logger.error(f"File does not exist in container: {file_path}")
                return False
        except Exception as e:
            logger.error(f"Error checking file existence: {e}")
            return False

        return True
    
    def _create_original_backup(self, file_path: str) -> str:
        """Create a backup of the original file (only once per file)."""
        if file_path in self.original_files:
            # Already backed up, return existing backup path
            return self.original_files[file_path]

        # Create backup with simple naming: original_filename.ext
        file_name = Path(file_path).name
        backup_path = f"{self.backup_dir}/original_{file_name}"

        try:
            # Create backup in container
            cmd = f"docker exec {self.container_name} cp {file_path} {backup_path}"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)

            # Record the original file backup
            self.original_files[file_path] = backup_path

            logger.info(f"Created original backup: {file_path} -> {backup_path}")
            return backup_path

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create backup for {file_path}: {e}")
            raise
    
    def _apply_modification(self, code_fix: Dict[str, str], backup_path: str) -> Dict[str, Any]:
        """Atomically apply code modification using temporary file."""
        # Extract file path and content based on format
        file_path = code_fix.get("file_path") or code_fix.get("file")
        new_content = code_fix.get("modified_content") or code_fix.get("modified_code")
        temp_file = f"{file_path}.tmp_{datetime.now().strftime('%f')}"
        
        try:
            # Clean markdown code blocks if present (LLM often wraps code in ```css)
            cleaned_content = self._clean_markdown_blocks(new_content)

            # Write new content to temporary file in container using a safer method
            # First, write content to a local temp file, then copy to container
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.tmp') as local_temp:
                local_temp.write(cleaned_content)
                local_temp_path = local_temp.name

            try:
                # Copy local temp file to container
                cmd = f"docker cp {local_temp_path} {self.container_name}:{temp_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode != 0:
                    raise subprocess.CalledProcessError(result.returncode, cmd, result.stderr)
            finally:
                # Clean up local temp file
                os.unlink(local_temp_path)
            
            # Verify temporary file was created correctly
            cmd = f"docker exec {self.container_name} test -f {temp_file}"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)
            
            # Atomic move: replace original with temporary file
            cmd = f"docker exec {self.container_name} mv {temp_file} {file_path}"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)
            
            logger.info(f"Successfully applied modification to: {file_path}")
            
            return {
                "success": True,
                "applied_file": file_path,
                "backup_path": backup_path
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to apply modification to {file_path}: {e}")
            
            # Clean up temporary file if it exists
            try:
                cmd = f"docker exec {self.container_name} rm -f {temp_file}"
                subprocess.run(cmd, shell=True, capture_output=True)
            except:
                pass
            
            # Attempt to restore using backup
            try:
                self._restore_single_file(file_path, backup_path)
                logger.info(f"Restored {file_path} from backup")
            except Exception as restore_error:
                logger.error(f"Failed to restore {file_path}: {restore_error}")
            
            return {
                "success": False,
                "error": str(e),
                "applied_file": file_path
            }
        except Exception as e:
            logger.error(f"Unexpected error applying modification: {e}")
            return {
                "success": False,
                "error": str(e),
                "applied_file": file_path
            }
    

    
    def _restore_single_file(self, original_path: str, backup_path: str):
        """Restore a single file from its backup."""
        try:
            # Restore from backup
            cmd = f"docker exec {self.container_name} cp {backup_path} {original_path}"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)

            logger.info(f"Restored: {backup_path} -> {original_path}")

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to restore {original_path}: {e}")
            raise
    
    def apply_code_fix(self, code_fix: Dict[str, str]) -> Dict[str, Any]:
        """Apply a single code fix with original file backup."""
        # Extract file path based on format
        file_path = code_fix.get("file_path") or code_fix.get("file")
        logger.info(f"Applying code fix to: {file_path}")

        # 1. Validate code fix
        if not self._validate_code_fix(code_fix):
            return {"success": False, "error": "Invalid code fix"}

        # 2. Create original backup (only once per file)
        try:
            backup_path = self._create_original_backup(file_path)
        except Exception as e:
            return {"success": False, "error": f"Backup failed: {str(e)}"}

        # 3. Apply modification
        result = self._apply_modification(code_fix, backup_path)

        if result["success"]:
            logger.info(f"Code fix applied successfully to: {file_path}")

        return result
    
    def restore_all_files(self) -> Dict[str, Any]:
        """Restore all modified files to their original state."""
        if not self.original_files:
            logger.info("No files to restore")
            return {"success": True, "message": "No files to restore"}

        logger.info(f"Restoring {len(self.original_files)} files...")

        restore_results = []

        # Restore all files
        for original_path, backup_path in self.original_files.items():
            try:
                self._restore_single_file(original_path, backup_path)
                restore_results.append({
                    "file": original_path,
                    "success": True
                })
            except Exception as e:
                restore_results.append({
                    "file": original_path,
                    "success": False,
                    "error": str(e)
                })

        successful_restores = sum(1 for r in restore_results if r["success"])
        logger.info(f"Restore completed: {successful_restores}/{len(restore_results)} successful")

        return {
            "success": successful_restores == len(restore_results),
            "restore_results": restore_results,
            "total_files": len(restore_results)
        }

    def restore_from_backup_directory(self) -> Dict[str, Any]:
        """Restore all files by discovering original backup files in backup directory."""
        try:
            # Get all original backup files (format: original_filename.ext)
            cmd = f"docker exec {self.container_name} find {self.backup_dir} -name 'original_*' 2>/dev/null"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logger.info("No original backup files found")
                return {"success": True, "message": "No original backup files found"}

            backup_files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]

            if not backup_files:
                logger.info("No original backup files found")
                return {"success": True, "message": "No original backup files found"}

            logger.info(f"Found {len(backup_files)} original backup files to restore")

            restore_results = []

            # Restore each file from its original backup
            for backup_file in backup_files:
                try:
                    # Extract original filename from backup filename
                    backup_filename = Path(backup_file).name
                    # Format: original_filename.ext
                    original_filename = backup_filename.replace('original_', '')

                    # Reconstruct original path based on file extension
                    if original_filename.endswith('.less'):
                        # Try common LESS file locations
                        possible_paths = [
                            f"/var/www/html/assets/css/_form/{original_filename}",
                            f"/var/www/html/assets/css/_card/{original_filename}",
                            f"/var/www/html/assets/css/{original_filename}"
                        ]

                        original_path = None
                        for path in possible_paths:
                            if self._file_exists_in_container(path):
                                original_path = path
                                break

                        if not original_path:
                            logger.warning(f"Could not find original location for {original_filename}")
                            continue
                    else:
                        # For other file types, we'd need more logic
                        continue

                    logger.info(f"Restoring {original_path} from {backup_file}")

                    # Perform restore
                    cmd = f"docker exec {self.container_name} cp {backup_file} {original_path}"
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                    if result.returncode == 0:
                        restore_results.append({
                            "file": original_path,
                            "backup_used": backup_file,
                            "success": True
                        })
                        logger.info(f"Successfully restored {original_path}")
                    else:
                        restore_results.append({
                            "file": original_path,
                            "backup_used": backup_file,
                            "success": False,
                            "error": result.stderr
                        })
                        logger.error(f"Failed to restore {original_path}: {result.stderr}")

                except Exception as e:
                    restore_results.append({
                        "file": f"unknown ({backup_file})",
                        "success": False,
                        "error": str(e)
                    })
                    logger.error(f"Error restoring from {backup_file}: {e}")

            successful_restores = sum(1 for r in restore_results if r["success"])
            logger.info(f"Restore completed: {successful_restores}/{len(restore_results)} successful")

            return {
                "success": successful_restores == len(restore_results),
                "restore_results": restore_results,
                "total_files": len(restore_results)
            }

        except Exception as e:
            logger.error(f"Error during restore from backup directory: {e}")
            return {"success": False, "error": str(e)}

    def _clean_markdown_blocks(self, content: str) -> str:
        """Remove markdown code block markers and extract only the final CSS/LESS code."""
        import re

        # First, try to extract the last CSS/LESS code block
        code_blocks = re.findall(r'```(?:css|less)?\n(.*?)```', content, re.DOTALL)
        if code_blocks:
            # Use the last code block found
            return code_blocks[-1].strip()

        # If no code blocks found, clean line by line
        lines = content.split('\n')
        cleaned_lines = []
        in_code_block = False

        for line in lines:
            stripped = line.strip()

            # Skip markdown code block markers
            if stripped.startswith('```'):
                in_code_block = not in_code_block
                continue

            # Skip markdown formatting and notes
            if (stripped.startswith('**') and stripped.endswith('**')) or \
               stripped.startswith('*') or \
               stripped.lower().startswith('note:') or \
               stripped.lower().startswith('assuming'):
                continue

            # Only include lines that are inside code blocks or look like CSS/LESS
            if in_code_block or any(char in line for char in ['{', '}', ':', ';']) or stripped.startswith('.') or stripped.startswith('&'):
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _file_exists_in_container(self, file_path: str) -> bool:
        """Check if file exists in container."""
        try:
            cmd = f"docker exec {self.container_name} test -f {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True)
            return result.returncode == 0
        except:
            return False
    
    def cleanup_backups(self):
        """Clean up all backup files."""
        try:
            cmd = f"docker exec {self.container_name} rm -rf {self.backup_dir}/*"
            subprocess.run(cmd, shell=True, check=True, capture_output=True)
            logger.info("Backup files cleaned up")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to cleanup backups: {e}")
    
    def get_modified_files_summary(self) -> Dict[str, Any]:
        """Get summary of all modified files."""
        return {
            "total_files": len(self.original_files),
            "files": [
                {
                    "original_file": original_path,
                    "backup_file": backup_path
                }
                for original_path, backup_path in self.original_files.items()
            ]
        }

    def restore_from_backup_directory(self) -> Dict[str, Any]:
        """Restore all files from backup directory."""
        try:
            # List all backup files
            cmd = f"docker exec {self.container_name} find {self.backup_dir} -name 'original_*' -type f"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logger.info("No backup files found")
                return {"success": True, "message": "No backup files found"}

            backup_files = result.stdout.strip().split('\n')
            backup_files = [f for f in backup_files if f.strip()]

            if not backup_files:
                logger.info("No backup files found")
                return {"success": True, "message": "No backup files found"}

            logger.info(f"Found {len(backup_files)} backup files to restore")

            restore_results = []
            for backup_file in backup_files:
                # Extract original file path from backup filename
                backup_name = backup_file.split('/')[-1]  # Get filename
                if backup_name.startswith('original_'):
                    original_name = backup_name[9:]  # Remove 'original_' prefix

                    # Find the original file path by searching for files with this name
                    find_cmd = f"docker exec {self.container_name} find /var/www/html -name '{original_name}' -type f"
                    find_result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)

                    if find_result.returncode == 0 and find_result.stdout.strip():
                        original_path = find_result.stdout.strip().split('\n')[0]  # Take first match

                        try:
                            self._restore_single_file(original_path, backup_file)
                            restore_results.append({
                                "file": original_path,
                                "success": True,
                                "backup_file": backup_file
                            })
                            logger.info(f"Restored {original_path} from {backup_file}")
                        except Exception as e:
                            restore_results.append({
                                "file": original_path,
                                "success": False,
                                "error": str(e),
                                "backup_file": backup_file
                            })
                            logger.error(f"Failed to restore {original_path}: {e}")
                    else:
                        logger.warning(f"Could not find original file for backup: {backup_file}")

            successful_restores = sum(1 for r in restore_results if r["success"])

            return {
                "success": successful_restores > 0,
                "total_backups": len(backup_files),
                "successful_restores": successful_restores,
                "restore_results": restore_results
            }

        except Exception as e:
            logger.error(f"Error restoring from backup directory: {e}")
            return {"success": False, "error": str(e)}

    def build_project(self) -> Dict[str, Any]:
        """Build the project using yarn build-dev."""
        logger.info("Building project with yarn build-dev...")
        try:
            cmd = f"docker exec {self.container_name} yarn build-dev"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

            # Check if the error is just browserslist warning (not a real build failure)
            stderr_lower = result.stderr.lower()
            stdout_lower = result.stdout.lower()

            # Check for real syntax errors
            has_syntax_error = (
                "unrecognised input" in stderr_lower or
                "syntax error" in stderr_lower or
                "parse error" in stderr_lower or
                "error in" in stderr_lower
            )

            # Check for browserslist warning only
            is_only_browserslist_warning = (
                "browserslist" in stderr_lower and
                "caniuse-lite is outdated" in stderr_lower and
                not has_syntax_error
            )

            if result.returncode == 0:
                logger.info("Project built successfully")
                return {"success": True, "output": result.stdout}
            elif is_only_browserslist_warning:
                logger.warning("Build completed with browserslist warning (ignoring)")
                logger.warning(f"Warning: {result.stderr}")
                return {"success": True, "output": result.stdout, "warning": result.stderr}
            else:
                logger.error(f"Build failed: {result.stderr}")
                return {"success": False, "error": result.stderr, "output": result.stdout}

        except subprocess.TimeoutExpired:
            logger.error("Build timed out after 5 minutes")
            return {"success": False, "error": "Build timeout"}
        except Exception as e:
            logger.error(f"Build error: {e}")
            return {"success": False, "error": str(e)}


def restore_and_build(container_name: str, build: bool = True) -> Dict[str, Any]:
    """Restore all files to original state and optionally rebuild."""
    applier = DockerCodeApplier(container_name)

    # Restore all files from backup directory
    restore_result = applier.restore_from_backup_directory()
    if not restore_result["success"]:
        return restore_result

    if build:
        # Build project
        build_result = applier.build_project()
        return {
            "success": build_result["success"],
            "restore_result": restore_result,
            "build_result": build_result
        }
    else:
        return {
            "success": True,
            "restore_result": restore_result,
            "build_result": {"success": True, "message": "Build skipped"}
        }


def apply_code_fixes_from_file(code_fixes_file: Path, container_name: str) -> Dict[str, Any]:
    """Apply code fixes from a JSON file."""
    try:
        with open(code_fixes_file, 'r') as f:
            code_fixes_data = json.load(f)
        
        applier = DockerCodeApplier(container_name)
        
        code_fixes = code_fixes_data.get("code_fixes", [])
        if not code_fixes:
            logger.warning(f"No code fixes found in {code_fixes_file}")
            return {"success": False, "error": "No code fixes found"}
        
        results = []
        for i, code_fix in enumerate(code_fixes):
            logger.info(f"Applying code fix {i+1}/{len(code_fixes)}")
            result = applier.apply_code_fix(code_fix)
            results.append(result)
            
            if not result["success"]:
                logger.error(f"Failed to apply code fix {i+1}: {result.get('error', 'Unknown error')}")
                # Continue with other fixes rather than stopping
        
        successful_fixes = sum(1 for r in results if r["success"])
        logger.info(f"Applied {successful_fixes}/{len(code_fixes)} code fixes successfully")

        # Build project after applying fixes
        build_result = applier.build_project()

        return {
            "success": successful_fixes > 0 and build_result["success"],
            "total_fixes": len(code_fixes),
            "successful_fixes": successful_fixes,
            "results": results,
            "build_result": build_result,
            "applier_summary": applier.get_modified_files_summary()
        }
        
    except Exception as e:
        logger.error(f"Error applying code fixes from {code_fixes_file}: {e}")
        return {"success": False, "error": str(e)}


def main():
    parser = argparse.ArgumentParser(description="Apply code fixes to Docker container")
    parser.add_argument("--code_fixes_file", help="JSON file containing code fixes (required for apply mode)")
    parser.add_argument("--container", required=True, help="Docker container name")
    parser.add_argument("--restore", action="store_true", help="Restore all files to original state instead of applying")

    args = parser.parse_args()

    if args.restore:
        # Restore mode - restore all files and rebuild
        result = restore_and_build(args.container)
        if result["success"]:
            logger.info("All files restored and project built successfully")
        else:
            logger.error("Restore or build failed")
            if not result["restore_result"]["success"]:
                logger.error(f"Restore failed: {result['restore_result'].get('error', 'Unknown error')}")
            if not result["build_result"]["success"]:
                logger.error(f"Build failed: {result['build_result'].get('error', 'Unknown error')}")
    else:
        # Apply mode
        if not args.code_fixes_file:
            parser.error("--code_fixes_file is required when not using --restore")

        code_fixes_file = Path(args.code_fixes_file)
        if not code_fixes_file.exists():
            logger.error(f"Code fixes file not found: {code_fixes_file}")
            return

        result = apply_code_fixes_from_file(code_fixes_file, args.container)
        if result["success"]:
            logger.info(f"Successfully applied {result['successful_fixes']}/{result['total_fixes']} code fixes")
        else:
            logger.error(f"Failed to apply code fixes: {result.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main()
