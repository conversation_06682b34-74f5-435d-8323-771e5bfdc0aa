#!/usr/bin/env python3
"""
Design Judge for Go-Browse: Analyzes UI design issues in negative trajectories.

This script processes a Go-Browse run directory and generates design feedback
for each step in negative trajectories where UI design issues may have caused
agent failures.
"""

import argparse
import base64
import json
import logging
import os
import glob
import re
import subprocess
from pathlib import Path
from urllib.parse import urlparse
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
import numpy as np
import requests
from textwrap import dedent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QwenVisionClient:
    """Client for Qwen VL completion endpoint with vision capabilities."""

    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key

    def chat_completions_create(self, model: str, messages: list, max_tokens: int = 512, temperature: float = 0.0):
        """Create a chat completion with vision using Qwen VL endpoint."""
        # Extract text and image from messages
        text_content = ""
        image_data = None

        for message in messages:
            if message["role"] == "system":
                text_content += f"System: {message['content']}\n\n"
            elif message["role"] == "user":
                content = message["content"]
                if isinstance(content, list):
                    # Handle multimodal content
                    for item in content:
                        if item["type"] == "text":
                            text_content += f"User: {item['text']}\n\n"
                        elif item["type"] == "image_url":
                            # Extract base64 image data
                            image_url = item["image_url"]["url"]
                            if image_url.startswith("data:image/"):
                                image_data = image_url.split(",")[1]  # Remove data:image/jpeg;base64, prefix
                else:
                    text_content += f"User: {content}\n\n"

        text_content += "Assistant:"

        # Prepare payload for Qwen VL
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": text_content}
                    ]
                }
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Add image if present
        if image_data:
            payload["messages"][0]["content"].append({
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}
            })

        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        response = requests.post(
            f"{self.base_url}/v1/chat/completions",
            json=payload,
            headers=headers,
            timeout=60
        )
        response.raise_for_status()

        qwen_response = response.json()
        return QwenVisionResponse(qwen_response)

class QwenVisionResponse:
    """Wrapper for Qwen VL response to match OpenAI format."""

    def __init__(self, qwen_response: dict):
        self.qwen_response = qwen_response
        self.choices = [QwenVisionChoice(qwen_response)]
        self.usage = QwenVisionUsage(qwen_response)

class QwenVisionChoice:
    def __init__(self, qwen_response: dict):
        self.message = QwenVisionMessage(qwen_response)

class QwenVisionMessage:
    def __init__(self, qwen_response: dict):
        # Try multiple possible response formats for Qwen
        self.content = ""

        # Format 1: Standard OpenAI-like format with choices
        if "choices" in qwen_response and len(qwen_response["choices"]) > 0:
            choice = qwen_response["choices"][0]
            if isinstance(choice, dict):
                if "message" in choice and isinstance(choice["message"], dict):
                    self.content = choice["message"].get("content", "")
                elif "text" in choice:
                    self.content = choice["text"]
                else:
                    self.content = str(choice)

        # Format 2: Direct content field
        elif "content" in qwen_response:
            self.content = qwen_response["content"]

        # Format 3: Direct text field
        elif "text" in qwen_response:
            self.content = qwen_response["text"]

        # Format 4: Response field (some Qwen implementations)
        elif "response" in qwen_response:
            self.content = qwen_response["response"]

        # Ensure content is a string
        if not isinstance(self.content, str):
            self.content = str(self.content) if self.content is not None else ""

class QwenVisionUsage:
    def __init__(self, qwen_response: dict):
        usage_data = qwen_response.get("usage", {})
        self.prompt_tokens = usage_data.get("prompt_tokens", 0)
        self.completion_tokens = usage_data.get("completion_tokens", 0)
        self.total_tokens = usage_data.get("total_tokens", 0)

    def to_dict(self):
        return {
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens
        }

def image_to_jpg_base64_url(image_array: np.ndarray) -> str:
    """Convert numpy image array to base64 encoded JPEG URL."""
    if isinstance(image_array, np.ndarray):
        img = Image.fromarray(image_array)
        import io
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG')
        img_bytes = buffer.getvalue()
        img_base64 = base64.b64encode(img_bytes).decode('utf-8')
        return f"data:image/jpeg;base64,{img_base64}"
    return ""

class DesignJudge:
    """Judge that analyzes UI design issues in failed trajectories."""
    
    def __init__(self, model_name: str, base_url: str = "http://localhost:8000", api_key: str = None, container_name: str = None, codebase_index_path: str = None):
        self.model_name = model_name
        self.client = QwenVisionClient(base_url, api_key)
        self.container_name = container_name
        self.codebase_index = None

        # Load codebase index if provided
        if codebase_index_path and os.path.exists(codebase_index_path):
            try:
                with open(codebase_index_path, 'r', encoding='utf-8') as f:
                    self.codebase_index = json.load(f)
                logger.info(f"Loaded codebase index with {len(self.codebase_index.get('indexed_files', {}))} files")
            except Exception as e:
                logger.warning(f"Failed to load codebase index: {e}")
                self.codebase_index = None

    def find_relevant_code_segments(self, element_classes: List[str], element_type: str, element_text: str = "") -> List[Dict]:
        """Find relevant source files (CSS/LESS/JS/SVG) using AI coder to judge relevance."""
        if not self.codebase_index:
            return []

        indexed_files = self.codebase_index.get('indexed_files', {})

        # Filter to project files only (exclude vendor/node_modules/third-party)
        project_files = {}
        for file_path, file_metadata in indexed_files.items():
            # Prioritize project's own source files (CSS/LESS/JS/SVG)
            if any(exclude in file_path.lower() for exclude in ['vendor/', 'node_modules/', 'coverage/', 'symfony/', 'zurb']):
                continue
            project_files[file_path] = file_metadata

        # If no project files, fall back to all files but limit to reasonable ones
        if not project_files:
            project_files = {k: v for k, v in list(indexed_files.items())[:20]}

        # Use AI coder to judge relevance
        return self._judge_file_relevance_with_ai(project_files, element_classes, element_type, element_text)

    def _judge_file_relevance_with_ai(self, files: Dict, element_classes: List[str], element_type: str, element_text: str) -> List[Dict]:
        """Use Qwen Coder to judge which files are most relevant for the UI element."""
        if not files:
            return []

        # Prepare file information for AI judgment
        file_summaries = []
        for file_path, metadata in files.items():
            summary = {
                'file_path': file_path,
                'functionality': metadata.get('functionality', ''),
                'ui_components': metadata.get('ui_components', []),
                'css_selectors': metadata.get('css_selectors', [])[:15],  # Limit for context
                'symbols': metadata.get('symbols', [])[:10]
            }
            file_summaries.append(summary)

        # Build prompt for AI judgment
        element_info = {
            'element_type': element_type,
            'element_classes': element_classes,
            'element_text': element_text
        }

        prompt = f"""You are analyzing a UI element to find the most relevant source files for fixing UI design issues.

Element Information:
- Type: {element_type}
- CSS Classes: {', '.join(element_classes)}
- Text: {element_text}

Available Files:
{json.dumps(file_summaries, indent=2)}

Please select the TOP 5 most relevant files for fixing this UI element. Consider ALL file types:
1. CSS/LESS files: For static styling, layout, colors, fonts, spacing
2. JavaScript files: For interactive behavior, dynamic styling, event handling, state management
3. SVG files: For icons and graphics
4. Controller files: For component-specific logic (dropdown, button, form interactions)

Focus on:
- Files that contain CSS selectors matching the element's classes
- JavaScript controllers that handle the same UI component type
- Files that manage the element's interactive behavior or dynamic states
- Project-specific files over third-party libraries

Return ONLY a JSON array of file paths in order of relevance (most relevant first):
["file1.css", "file2.less", ...]"""

        try:
            response = self._call_qwen_coder(prompt)
            if response and response.strip():
                # Parse the JSON response
                import re
                json_match = re.search(r'\[.*\]', response, re.DOTALL)
                if json_match:
                    selected_files = json.loads(json_match.group())

                    # Build result with selected files
                    result = []
                    for file_path in selected_files[:5]:
                        if file_path in files:
                            metadata = files[file_path]
                            result.append({
                                'file_path': file_path,
                                'functionality': metadata.get('functionality', ''),
                                'ui_components': metadata.get('ui_components', []),
                                'css_selectors': metadata.get('css_selectors', []),
                                'symbols': metadata.get('symbols', [])
                            })
                    return result
        except Exception as e:
            logger.warning(f"AI file relevance judgment failed: {e}")

        # Fallback: return first 5 files
        return [{'file_path': fp, **metadata} for fp, metadata in list(files.items())[:5]]

    def build_design_judge_prompt(self, current_step: Dict, next_step: Optional[Dict],
                                task_goal: str, eval_thoughts: str, axtree: str = "") -> Tuple[str, str]:
        """Build the prompt for design judgment."""
        system_msg = dedent("""\
            You are an expert UI/UX designer analyzing web navigation agent failures. Given the current webpage UI, the agent's current action and thoughts, the agent's next action and thoughts, your goal is to determine if a UI design issue caused the agent's failure, what the problematic UI element is, its issue and provide specific improvement suggestions.

            The problematic UI element you point out should be specific and strictly one element in the webpage, and the improvement suggestions should also be specific to that problematic UI element.

            *IMPORTANT*
            Format your response exactly as shown below:

            UI_DESIGN_ISSUE: YES/NO
            PROBLEMATIC_ELEMENT: [Specific UI element that caused the issue]
            PRIMARY_REASON: [Single most important UI design issue that caused the failure]
            PRIMARY_IMPROVEMENT: [Single most important improvement suggestion]
            """)

        # Use raw action content which includes both thought and action
        current_raw_action = current_step.get('action', 'N/A')

        next_info = ""
        if next_step:
            next_raw_action = next_step.get('action', 'N/A')
            next_info = f"\n\n# Next Step Action\n{next_raw_action}"

        axtree_info = ""
        if axtree:
            axtree_info = f"\n\n# Current Page Accessibility Tree\n{axtree}"

        prompt = f"""# Task Goal
{task_goal}

# Current Step Action
{current_raw_action}{next_info}

# Evaluation Agent's Analysis
{eval_thoughts}{axtree_info}

# Instructions
Analyze the UI screenshot shown in the image and the accessibility tree to determine if UI design issues contributed to the agent's failure. The screenshot shows the exact state of the webpage when the agent performed the current action."""

        return prompt, system_msg
    
    def analyze_step(self, current_step: Dict, next_step: Optional[Dict],
                    screenshot: np.ndarray, task_goal: str, eval_thoughts: str, axtree: str = "",
                    source_files: List[str] = None) -> Optional[Dict]:
        """Analyze a single step for UI design issues."""
        try:
            prompt, system_msg = self.build_design_judge_prompt(
                current_step, next_step, task_goal, eval_thoughts, axtree
            )

            # Convert screenshot to base64
            jpg_base64_str = image_to_jpg_base64_url(screenshot)

            messages = [
                {"role": "system", "content": system_msg},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": jpg_base64_str}},
                    ],
                }
            ]

            logger.info(f"Sending request to judge model with screenshot size: {screenshot.shape}")

            response = self.client.chat_completions_create(
                model=self.model_name,
                messages=messages,
                max_tokens=512,
                temperature=0.0
            )

            response_text = response.choices[0].message.content
            logger.info(f"Judge response: {response_text}")

            # Parse the response
            lines = response_text.strip().split('\n')
            ui_issue_line = next((line for line in lines if 'UI_DESIGN_ISSUE:' in line), '')
            is_ui_issue = 'YES' in ui_issue_line.upper()

            if not is_ui_issue:
                logger.info("No UI design issue identified for this step")
                return None  # No UI design issue, don't generate feedback

            # Extract fields from response
            problematic_element = ""
            primary_reason = ""
            primary_improvement = ""

            for line in lines:
                if 'PROBLEMATIC_ELEMENT:' in line:
                    problematic_element = line.split('PROBLEMATIC_ELEMENT:')[1].strip()
                elif 'PRIMARY_REASON:' in line:
                    primary_reason = line.split('PRIMARY_REASON:')[1].strip()
                elif 'PRIMARY_IMPROVEMENT:' in line:
                    primary_improvement = line.split('PRIMARY_IMPROVEMENT:')[1].strip()

            feedback = {
                "is_ui_design_issue": True,
                "problematic_element": problematic_element,
                "primary_reason": primary_reason,
                "primary_improvement": primary_improvement,
                "raw_response": response_text,
                "model_usage": response.usage.to_dict()
            }

            # Extract and add URL information
            observation = current_step.get('observation', {})
            last_action = observation.get('last_action', '')
            url = self._extract_url_from_action(last_action)
            if url:
                feedback["url"] = url

            # Use Qwen Coder for comprehensive HTML analysis and find relevant code segments
            if self.container_name and problematic_element:
                # Get HTML analysis using Qwen Coder
                html_analysis = self._analyze_html_with_qwen_coder(current_step, problematic_element)
                if html_analysis:
                    feedback["html_analysis"] = html_analysis

                    # Extract element information for code segment search
                    element_classes = html_analysis.get('css_classes', [])
                    element_type = html_analysis.get('element_type', '')

                    # Find relevant code segments using codebase index
                    if self.codebase_index:
                        code_segments = self.find_relevant_code_segments(element_classes, element_type)
                        if code_segments:
                            feedback["code_segments"] = code_segments
                            logger.info(f"Found {len(code_segments)} relevant code segments")
                    else:
                        # Fallback to old method if no codebase index
                        if source_files:
                            code_snippets = self._find_code_snippets_with_qwen_coder(html_analysis, source_files)
                            if code_snippets:
                                feedback["code_snippets"] = code_snippets

            # Add source files list for backward compatibility
            if source_files:
                feedback["source_files"] = source_files

            logger.info(f"Generated feedback - Reason: {primary_reason[:100]}...")
            return feedback

        except Exception as e:
            logger.error(f"Error analyzing step: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _find_relevant_code_snippets(self, problematic_element: str, source_files: List[str],
                                   current_step: Dict = None) -> List[Dict[str, str]]:
        """Find relevant code snippets (CSS/LESS/JS/SVG) using Qwen Coder to analyze HTML structure."""
        if not self.container_name or not problematic_element or not current_step:
            return []

        # Extract comprehensive DOM information using Qwen Coder
        dom_analysis = self._analyze_html_with_qwen_coder(current_step, problematic_element)

        if not dom_analysis:
            logger.debug("Could not analyze HTML with Qwen Coder")
            return []

        # Use Qwen Coder to find relevant source files and code snippets
        snippets = self._find_code_snippets_with_qwen_coder(dom_analysis, source_files)

        return snippets[:5]  # Limit to top 5 most relevant snippets

    def _analyze_html_with_qwen_coder(self, current_step: Dict, problematic_element: str) -> Dict:
        """Use Qwen Coder to analyze HTML structure and identify the problematic element."""
        try:
            # Extract data from step
            observation = current_step.get('observation', {})
            pruned_html = observation.get('pruned_html', '')
            axtree_txt = observation.get('axtree_txt', '')
            last_action = observation.get('last_action', '')
            url = self._extract_url_from_action(last_action)

            if not pruned_html or not axtree_txt:
                return {}

            # Create prompt for Qwen Coder to analyze HTML
            prompt = f"""# HTML Structure Analysis Task

You are analyzing a web page to identify a problematic UI element. Your task is to:
1. Find the element described as: {problematic_element}
2. Extract comprehensive information about this element
3. Generate accurate CSS selectors
4. Identify parent/child relationships
5. Find existing CSS classes and states

## URL
{url}

## Accessibility Tree (axtree)
{axtree_txt}

## HTML Content
{pruned_html}

## Instructions
Analyze the HTML and axtree to find the element "{problematic_element}". Return a JSON object with:
- element_type: HTML tag name
- element_text: visible text content
- bid: element ID from axtree
- css_classes: array of CSS classes
- css_selectors: array of possible CSS selectors
- parent_chain: array of parent elements with their classes
- siblings: array of sibling elements
- existing_states: array of existing state classes (like .active, .selected)
- aria_attributes: object with aria-* attributes
- html_context: the actual HTML snippet of the element

Be precise and don't make assumptions. Only return information that exists in the provided HTML/axtree.
"""

            # Call Qwen Coder
            response = self._call_qwen_coder(prompt)

            if response:
                try:
                    import json
                    # Clean the response - remove code block markers
                    cleaned_response = response.strip()
                    if cleaned_response.startswith('```json'):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith('```'):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()

                    analysis = json.loads(cleaned_response)
                    logger.info(f"Qwen Coder HTML analysis: {analysis}")
                    return analysis
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse Qwen Coder response as JSON: {response}")

            return {}

        except Exception as e:
            logger.error(f"Error in HTML analysis with Qwen Coder: {e}")
            return {}

    def _call_qwen_coder(self, prompt: str) -> str:
        """Call Qwen Coder API for code analysis."""
        try:
            import requests

            # Use Qwen Coder endpoint (port 8001)
            url = "http://localhost:8001/v1/chat/completions"

            payload = {
                "model": "Qwen/Qwen2.5-Coder-32B-Instruct",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.0,
                "max_tokens": 2048
            }

            headers = {"Content-Type": "application/json"}

            response = requests.post(url, json=payload, headers=headers, timeout=60)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]

            return ""

        except Exception as e:
            logger.error(f"Error calling Qwen Coder: {e}")
            return ""

    def _find_code_snippets_with_qwen_coder(self, dom_analysis: Dict, source_files: List[str]) -> List[Dict]:
        """Use Qwen Coder to find relevant code snippets based on DOM analysis."""
        if not dom_analysis or not source_files:
            return []

        snippets = []

        # Filter relevant source files (CSS, JS, SVG)
        relevant_files = [f for f in source_files if f.endswith(('.css', '.less', '.scss', '.js', '.svg'))]

        for source_file in relevant_files:
            try:
                # Read source file content
                cmd = f"docker exec {self.container_name} cat {source_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode != 0:
                    continue

                file_content = result.stdout

                # Use Qwen Coder to find relevant code
                file_type = "CSS/LESS" if source_file.endswith(('.css', '.less', '.scss')) else "JavaScript" if source_file.endswith('.js') else "SVG"

                prompt = f"""# Source Code Analysis Task

Find code that is relevant to this HTML element:

## Element Information
- Type: {dom_analysis.get('element_type', '')}
- Classes: {dom_analysis.get('css_classes', [])}
- Text: {dom_analysis.get('element_text', '')}
- CSS Selectors: {dom_analysis.get('css_selectors', [])}
- Parent Chain: {dom_analysis.get('parent_chain', [])}

## {file_type} File: {source_file}
{file_content}

## Instructions
Find code that could affect this element. Look for:
1. CSS/LESS: Rules targeting the element's classes, parent elements, similar UI components, state-related rules
2. JavaScript: Controllers, event handlers, dynamic styling, state management for this element type
3. SVG: Icons or graphics related to this element

Return a JSON array of relevant code blocks. Each block should have:
- line_number: starting line number
- code: the complete code block (CSS rule, JS function, SVG element, etc.)
- matched_term: what matched (class name, element type, etc.)
- match_type: type of match (css_class, element_type, parent_selector, etc.)
- priority: relevance score 1-10

Only return actual CSS rules that exist in the file. Don't make up CSS.
"""

                response = self._call_qwen_coder(prompt)

                if response:
                    try:
                        import json
                        import re

                        # Extract JSON from response - look for ```json blocks
                        json_match = re.search(r'```json\s*(\[.*?\])\s*```', response, re.DOTALL)
                        if json_match:
                            json_str = json_match.group(1)
                            code_blocks = json.loads(json_str)

                            for block in code_blocks:
                                if isinstance(block, dict) and 'code' in block:
                                    block['file'] = source_file
                                    snippets.append(block)
                        else:
                            # Try to parse the entire response as JSON
                            cleaned_response = response.strip()
                            if cleaned_response.startswith('```json'):
                                cleaned_response = cleaned_response[7:]
                            if cleaned_response.endswith('```'):
                                cleaned_response = cleaned_response[:-3]
                            cleaned_response = cleaned_response.strip()

                            code_blocks = json.loads(cleaned_response)

                            for block in code_blocks:
                                if isinstance(block, dict) and 'code' in block:
                                    block['file'] = source_file
                                    snippets.append(block)

                    except (json.JSONDecodeError, AttributeError) as e:
                        logger.error(f"Failed to parse code analysis response: {e}")
                        logger.debug(f"Response was: {response[:500]}...")

            except Exception as e:
                logger.error(f"Error analyzing source file {source_file}: {e}")

        # Sort by priority and return top results
        snippets.sort(key=lambda x: x.get('priority', 0), reverse=True)
        return snippets

    def _extract_dom_info_from_step(self, current_step: Dict, problematic_element: str) -> Dict[str, any]:
        """Extract comprehensive DOM information for the problematic element from step data."""
        dom_info = {
            'css_classes': [],
            'element_type': '',
            'element_text': '',
            'element_id': '',
            'bid': '',
            'aria_label': '',
            'url': '',
            'html_context': '',
            'css_selectors': [],
            'parent_elements': [],
            'sibling_elements': []
        }

        try:
            # Get URL from step data
            observation = current_step.get('observation', {})
            last_action = observation.get('last_action', '')
            url = self._extract_url_from_action(last_action)
            dom_info['url'] = url

            # Get HTML and axtree from observation
            pruned_html = observation.get('pruned_html', '')
            axtree_txt = observation.get('axtree_txt', '')

            # Extract element information from problematic_element description
            element_text = self._extract_text_from_element_description(problematic_element)
            dom_info['element_text'] = element_text

            # Find element in axtree by text content
            element_id = self._find_element_id_in_axtree(axtree_txt, element_text)
            if element_id:
                dom_info['element_id'] = element_id

                # Find corresponding HTML element by bid and extract comprehensive info
                html_analysis = self._analyze_html_element(pruned_html, element_id, element_text)
                if html_analysis:
                    dom_info.update(html_analysis)

            logger.debug(f"Extracted comprehensive DOM info: {dom_info}")
            return dom_info

        except Exception as e:
            logger.debug(f"Error extracting DOM info: {e}")
            return dom_info

    def _extract_url_from_action(self, last_action: str) -> str:
        """Extract URL from the last action."""
        import re
        match = re.search(r"goto\(['\"]([^'\"]+)['\"]\)", last_action)
        if match:
            return match.group(1)
        return ""

    def _extract_text_from_element_description(self, problematic_element: str) -> str:
        """Extract the text content from element description."""
        # Handle patterns like "[button 'Sort by: Controversial']", "[button \"Sort by: Hot\"]", or "[Sort by: Hot] button"
        import re

        # Look for text in single quotes
        quote_match = re.search(r"'([^']+)'", problematic_element)
        if quote_match:
            return quote_match.group(1)

        # Look for text in double quotes
        double_quote_match = re.search(r'"([^"]+)"', problematic_element)
        if double_quote_match:
            return double_quote_match.group(1)

        # Look for text in brackets without quotes
        bracket_match = re.search(r'\[([^\]]+)\]', problematic_element)
        if bracket_match:
            text = bracket_match.group(1)
            # Remove element type words
            element_types = ['button', 'link', 'input', 'select', 'div', 'span']
            for elem_type in element_types:
                text = text.replace(elem_type, '').strip()
            return text

        return problematic_element

    def _find_element_id_in_axtree(self, axtree_txt: str, element_text: str) -> str:
        """Find element ID in accessibility tree by text content."""
        if not element_text or not axtree_txt:
            return ""

        lines = axtree_txt.split('\n')
        for line in lines:
            if element_text in line:
                # Extract element ID from line like "[81] button 'Sort by: Controversial'"
                import re
                id_match = re.search(r'\[(\d+)\]', line)
                if id_match:
                    return id_match.group(1)
        return ""

    def _analyze_html_element(self, pruned_html: str, element_id: str, element_text: str) -> Dict[str, any]:
        """Comprehensively analyze HTML element and its context."""
        element_info = {
            'css_classes': [],
            'element_type': '',
            'bid': element_id,
            'aria_label': '',
            'html_context': '',
            'css_selectors': [],
            'parent_elements': [],
            'sibling_elements': []
        }

        if not element_id or not pruned_html:
            return element_info

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(pruned_html, 'html.parser')

            # Find element with matching bid
            element = soup.find(attrs={'bid': element_id})
            if element:
                # Basic element info
                element_info['element_type'] = element.name

                # Extract CSS classes
                css_classes = element.get('class', [])
                if isinstance(css_classes, list):
                    element_info['css_classes'] = css_classes
                elif isinstance(css_classes, str):
                    element_info['css_classes'] = css_classes.split()

                # Extract aria-label
                aria_label = element.get('aria-label', '')
                if aria_label:
                    element_info['aria_label'] = aria_label

                # Generate CSS selectors based on actual HTML structure
                element_info['css_selectors'] = self._generate_css_selectors(element)

                # Extract HTML context (element + some surrounding context)
                element_info['html_context'] = self._extract_html_context(element)

                # Analyze parent elements
                element_info['parent_elements'] = self._analyze_parent_elements(element)

                # Analyze sibling elements (for dropdown menus, etc.)
                element_info['sibling_elements'] = self._analyze_sibling_elements(element, element_text)

            return element_info

        except Exception as e:
            logger.debug(f"Error analyzing HTML element: {e}")
            return element_info

    def _generate_css_selectors(self, element) -> List[str]:
        """Generate possible CSS selectors for the element."""
        selectors = []

        # Element type selector
        if element.name:
            selectors.append(element.name)

        # Class-based selectors
        css_classes = element.get('class', [])
        if css_classes:
            # Single class selectors
            for cls in css_classes:
                selectors.append(f".{cls}")

            # Combined class selector
            if len(css_classes) > 1:
                selectors.append("." + ".".join(css_classes))

            # Element + class selectors
            if element.name:
                for cls in css_classes:
                    selectors.append(f"{element.name}.{cls}")

        # ID selector (if exists)
        element_id = element.get('id')
        if element_id:
            selectors.append(f"#{element_id}")

        # Attribute selectors
        aria_label = element.get('aria-label')
        if aria_label:
            selectors.append(f'[aria-label="{aria_label}"]')

        return selectors

    def _extract_html_context(self, element) -> str:
        """Extract HTML context around the element."""
        try:
            # Get the element and its immediate parent for context
            parent = element.parent
            if parent:
                return str(parent)[:500]  # Limit context size
            else:
                return str(element)[:200]
        except:
            return ""

    def _analyze_parent_elements(self, element) -> List[Dict[str, str]]:
        """Analyze parent elements for context."""
        parents = []
        current = element.parent
        depth = 0

        while current and depth < 3:  # Limit to 3 levels up
            if current.name:
                parent_info = {
                    'tag': current.name,
                    'classes': current.get('class', []),
                    'id': current.get('id', ''),
                    'depth': depth
                }
                parents.append(parent_info)
            current = current.parent
            depth += 1

        return parents

    def _analyze_sibling_elements(self, element, element_text: str) -> List[Dict[str, str]]:
        """Analyze sibling elements (useful for dropdown menus, tabs, etc.)."""
        siblings = []

        if element.parent:
            for sibling in element.parent.find_all(element.name, recursive=False):
                if sibling != element:
                    sibling_info = {
                        'tag': sibling.name,
                        'classes': sibling.get('class', []),
                        'text': sibling.get_text(strip=True)[:50],
                        'aria_label': sibling.get('aria-label', '')
                    }
                    siblings.append(sibling_info)

        return siblings[:5]  # Limit to 5 siblings

    def _extract_element_keywords(self, problematic_element: str) -> List[str]:
        """Extract keywords from problematic element description."""
        keywords = []

        # Common UI element mappings
        element_mappings = {
            'button': ['btn', 'button', 'submit', 'form-control', 'control'],
            'form': ['form', 'input', 'field', 'form-control', 'control'],
            'navigation': ['nav', 'menu', 'navbar'],
            'card': ['card', 'panel', 'box', 'dropdown-card'],
            'dropdown': ['dropdown', 'select', 'menu', 'dropdown-card'],
            'modal': ['modal', 'dialog', 'popup'],
            'table': ['table', 'row', 'cell'],
            'header': ['header', 'title', 'heading'],
            'footer': ['footer'],
            'sidebar': ['sidebar', 'aside'],
            'content': ['content', 'main', 'body'],
            'sort': ['sort', 'dropdown', 'menu', 'control'],
            'controversial': ['dropdown', 'menu', 'control'],
            'hot': ['dropdown', 'menu', 'control']
        }

        element_lower = problematic_element.lower()

        # Add direct keywords from element description
        words = re.findall(r'\b\w+\b', element_lower)
        keywords.extend(words)

        # Add mapped keywords
        for element_type, css_classes in element_mappings.items():
            if element_type in element_lower:
                keywords.extend(css_classes)

        return list(set(keywords))

    def _search_css_by_dom_info(self, content: str, dom_info: Dict[str, any], file_path: str) -> List[Dict[str, str]]:
        """Search CSS content using DOM information (CSS classes, element type, etc.)."""
        snippets = []
        lines = content.split('\n')

        # Priority search terms based on DOM info
        search_terms = []

        # 1. CSS classes (highest priority)
        for css_class in dom_info.get('css_classes', []):
            search_terms.append((css_class, 'css_class', 10))

            # Handle BEM naming convention (e.g., dropdown__toggle -> dropdown and __toggle)
            if '__' in css_class:
                base_class, modifier = css_class.split('__', 1)
                search_terms.append((base_class, 'css_class_base', 9))
                search_terms.append(('__' + modifier, 'css_class_modifier', 8))
            elif '--' in css_class:
                base_class, variant = css_class.split('--', 1)
                search_terms.append((base_class, 'css_class_base', 9))
                search_terms.append(('--' + variant, 'css_class_variant', 8))

        # 2. Element type + common patterns
        element_type = dom_info.get('element_type', '')
        if element_type:
            search_terms.append((element_type, 'element_type', 8))

        # 3. Aria label keywords
        aria_label = dom_info.get('aria_label', '')
        if aria_label:
            aria_words = re.findall(r'\b\w+\b', aria_label.lower())
            for word in aria_words:
                if len(word) > 3:  # Skip short words
                    search_terms.append((word, 'aria_label', 6))

        # 4. Element text keywords
        element_text = dom_info.get('element_text', '')
        if element_text:
            text_words = re.findall(r'\b\w+\b', element_text.lower())
            for word in text_words:
                if len(word) > 3:
                    search_terms.append((word, 'element_text', 4))

        # Search for each term
        for search_term, term_type, priority in search_terms:
            for i, line in enumerate(lines):
                line_lower = line.lower()

                # Look for CSS selectors and rules containing the search term
                if search_term.lower() in line_lower and ('{' in line or ':' in line or '.' in line or '#' in line):
                    snippet = self._extract_css_block(lines, i)
                    if snippet:
                        snippets.append({
                            'file': file_path,
                            'line_number': i + 1,
                            'code': snippet,
                            'matched_term': search_term,
                            'match_type': term_type,
                            'priority': priority
                        })

        # Sort by priority and remove duplicates
        seen_snippets = set()
        unique_snippets = []
        for snippet in sorted(snippets, key=lambda x: x['priority'], reverse=True):
            snippet_key = (snippet['file'], snippet['line_number'])
            if snippet_key not in seen_snippets:
                seen_snippets.add(snippet_key)
                unique_snippets.append(snippet)

        return unique_snippets

    def _search_css_content(self, content: str, keywords: List[str], file_path: str) -> List[Dict[str, str]]:
        """Search CSS content for relevant snippets based on keywords."""
        snippets = []
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Check if line contains any keywords
            for keyword in keywords:
                if keyword in line_lower and ('{' in line or ':' in line):
                    # Found a relevant CSS rule, extract the block
                    snippet = self._extract_css_block(lines, i)
                    if snippet:
                        snippets.append({
                            'file': file_path,
                            'line_number': i + 1,
                            'code': snippet,
                            'matched_keyword': keyword
                        })
                    break

        return snippets

    def _extract_css_block(self, lines: List[str], start_line: int) -> str:
        """Extract a complete CSS block starting from the given line."""
        # Find the start of the CSS rule by looking for the selector
        rule_start = start_line

        # Look backwards to find the selector line (contains { but not just inside a comment)
        while rule_start > 0:
            prev_line = lines[rule_start - 1].strip()
            # Check if this line contains a CSS selector (has { and looks like a selector)
            if ('{' in prev_line and
                not prev_line.startswith('//') and
                not prev_line.startswith('/*') and
                (prev_line.endswith('{') or '} {' in prev_line or prev_line.count('{') > prev_line.count('}'))):
                rule_start -= 1
                break
            rule_start -= 1

        # Extract the complete CSS rule with proper brace matching
        block_lines = []
        brace_count = 0
        found_opening_brace = False

        for i in range(rule_start, min(len(lines), start_line + 30)):
            line = lines[i]
            block_lines.append(line)

            # Count braces (ignore braces in comments and strings)
            line_content = line
            # Simple comment removal for brace counting
            if '//' in line_content:
                line_content = line_content.split('//')[0]

            open_braces = line_content.count('{')
            close_braces = line_content.count('}')

            brace_count += open_braces - close_braces

            if open_braces > 0:
                found_opening_brace = True

            # Stop when we've closed all braces and found at least one opening brace
            if found_opening_brace and brace_count <= 0:
                break

        return '\n'.join(block_lines) if block_lines else ""

def find_negative_trajectories(run_dir: str) -> List[str]:
    """Find all negative trajectory directories in the run."""
    graph_dir = os.path.join(run_dir, "graph")
    if not os.path.exists(graph_dir):
        logger.error(f"Graph directory not found: {graph_dir}")
        return []

    negative_traj_paths = []

    # Find all node directories
    node_dirs = glob.glob(os.path.join(graph_dir, "node_*"))

    for node_dir in node_dirs:
        tasks_dir = os.path.join(node_dir, "tasks")
        if not os.path.exists(tasks_dir):
            continue

        # Find all task directories
        task_dirs = glob.glob(os.path.join(tasks_dir, "task_*"))

        for task_dir in task_dirs:
            negative_trajs_dir = os.path.join(task_dir, "negative_trajs")
            if not os.path.exists(negative_trajs_dir):
                continue

            # Find all negative trajectory directories
            neg_traj_dirs = glob.glob(os.path.join(negative_trajs_dir, "*"))
            negative_traj_paths.extend([d for d in neg_traj_dirs if os.path.isdir(d)])

    return negative_traj_paths

def load_step_info(step_dir: str) -> Optional[Dict]:
    """Load step information from step directory."""
    step_info_path = os.path.join(step_dir, "step_info.json")
    if not os.path.exists(step_info_path):
        return None

    try:
        with open(step_info_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading step info from {step_info_path}: {e}")
        return None

def load_screenshot(step_dir: str) -> Optional[np.ndarray]:
    """Load screenshot from step directory."""
    screenshot_path = os.path.join(step_dir, "screenshot.png")
    if not os.path.exists(screenshot_path):
        return None

    try:
        return np.asarray(Image.open(screenshot_path))
    except Exception as e:
        logger.error(f"Error loading screenshot from {screenshot_path}: {e}")
        return None

def load_trajectory_info(traj_dir: str) -> Optional[Dict]:
    """Load trajectory information."""
    traj_info_path = os.path.join(traj_dir, "traj_info.json")
    if not os.path.exists(traj_info_path):
        return None

    try:
        with open(traj_info_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading trajectory info from {traj_info_path}: {e}")
        return None

def clear_existing_feedbacks(run_dir: str):
    """Clear all existing design_feedback.json files."""
    logger.info("Clearing existing design feedback files...")

    feedback_files = glob.glob(os.path.join(run_dir, "graph", "**", "design_feedback.json"), recursive=True)

    for feedback_file in feedback_files:
        try:
            os.remove(feedback_file)
            logger.debug(f"Removed {feedback_file}")
        except Exception as e:
            logger.error(f"Error removing {feedback_file}: {e}")

    logger.info(f"Cleared {len(feedback_files)} existing feedback files")

def save_design_feedback(step_dir: str, feedback: Dict):
    """Save design feedback to step directory."""
    feedback_path = os.path.join(step_dir, "design_feedback.json")

    try:
        with open(feedback_path, 'w') as f:
            json.dump(feedback, f, indent=2)
        logger.debug(f"Saved design feedback to {feedback_path}")
    except Exception as e:
        logger.error(f"Error saving design feedback to {feedback_path}: {e}")

def load_source_mapping(run_dir: str) -> Dict[str, List[str]]:
    """Load URL to source files mapping."""
    mapping_file = os.path.join(run_dir, "source_mapping.json")
    if os.path.exists(mapping_file):
        try:
            with open(mapping_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading source mapping from {mapping_file}: {e}")
    return {}

def get_source_files_for_step(step_info: Dict, source_mapping: Dict[str, List[str]]) -> List[str]:
    """Get source files for the current step based on URL."""
    if 'observation' in step_info and 'last_action' in step_info['observation']:
        last_action = step_info['observation']['last_action']
        if 'goto(' in last_action:
            # Extract URL from goto action
            import re
            match = re.search(r"goto\(['\"]([^'\"]+)['\"]", last_action)
            if match:
                url = match.group(1)

                # Try exact match first
                if url in source_mapping:
                    return source_mapping[url]

                # Try base URL match (remove query parameters and paths)
                from urllib.parse import urlparse
                parsed = urlparse(url)
                base_url = f"{parsed.scheme}://{parsed.netloc}/"
                if base_url in source_mapping:
                    return source_mapping[base_url]

                # Try domain match
                domain_url = f"{parsed.scheme}://{parsed.netloc}"
                if domain_url in source_mapping:
                    return source_mapping[domain_url]

                # Fallback: if it's a homepage request, try to find a similar page
                if parsed.path in ['', '/']:
                    # Look for featured/hot or all/hot pages as homepage alternatives
                    fallback_urls = [
                        f"{parsed.scheme}://{parsed.netloc}/featured/hot",
                        f"{parsed.scheme}://{parsed.netloc}/all/hot",
                        f"{parsed.scheme}://{parsed.netloc}/hot",
                        f"{parsed.scheme}://{parsed.netloc}/new"
                    ]
                    for fallback_url in fallback_urls:
                        if fallback_url in source_mapping:
                            logger.debug(f"Using fallback URL {fallback_url} for {url}")
                            return source_mapping[fallback_url]

    return []

def process_negative_trajectory(traj_dir: str, judge: DesignJudge, source_mapping: Dict[str, List[str]] = None) -> int:
    """Process a single negative trajectory and generate design feedback."""
    logger.info(f"Processing negative trajectory: {traj_dir}")

    # Load trajectory info
    traj_info = load_trajectory_info(traj_dir)
    if not traj_info:
        logger.error(f"Could not load trajectory info for {traj_dir}")
        return 0

    task_goal = traj_info.get('goal', 'Unknown goal')
    eval_thoughts = ""

    # Extract evaluation thoughts if available
    if 'misc' in traj_info and 'evaluation_info' in traj_info['misc']:
        eval_info = traj_info['misc']['evaluation_info']
        if 'output' in eval_info and 'thoughts' in eval_info['output']:
            eval_thoughts = eval_info['output']['thoughts']

    # Find all step directories
    step_dirs = glob.glob(os.path.join(traj_dir, "step_*"))
    step_dirs.sort(key=lambda x: int(x.split("_")[-1]))  # Sort by step number

    feedback_count = 0

    for i, step_dir in enumerate(step_dirs):
        step_info = load_step_info(step_dir)
        screenshot = load_screenshot(step_dir)

        if not step_info or screenshot is None:
            logger.warning(f"Skipping step {step_dir} - missing step info or screenshot")
            continue

        # Get next step info if available
        next_step_info = None
        if i + 1 < len(step_dirs):
            next_step_info = load_step_info(step_dirs[i + 1])

        # Extract axtree from step_info
        axtree = ""
        if 'observation' in step_info and 'axtree_txt' in step_info['observation']:
            axtree = step_info['observation']['axtree_txt']

        # Get source files for this step
        source_files = []
        if source_mapping:
            source_files = get_source_files_for_step(step_info, source_mapping)

        # Analyze this step
        feedback = judge.analyze_step(
            current_step=step_info,
            next_step=next_step_info,
            screenshot=screenshot,
            task_goal=task_goal,
            eval_thoughts=eval_thoughts,
            axtree=axtree,
            source_files=source_files
        )

        if feedback:
            # Add source files to feedback
            if source_files:
                feedback["source_files"] = source_files

            save_design_feedback(step_dir, feedback)
            feedback_count += 1
            logger.info(f"Generated design feedback for step {os.path.basename(step_dir)}")

    return feedback_count

def main():
    parser = argparse.ArgumentParser(description="Generate design feedback for Go-Browse negative trajectories")
    parser.add_argument("--model_name", type=str, required=True,
                       help="Model name for the design judge (e.g., Qwen/Qwen2.5-VL-72B-Instruct)")
    parser.add_argument("--run_dir", type=str, required=True,
                       help="Path to the Go-Browse run directory")
    parser.add_argument("--base_url", type=str, default="http://localhost:8000",
                       help="Base URL for the Qwen API endpoint")
    parser.add_argument("--api_key", type=str, default=None,
                       help="API key for the Qwen endpoint (if required)")
    parser.add_argument("--use_source_mapping", action="store_true",
                       help="Use source file mapping if available")
    parser.add_argument("--container", type=str, default=None,
                       help="Docker container name for code snippet analysis")
    parser.add_argument("--codebase_index", type=str, default=None,
                       help="Path to the codebase index JSON file")

    args = parser.parse_args()

    # Validate run directory
    if not os.path.exists(args.run_dir):
        logger.error(f"Run directory does not exist: {args.run_dir}")
        return

    graph_dir = os.path.join(args.run_dir, "graph")
    if not os.path.exists(graph_dir):
        logger.error(f"Graph directory not found in run: {graph_dir}")
        return

    # Clear existing feedback files
    clear_existing_feedbacks(args.run_dir)

    # Load source mapping if requested
    source_mapping = {}
    if args.use_source_mapping:
        source_mapping = load_source_mapping(args.run_dir)
        logger.info(f"Loaded source mapping for {len(source_mapping)} URLs")

    # Initialize design judge
    judge = DesignJudge(args.model_name, args.base_url, args.api_key, args.container, args.codebase_index)

    # Find all negative trajectories
    negative_traj_paths = find_negative_trajectories(args.run_dir)
    logger.info(f"Found {len(negative_traj_paths)} negative trajectories to process")

    if not negative_traj_paths:
        logger.info("No negative trajectories found. Nothing to process.")
        return

    # Process each negative trajectory
    total_feedback_count = 0
    processed_trajs = 0

    for traj_path in negative_traj_paths:
        try:
            feedback_count = process_negative_trajectory(traj_path, judge, source_mapping)
            total_feedback_count += feedback_count
            processed_trajs += 1
            logger.info(f"Processed trajectory {processed_trajs}/{len(negative_traj_paths)}: "
                       f"{feedback_count} feedbacks generated")
        except Exception as e:
            logger.error(f"Error processing trajectory {traj_path}: {e}")

    logger.info(f"Design judge analysis complete!")
    logger.info(f"Processed {processed_trajs} negative trajectories")
    logger.info(f"Generated {total_feedback_count} design feedback files")

if __name__ == "__main__":
    main()
