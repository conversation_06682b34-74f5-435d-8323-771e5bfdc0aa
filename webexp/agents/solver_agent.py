from .base_agent import AgentFactory, BaseAgent
from .prompt_builders.solver_prompt_builder import SolverPromptBuilder
from .trajectory_data import BrowserGymAgentStepData
from browsergym.core.action.highlevel import HighLevelActionSet
from browsergym.utils.obs import flatten_axtree_to_str, flatten_dom_to_str, prune_html
from openai import OpenAI
from tenacity import retry, before_sleep_log, stop_after_attempt, wait_exponential, wait_random
import ast
import logging
import os
import re
import requests
import time

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class QwenClient:
    """Client for Qwen completion endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key

    def chat_completions_create(self, model: str, messages: list[dict], temperature: float = 0.0, max_tokens: int = 1000):
        """Create a chat completion using <PERSON>wen endpoint with vLLM format."""
        # Use vLLM official format
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            # "top_p": 0.95,
            # "top_k": 20,
            "max_tokens": max_tokens
        }

        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        # Use chat/completions endpoint
        response = requests.post(
            f"{self.base_url}/v1/chat/completions",
            json=payload,
            headers=headers,
            timeout=60
        )
        response.raise_for_status()
        qwen_response = response.json()

        return QwenResponse(qwen_response)

    def _messages_to_prompt(self, messages: list[dict]) -> str:
        """Convert OpenAI messages format to a single prompt string."""
        prompt_parts = []
        for message in messages:
            role = message["role"]
            content = message["content"]
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")

        prompt_parts.append("Assistant:")  # Prompt for response
        return "\n\n".join(prompt_parts)

class QwenResponse:
    """Wrapper to make Qwen response compatible with OpenAI response format."""

    def __init__(self, qwen_response: dict):
        self.qwen_response = qwen_response
        self.choices = [QwenChoice(qwen_response)]
        self.usage = QwenUsage(qwen_response)

class QwenChoice:
    def __init__(self, qwen_response: dict):
        self.message = QwenMessage(qwen_response)

class QwenMessage:
    def __init__(self, qwen_response: dict):
        # Extract text from Qwen response - handle multiple possible formats
        self.content = ""

        # Format 1: Standard choices format
        if "choices" in qwen_response and len(qwen_response["choices"]) > 0:
            choice = qwen_response["choices"][0]
            if isinstance(choice, dict):
                # Try different possible fields in choice
                if "text" in choice:
                    self.content = choice["text"]
                elif "message" in choice and isinstance(choice["message"], dict):
                    self.content = choice["message"].get("content", "")
                else:
                    self.content = str(choice)

        # Format 2: Direct text field
        elif "text" in qwen_response:
            self.content = qwen_response["text"]

        # Format 3: Direct content field
        elif "content" in qwen_response:
            self.content = qwen_response["content"]

        # Format 4: Response field (some Qwen implementations)
        elif "response" in qwen_response:
            self.content = qwen_response["response"]

        # Ensure content is a string
        if not isinstance(self.content, str):
            self.content = str(self.content) if self.content is not None else ""

class QwenUsage:
    def __init__(self, qwen_response: dict):
        usage_data = qwen_response.get("usage", {})
        self.prompt_tokens = usage_data.get("prompt_tokens", 0)
        self.completion_tokens = usage_data.get("completion_tokens", 0)
        self.total_tokens = usage_data.get("total_tokens", 0)

    def to_dict(self):
        return {
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens
        }


def messages_to_string(messages: list[dict]) -> str:
    prompt_text_strings = []
    for message in messages:
        prompt_text_strings.append(message["content"])
    full_prompt_txt = "\n".join(prompt_text_strings)
    return full_prompt_txt
        

def extract_action_and_thought(raw_string):
    """Extract thought and action from JSON string.

    Args:
        raw_string (str): Raw string containing thought and action

    Returns:
        tuple: (action, thought) or (None, None) if extraction fails
    """
    # Initialize defaults
    thought = None
    action = None

    try:
        # Find thought and action patterns
        thought_match = re.search(r'"thought"\s*:\s*"(.*?)"', raw_string, re.DOTALL)
        action_match = re.search(r'"action"\s*:\s*"(.*?)"', raw_string, re.DOTALL)

        if thought_match:
            thought = thought_match.group(1)
            # Clean up escaped quotes
            thought = thought.replace('\\"', '"')

        if action_match:
            action = action_match.group(1)
            # Clean up escaped quotes
            action = action.replace('\\"', '"')

    except Exception as e:
        logger.error(f"Error parsing string: {e}")
        logger.error(f"Raw string: {raw_string[:500]}...")  # Log first 500 chars
        return None, None

    return action, thought


@AgentFactory.register
class SolverAgent(BaseAgent):
    """
    Agent used to fulfill/solve user requests.
    """

    def __init__(
            self,
            model_id: str,
            model_id_2: str | None = None,
            base_url: str | None = None,
            base_url_2: str | None = None,
            api_key: str | None = None,
            temperature: float = 1.0,
            char_limit: int = -1,
            demo_mode: str = 'off',
    ):
        """
        Initialize the agent.

        Args:
            model_name (str): The name of the model to use.
            temperature (float): The temperature to use for sampling.
            demo_mode (bool): Whether to run in demo mode.
        """
        
        # These are args that will be specified in the config.
        super().__init__(model_id=model_id, temperature=temperature, char_limit=char_limit, demo_mode=demo_mode)
        
        self.model_id = model_id
        self.model_id_2 = model_id_2 or model_id
        self.temperature = temperature
        self.char_limit = char_limit
        self.demo_mode = demo_mode
        

        # Determine if we're using Qwen or OpenAI based on model_id
        is_qwen = "Qwen" in model_id or "qwen" in model_id.lower()

        if is_qwen:
            # Use Qwen client for Qwen models
            base_url = base_url or os.getenv("QWEN_BASE_URL", "http://localhost:8000")
            base_url_2 = base_url_2 or os.getenv("QWEN_BASE_URL", "http://localhost:8000")
            api_key = api_key or os.getenv("QWEN_API_KEY")
            self.client = QwenClient(base_url=base_url, api_key=api_key)
            self.client_long = QwenClient(base_url=base_url_2, api_key=api_key)
        else:
            # Use OpenAI client for other models
            base_url = base_url or os.getenv("OPENAI_BASE_URL")
            base_url_2 = base_url_2 or os.getenv("OPENAI_BASE_URL")
            api_key = api_key or os.getenv("OPENAI_API_KEY", "Unspecified!")
            self.client = OpenAI(base_url=base_url, api_key=api_key)
            self.client_long = OpenAI(base_url=base_url_2, api_key=api_key)

        self.action_set = HighLevelActionSet(
            subsets=["chat", "bid", "infeas", "nav"],
            strict=False,
            multiaction=False,
            demo_mode=demo_mode
        )

        self.prompt_builder = SolverPromptBuilder(self.action_set)

        self.history: list[BrowserGymAgentStepData] = []

    def reset(self):
        self.history.clear()

    def obs_preprocessor(self, obs: dict) -> dict:

        # Try to get pruned HTML, fallback to empty string if parsing fails
        try:
            pruned_html = prune_html(flatten_dom_to_str(obs["dom_object"]))
        except Exception as e:
            logger.warning(f"Failed to parse HTML DOM: {e}. Using empty string for pruned_html.")
            pruned_html = ""

        return {
            "chat_messages": obs["chat_messages"],
            "screenshot": obs["screenshot"],
            "goal_object": obs["goal_object"],
            "last_action": obs["last_action"],
            "last_action_error": obs["last_action_error"],
            "open_pages_urls": obs["open_pages_urls"],
            "open_pages_titles": obs["open_pages_titles"],
            "active_page_index": obs["active_page_index"],
            "axtree_txt": flatten_axtree_to_str(obs["axtree_object"], filter_visible_only=False, extra_properties=obs["extra_element_properties"]),
            "axtree_visible_only_txt": flatten_axtree_to_str(obs["axtree_object"], filter_visible_only=True, extra_properties=obs["extra_element_properties"]),
            "pruned_html": pruned_html,
            "extra_element_properties": obs["extra_element_properties"],
        }
    
    def action_processor(self, action: str) -> str:
        """
        Process the action before it is passed to the environment.

        Args:
            action (str): The action to process.

        Returns:
            str: The processed action.
        """
        parsed_action, thought = extract_action_and_thought(action)
        return self.action_set.to_python_code(parsed_action if parsed_action else action)

    
    def get_action(self, obs: dict, oracle_action:tuple[str, str] = None, **kwargs) -> tuple[str, dict]:
        """
        Get the action for the given observation.

        Args:
            obs (dict): The observation from the environment.
            oracle_action tuple[str, str]: Tuple of (action, thought) to use if available instead of generating a new one.

        Returns:
            str: The action to take.
        """

        current_step = BrowserGymAgentStepData(
            action=None,
            thought=None,
            axtree=obs["axtree_txt"],
            last_action_error=obs.get("last_action_error"),
            misc={}
        )

        if oracle_action is None:
            # Use adaptive retry mechanism with character limit reduction
            response = self.make_llm_call_with_adaptive_retry(obs, current_step)
            
            raw_action = response.choices[0].message.content
            action, thought = extract_action_and_thought(raw_action)
            current_step.misc["model_usage"] = response.usage.to_dict()
        
        else:
            action, thought = oracle_action
            raw_action = f'{{"thought": "{thought}", "action": "{action}"}}'
            
        print(f"Raw Action:\n {raw_action}")

        current_step.action = action
        current_step.thought = thought
        current_step.misc.update({"thought": thought, "parsed_action": action})
        
        self.history.append(current_step)

        return raw_action, current_step.misc
        
    def make_llm_call_with_adaptive_retry(self, obs: dict, current_step: BrowserGymAgentStepData) -> dict:
        """
        Make a call to the LLM with adaptive retry that reduces character limit on failures.
        
        Args:
            obs (dict): The observation from the environment.
            current_step (BrowserGymAgentStepData): The current step data.
            
        Returns:
            dict: The response from the LLM.
        """
        max_attempts = 5
        attempt = 0
        current_char_limit = self.char_limit
        
        while attempt < max_attempts:
            try:
                goal_text = obs["goal_object"][0]["text"]

                # Build messages with current character limit
                messages = self.prompt_builder.build_messages(
                    goal=goal_text,
                    current_step=current_step,
                    history=self.history,
                    char_limit=current_char_limit if (attempt == 0) or (current_char_limit < 0) else current_char_limit * 2 # TODO: Ad-hoc!
                )['prompt']
                
                if attempt == 0:
                    # Make the actual API call
                    if isinstance(self.client, QwenClient):
                        return self.client.chat_completions_create(
                            model=self.model_id,
                            messages=messages,
                            temperature=self.temperature
                        )
                    else:
                        return self.client.chat.completions.create(
                            model=self.model_id,
                            messages=messages,
                            temperature=self.temperature
                        )
                else:
                    if isinstance(self.client_long, QwenClient):
                        return self.client_long.chat_completions_create(
                            model=self.model_id_2,
                            messages=messages,
                            temperature=self.temperature
                        )
                    else:
                        return self.client_long.chat.completions.create(
                            model=self.model_id_2,
                            messages=messages,
                            temperature=self.temperature
                        )
                
            except Exception as e:
                attempt += 1
                if attempt >= max_attempts:
                    logger.error(f"Failed after {max_attempts} attempts: {str(e)}")
                    raise
                    
                if attempt > 1:
                    current_char_limit = int(current_char_limit * 0.95)
                logger.warning(f"Retrying with {current_char_limit} character limit after error: {str(e)}")
                
                if attempt > 1:  # Skip delay for first retry
                    wait_time = 1.5 * (2 ** (attempt-1)) + (0.1 * attempt)
                    logger.info(f"Waiting {wait_time:.2f} seconds before retry")
                    time.sleep(wait_time)
                else:
                    logger.info("Retrying immediately")
