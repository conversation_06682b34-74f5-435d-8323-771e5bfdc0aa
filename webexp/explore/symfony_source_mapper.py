#!/usr/bin/env python3
"""
Symfony Source File Mapper for Go-Browse

This tool maps URLs visited during Go-Browse exploration to their corresponding
source files in Symfony applications through real analysis of routing configuration.

Designed to be robust and based on actual application structure analysis.
"""

import os
import json
import logging
import argparse
import subprocess
import re
import yaml
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SymfonySourceMapper:
    """Maps URLs from Go-Browse exploration to Symfony application source files."""
    
    def __init__(self, container_name: str):
        self.container = container_name
        self.app_root = "/var/www/html"
        self.routes_cache = {}
        self.controllers_cache = {}
        
    def map_exploration_graph_to_sources(self, run_dir: str) -> Dict[str, List[str]]:
        """
        Main method to map URLs from exploration graph to source files.
        
        Args:
            run_dir: Path to the Go-Browse run directory
            
        Returns:
            Dictionary mapping URLs to lists of source file paths
        """
        logger.info(f"Starting Symfony source file mapping for run: {run_dir}")
        
        # Step 1: Analyze Symfony application structure
        self._analyze_symfony_structure()
        
        # Step 2: Extract all URLs from exploration graph
        urls = self._extract_urls_from_exploration(run_dir)
        logger.info(f"Found {len(urls)} unique URLs in exploration graph")
        
        # Step 3: Map each URL to source files using Symfony routing
        url_to_files = {}
        for i, url in enumerate(urls, 1):
            logger.info(f"Analyzing URL {i}/{len(urls)}: {url}")
            source_files = self._map_url_to_symfony_sources(url)
            
            if source_files:
                url_to_files[url] = source_files
                logger.info(f"  -> Found {len(source_files)} source files: {source_files}")
            else:
                logger.warning(f"  -> No source files found for {url}")
        
        # Step 4: Save mapping to file
        self._save_mapping(run_dir, url_to_files)
        
        logger.info(f"Source mapping completed. Mapped {len(url_to_files)} URLs to source files")
        return url_to_files
    
    def _analyze_symfony_structure(self):
        """Analyze the Symfony application structure and cache routing information."""
        logger.info("Analyzing Symfony application structure...")
        
        # Load all route configurations
        self._load_route_configurations()
        
        # Cache controller information
        self._cache_controller_info()
        
        logger.info(f"Loaded {len(self.routes_cache)} route configurations")
    
    def _load_route_configurations(self):
        """Load all Symfony route configurations from config/app_routes/."""
        routes_dir = f"{self.app_root}/config/app_routes"
        
        try:
            # List all YAML files in routes directory
            cmd = f"docker exec {self.container} find {routes_dir} -name '*.yaml' -o -name '*.yml'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                route_files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
                
                for route_file in route_files:
                    self._parse_route_file(route_file)
            else:
                logger.warning(f"Could not list route files in {routes_dir}")
                
        except Exception as e:
            logger.error(f"Error loading route configurations: {e}")
    
    def _parse_route_file(self, route_file: str):
        """Parse a single route YAML file and extract route information."""
        try:
            # Read the route file content
            cmd = f"docker exec {self.container} cat {route_file}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                content = result.stdout
                
                # Parse YAML content
                try:
                    routes_data = yaml.safe_load(content)
                    if routes_data:
                        for route_name, route_config in routes_data.items():
                            if isinstance(route_config, dict):
                                self.routes_cache[route_name] = {
                                    'path': route_config.get('path', ''),
                                    'controller': route_config.get('controller', ''),
                                    'methods': route_config.get('methods', ['GET']),
                                    'defaults': route_config.get('defaults', {}),
                                    'requirements': route_config.get('requirements', {}),
                                    'file': route_file
                                }
                except yaml.YAMLError as e:
                    logger.warning(f"Could not parse YAML in {route_file}: {e}")
            else:
                logger.warning(f"Could not read route file {route_file}")
                
        except Exception as e:
            logger.error(f"Error parsing route file {route_file}: {e}")
    
    def _cache_controller_info(self):
        """Cache information about available controllers."""
        try:
            controllers_dir = f"{self.app_root}/src/Controller"
            cmd = f"docker exec {self.container} find {controllers_dir} -name '*.php'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                controller_files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
                
                for controller_file in controller_files:
                    # Extract controller class name from file path
                    class_name = os.path.basename(controller_file).replace('.php', '')
                    namespace = f"App\\Controller\\{class_name}"
                    
                    self.controllers_cache[namespace] = controller_file
                    
        except Exception as e:
            logger.error(f"Error caching controller info: {e}")
    
    def _extract_urls_from_exploration(self, run_dir: str) -> Set[str]:
        """Extract all unique URLs from the exploration graph."""
        urls = set()
        
        # Find all step_info.json files
        step_files = []
        for root, dirs, files in os.walk(run_dir):
            for file in files:
                if file == 'step_info.json':
                    step_files.append(os.path.join(root, file))
        
        logger.info(f"Found {len(step_files)} step_info.json files")
        
        for step_file in step_files:
            try:
                with open(step_file, 'r') as f:
                    step_data = json.load(f)
                
                # Extract URL from observation.last_action
                if 'observation' in step_data and 'last_action' in step_data['observation']:
                    last_action = step_data['observation']['last_action']
                    url = self._extract_url_from_action(last_action)
                    if url and self._is_localhost_url(url):
                        urls.add(url)
                        
            except Exception as e:
                logger.error(f"Error processing {step_file}: {e}")
                continue
        
        return urls
    
    def _extract_url_from_action(self, action: str) -> Optional[str]:
        """Extract URL from action string like goto('http://localhost:9999/path')."""
        if not action or 'goto(' not in action:
            return None
            
        # Extract URL from goto('url') pattern
        match = re.search(r"goto\(['\"]([^'\"]+)['\"]", action)
        if match:
            return match.group(1)
        return None
    
    def _is_localhost_url(self, url: str) -> bool:
        """Check if URL is a localhost URL that we should analyze."""
        if not url:
            return False
        
        parsed = urlparse(url)
        hostname = parsed.hostname
        
        # Only process localhost URLs
        return hostname in ['localhost', '127.0.0.1'] or (hostname and hostname.startswith('192.168.'))
    
    def _map_url_to_symfony_sources(self, url: str) -> List[str]:
        """Map a URL to its corresponding Symfony source files."""
        source_files = []

        parsed_url = urlparse(url)
        path = parsed_url.path

        # Find matching route
        matching_route = self._find_matching_route(path)

        if matching_route:
            route_name, route_config = matching_route
            logger.debug(f"Found matching route '{route_name}' for path '{path}'")

            # Get controller file and specific method
            controller = route_config.get('controller', '')
            if controller:
                controller_file = self._get_controller_file(controller)
                if controller_file:
                    source_files.append(controller_file)

                    # Add specific method information for better granularity
                    if '::' in controller:
                        method_name = controller.split('::')[1]
                        # Try to find method-specific template or related files
                        method_specific_files = self._get_method_specific_files(controller_file, method_name, route_name)
                        source_files.extend(method_specific_files)

            # Get template files
            template_files = self._get_template_files(route_name, controller)
            source_files.extend(template_files)

            # Get frontend files (CSS, JS, static assets)
            frontend_files = self._get_frontend_files(route_name, controller, path)
            source_files.extend(frontend_files)

            # Add route configuration file as source (only if no frontend files found)
            if not frontend_files:
                route_file = route_config.get('file', '')
                if route_file and route_file not in source_files:
                    source_files.append(route_file)
        else:
            logger.debug(f"No matching route found for path '{path}'")

        return source_files
    
    def _find_matching_route(self, path: str) -> Optional[Tuple[str, Dict]]:
        """Find the route that matches the given path."""
        for route_name, route_config in self.routes_cache.items():
            route_path = route_config.get('path', '')
            
            if self._path_matches_route(path, route_path, route_config):
                return (route_name, route_config)
        
        return None
    
    def _path_matches_route(self, path: str, route_path: str, route_config: Dict) -> bool:
        """Check if a path matches a Symfony route pattern."""
        if not route_path:
            return False
        
        # Handle exact matches
        if path == route_path:
            return True
        
        # Handle parameterized routes like /{sortBy}
        # Convert Symfony route pattern to regex
        pattern = route_path
        
        # Replace {param} with regex patterns
        pattern = re.sub(r'\{([^}]+)\}', r'([^/]+)', pattern)
        
        # Escape special regex characters except our replacements
        pattern = pattern.replace('/', r'\/')
        
        # Add anchors
        pattern = f'^{pattern}$'
        
        try:
            return bool(re.match(pattern, path))
        except re.error:
            return False
    
    def _get_controller_file(self, controller: str) -> Optional[str]:
        """Get the file path for a controller."""
        # Controller format: App\Controller\FrontController::front
        if '::' in controller:
            class_name = controller.split('::')[0]
            return self.controllers_cache.get(class_name)
        
        return None
    
    def _get_method_specific_files(self, controller_file: str, method_name: str, route_name: str) -> List[str]:
        """Get files specific to a controller method."""
        method_files = []

        try:
            # Look for method-specific configuration or related files
            controller_dir = os.path.dirname(controller_file)
            controller_name = os.path.basename(controller_file).replace('.php', '').replace('Controller', '').lower()

            # Check for method-specific service configurations
            service_patterns = [
                f"{self.app_root}/config/services/{controller_name}_{method_name}.yaml",
                f"{self.app_root}/config/services/{route_name}.yaml"
            ]

            for service_path in service_patterns:
                if self._file_exists_in_container(service_path):
                    method_files.append(service_path)

        except Exception as e:
            logger.debug(f"Error getting method-specific files: {e}")

        return method_files

    def _get_template_files(self, route_name: str, controller: str) -> List[str]:
        """Get template files associated with a route."""
        template_files = []

        try:
            # Infer template path from controller and action
            if '::' in controller:
                class_name, action = controller.split('::')

                # Extract controller name (remove namespace and Controller suffix)
                controller_name = class_name.split('\\')[-1].replace('Controller', '').lower()

                # Common template patterns
                template_patterns = [
                    f"{self.app_root}/templates/{controller_name}/{action}.html.twig",
                    f"{self.app_root}/templates/{controller_name}/{route_name}.html.twig",
                    f"{self.app_root}/templates/{route_name}.html.twig"
                ]

                for template_path in template_patterns:
                    if self._file_exists_in_container(template_path):
                        template_files.append(template_path)
                        break  # Use first matching template

        except Exception as e:
            logger.debug(f"Error getting template files: {e}")

        return template_files

    def _get_frontend_files(self, route_name: str, controller: str, path: str) -> List[str]:
        """Get frontend files (CSS, JS, assets) associated with a route."""
        frontend_files = []

        try:
            # Common frontend file locations in Symfony
            frontend_dirs = [
                f"{self.app_root}/public",
                f"{self.app_root}/assets",
                f"{self.app_root}/web",
                f"{self.app_root}/public/assets",
                f"{self.app_root}/public/css",
                f"{self.app_root}/public/js"
            ]

            # Extract controller and action names for file matching
            controller_name = ""
            action_name = ""

            if '::' in controller:
                class_name, action_name = controller.split('::')
                controller_name = class_name.split('\\')[-1].replace('Controller', '').lower()

            # Look for CSS files
            css_patterns = [
                f"css/{controller_name}.css",
                f"css/{action_name}.css",
                f"css/{route_name}.css",
                f"css/app.css",
                f"css/main.css",
                f"css/style.css",
                f"stylesheets/{controller_name}.css",
                f"stylesheets/{action_name}.css"
            ]

            # Look for JS files
            js_patterns = [
                f"js/{controller_name}.js",
                f"js/{action_name}.js",
                f"js/{route_name}.js",
                f"js/app.js",
                f"js/main.js",
                f"javascript/{controller_name}.js",
                f"javascript/{action_name}.js"
            ]

            # Search for files in frontend directories
            for frontend_dir in frontend_dirs:
                if not self._dir_exists_in_container(frontend_dir):
                    continue

                # Check CSS files
                for css_pattern in css_patterns:
                    css_path = f"{frontend_dir}/{css_pattern}"
                    if self._file_exists_in_container(css_path):
                        frontend_files.append(css_path)

                # Check JS files
                for js_pattern in js_patterns:
                    js_path = f"{frontend_dir}/{js_pattern}"
                    if self._file_exists_in_container(js_path):
                        frontend_files.append(js_path)

            # Look for Webpack/Asset files
            webpack_files = self._get_webpack_assets(controller_name, action_name, route_name)
            frontend_files.extend(webpack_files)

            # Look for source CSS/LESS files (editable, won't be overwritten)
            source_css_files = self._get_source_css_files(controller_name, action_name, route_name)
            frontend_files.extend(source_css_files)

            # Note: Skipping compiled CSS files as they are dynamically generated and would be overwritten

            # Remove duplicates while preserving order
            seen = set()
            unique_files = []
            for file in frontend_files:
                if file not in seen:
                    seen.add(file)
                    unique_files.append(file)

            return unique_files

        except Exception as e:
            logger.debug(f"Error getting frontend files: {e}")
            return []

    def _get_webpack_assets(self, controller_name: str, action_name: str, route_name: str) -> List[str]:
        """Get Webpack/Encore compiled assets."""
        webpack_files = []

        try:
            # Check for Symfony Webpack Encore manifest
            manifest_paths = [
                f"{self.app_root}/public/build/manifest.json",
                f"{self.app_root}/web/build/manifest.json"
            ]

            for manifest_path in manifest_paths:
                if self._file_exists_in_container(manifest_path):
                    # Read manifest to find compiled assets
                    cmd = f"docker exec {self.container} cat {manifest_path}"
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        try:
                            import json
                            manifest = json.loads(result.stdout)

                            # Look for relevant assets
                            asset_patterns = [
                                f"{controller_name}.css",
                                f"{action_name}.css",
                                f"{route_name}.css",
                                f"{controller_name}.js",
                                f"{action_name}.js",
                                f"{route_name}.js",
                                "app.css",
                                "app.js"
                            ]

                            for pattern in asset_patterns:
                                if pattern in manifest:
                                    asset_path = f"{os.path.dirname(manifest_path)}/{manifest[pattern]}"
                                    webpack_files.append(asset_path)

                        except json.JSONDecodeError:
                            logger.debug(f"Could not parse manifest: {manifest_path}")

        except Exception as e:
            logger.debug(f"Error getting webpack assets: {e}")

        return webpack_files

    def _get_theme_css_files(self) -> List[str]:
        """Get main CSS theme files that control visual layout."""
        theme_files = []

        try:
            # Look for main CSS files in common locations
            css_locations = [
                f"{self.app_root}/public/build",
                f"{self.app_root}/public/css",
                f"{self.app_root}/web/css",
                f"{self.app_root}/assets/css"
            ]

            for css_dir in css_locations:
                if not self._dir_exists_in_container(css_dir):
                    continue

                # Find main theme/layout CSS files
                cmd = f"docker exec {self.container} find {css_dir} -name '*.css' | grep -E '(theme|main|app|style|layout|postmill)' | head -5"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    css_files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
                    theme_files.extend(css_files)

            # Remove duplicates
            return list(set(theme_files))

        except Exception as e:
            logger.debug(f"Error getting theme CSS files: {e}")
            return []

    def _get_source_css_files(self, controller_name: str, action_name: str, route_name: str) -> List[str]:
        """Get source CSS/LESS files that can be safely edited without being overwritten."""
        source_files = []

        try:
            # Look for source CSS/LESS files in assets directory
            source_dirs = [
                f"{self.app_root}/assets/css",
                f"{self.app_root}/assets/scss",
                f"{self.app_root}/assets/less",
                f"{self.app_root}/src/Resources/assets/css",
                f"{self.app_root}/src/Resources/assets/scss"
            ]

            for source_dir in source_dirs:
                if not self._dir_exists_in_container(source_dir):
                    continue

                # Find relevant source files
                cmd = f"docker exec {self.container} find {source_dir} -name '*.css' -o -name '*.scss' -o -name '*.less' | head -20"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]

                    # Prioritize files that might be related to the current route/controller
                    priority_files = []
                    other_files = []

                    for file in files:
                        filename = os.path.basename(file).lower()
                        if any(keyword in filename for keyword in [controller_name, action_name, route_name, 'main', 'app', 'theme']):
                            priority_files.append(file)
                        else:
                            other_files.append(file)

                    # Add priority files first, then others (limited)
                    source_files.extend(priority_files)
                    source_files.extend(other_files[:5])  # Limit to avoid too many files

            # Remove duplicates
            return list(set(source_files))

        except Exception as e:
            logger.debug(f"Error getting source CSS files: {e}")
            return []

    def _dir_exists_in_container(self, dir_path: str) -> bool:
        """Check if directory exists in container."""
        try:
            cmd = f"docker exec {self.container} test -d {dir_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=3)
            return result.returncode == 0
        except Exception:
            return False

    def _file_exists_in_container(self, file_path: str) -> bool:
        """Check if file exists in container."""
        try:
            cmd = f"docker exec {self.container} test -f {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=3)
            return result.returncode == 0
        except Exception:
            return False
    
    def _save_mapping(self, run_dir: str, url_to_files: Dict[str, List[str]]):
        """Save URL to source files mapping to JSON file."""
        mapping_file = os.path.join(run_dir, "source_mapping.json")
        
        try:
            with open(mapping_file, 'w') as f:
                json.dump(url_to_files, f, indent=2)
            logger.info(f"Saved source mapping to {mapping_file}")
        except Exception as e:
            logger.error(f"Error saving mapping to {mapping_file}: {e}")


def main():
    parser = argparse.ArgumentParser(description="Map Go-Browse exploration URLs to Symfony source files")
    parser.add_argument("--run_dir", type=str, required=True,
                       help="Path to the Go-Browse run directory")
    parser.add_argument("--container", type=str, required=True,
                       help="Docker container name")
    
    args = parser.parse_args()
    
    # Validate run directory
    if not os.path.exists(args.run_dir):
        logger.error(f"Run directory does not exist: {args.run_dir}")
        return 1
    
    # Check if Docker container is running
    check_cmd = f"docker ps --filter name={args.container} --format '{{{{.Names}}}}'"
    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
    
    if args.container not in result.stdout:
        logger.error(f"Docker container '{args.container}' is not running")
        return 1
    
    # Create source mapper and run mapping
    mapper = SymfonySourceMapper(args.container)
    
    try:
        url_to_files = mapper.map_exploration_graph_to_sources(args.run_dir)
        
        logger.info("Source mapping completed successfully!")
        logger.info(f"Mapped {len(url_to_files)} URLs to source files")
        
        # Print summary
        total_files = sum(len(files) for files in url_to_files.values())
        logger.info(f"Total source files identified: {total_files}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during source mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit(main())
