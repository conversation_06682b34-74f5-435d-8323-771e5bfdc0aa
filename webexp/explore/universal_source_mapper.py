#!/usr/bin/env python3
"""
Universal Source File Mapper for Go-Browse

This tool automatically detects web application frameworks and maps URLs 
to source files using framework-specific analysis methods.

Supports:
- Symfony (PHP)
- Django (Python) 
- Rails (Ruby)
- Express.js (Node.js)
- <PERSON><PERSON> (PHP)
- Generic PHP applications
"""

import os
import json
import logging
import argparse
import subprocess
import re
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UniversalSourceMapper:
    """Universal source mapper that detects framework and uses appropriate mapping strategy."""
    
    def __init__(self, container_name: str):
        self.container = container_name
        self.framework = None
        self.app_root = None
        self.mapper = None
        
    def map_exploration_graph_to_sources(self, run_dir: str) -> Dict[str, List[str]]:
        """Main method to map URLs from exploration graph to source files."""
        logger.info(f"Starting universal source file mapping for run: {run_dir}")
        
        # Step 1: Detect web application framework
        self._detect_framework()
        
        # Step 2: Initialize framework-specific mapper
        self._initialize_mapper()
        
        # Step 3: Perform mapping
        if self.mapper:
            return self.mapper.map_exploration_graph_to_sources(run_dir)
        else:
            logger.error("No suitable mapper found for detected framework")
            return {}
    
    def _detect_framework(self):
        """Detect the web application framework running in the container."""
        logger.info("Detecting web application framework...")
        
        # Check for common framework indicators
        framework_indicators = [
            # Symfony
            ('/var/www/html/composer.json', 'symfony', '/var/www/html'),
            ('/app/composer.json', 'symfony', '/app'),
            
            # Django
            ('/app/manage.py', 'django', '/app'),
            ('/var/www/html/manage.py', 'django', '/var/www/html'),
            
            # Rails
            ('/app/Gemfile', 'rails', '/app'),
            ('/var/www/html/Gemfile', 'rails', '/var/www/html'),
            
            # Laravel
            ('/var/www/html/artisan', 'laravel', '/var/www/html'),
            ('/app/artisan', 'laravel', '/app'),
            
            # Node.js/Express
            ('/app/package.json', 'nodejs', '/app'),
            ('/var/www/html/package.json', 'nodejs', '/var/www/html'),
            
            # Generic PHP
            ('/var/www/html/index.php', 'php', '/var/www/html'),
            ('/app/index.php', 'php', '/app'),
        ]
        
        for file_path, framework, app_root in framework_indicators:
            if self._file_exists_in_container(file_path):
                # Additional validation for framework detection
                if self._validate_framework(file_path, framework):
                    self.framework = framework
                    self.app_root = app_root
                    logger.info(f"Detected framework: {framework} at {app_root}")
                    return
        
        logger.warning("Could not detect web application framework")
        self.framework = 'unknown'
        self.app_root = '/var/www/html'  # Default
    
    def _validate_framework(self, file_path: str, framework: str) -> bool:
        """Validate framework detection by checking file contents."""
        try:
            if framework == 'symfony':
                # Check composer.json for Symfony dependencies
                cmd = f"docker exec {self.container} cat {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    content = result.stdout.lower()
                    return 'symfony' in content
                    
            elif framework == 'django':
                # Check for Django-specific imports in manage.py
                cmd = f"docker exec {self.container} head -10 {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    content = result.stdout.lower()
                    return 'django' in content
                    
            elif framework == 'rails':
                # Check Gemfile for Rails gem
                cmd = f"docker exec {self.container} cat {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    content = result.stdout.lower()
                    return 'rails' in content
                    
            elif framework == 'laravel':
                # Check artisan file for Laravel
                cmd = f"docker exec {self.container} head -10 {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    content = result.stdout.lower()
                    return 'laravel' in content
                    
            elif framework == 'nodejs':
                # Check package.json for Express or other Node.js frameworks
                cmd = f"docker exec {self.container} cat {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    content = result.stdout.lower()
                    return any(fw in content for fw in ['express', 'koa', 'fastify', 'next'])
                    
            elif framework == 'php':
                # Basic PHP validation
                return True
                
        except Exception as e:
            logger.debug(f"Error validating framework {framework}: {e}")
            
        return False
    
    def _initialize_mapper(self):
        """Initialize the appropriate framework-specific mapper."""
        if self.framework == 'symfony':
            from .symfony_source_mapper import SymfonySourceMapper
            self.mapper = SymfonySourceMapper(self.container)
            
        elif self.framework == 'django':
            self.mapper = DjangoSourceMapper(self.container, self.app_root)
            
        elif self.framework == 'rails':
            self.mapper = RailsSourceMapper(self.container, self.app_root)
            
        elif self.framework == 'laravel':
            self.mapper = LaravelSourceMapper(self.container, self.app_root)
            
        elif self.framework == 'nodejs':
            self.mapper = NodeJSSourceMapper(self.container, self.app_root)
            
        elif self.framework == 'php':
            self.mapper = GenericPHPSourceMapper(self.container, self.app_root)
            
        else:
            logger.warning(f"No mapper available for framework: {self.framework}")
            self.mapper = None
    
    def _file_exists_in_container(self, file_path: str) -> bool:
        """Check if file exists in container."""
        try:
            cmd = f"docker exec {self.container} test -f {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=3)
            return result.returncode == 0
        except Exception:
            return False


class GenericPHPSourceMapper:
    """Generic PHP application source mapper."""
    
    def __init__(self, container_name: str, app_root: str):
        self.container = container_name
        self.app_root = app_root
        
    def map_exploration_graph_to_sources(self, run_dir: str) -> Dict[str, List[str]]:
        """Map URLs to PHP source files using generic analysis."""
        logger.info("Using generic PHP source mapping")
        
        # Extract URLs from exploration
        urls = self._extract_urls_from_exploration(run_dir)
        logger.info(f"Found {len(urls)} unique URLs in exploration graph")
        
        url_to_files = {}
        for url in urls:
            source_files = self._map_url_to_php_sources(url)
            if source_files:
                url_to_files[url] = source_files
                
        # Save mapping
        self._save_mapping(run_dir, url_to_files)
        return url_to_files
    
    def _extract_urls_from_exploration(self, run_dir: str) -> Set[str]:
        """Extract URLs from exploration graph."""
        urls = set()
        
        # Find all step_info.json files
        for root, dirs, files in os.walk(run_dir):
            for file in files:
                if file == 'step_info.json':
                    step_file = os.path.join(root, file)
                    try:
                        with open(step_file, 'r') as f:
                            step_data = json.load(f)
                        
                        # Extract URL from observation.last_action
                        if 'observation' in step_data and 'last_action' in step_data['observation']:
                            last_action = step_data['observation']['last_action']
                            url = self._extract_url_from_action(last_action)
                            if url and self._is_localhost_url(url):
                                urls.add(url)
                                
                    except Exception as e:
                        logger.error(f"Error processing {step_file}: {e}")
                        continue
        
        return urls
    
    def _extract_url_from_action(self, action: str) -> Optional[str]:
        """Extract URL from action string."""
        if not action or 'goto(' not in action:
            return None
            
        match = re.search(r"goto\(['\"]([^'\"]+)['\"]", action)
        if match:
            return match.group(1)
        return None
    
    def _is_localhost_url(self, url: str) -> bool:
        """Check if URL is a localhost URL."""
        if not url:
            return False
        
        parsed = urlparse(url)
        hostname = parsed.hostname
        return hostname in ['localhost', '127.0.0.1'] or (hostname and hostname.startswith('192.168.'))
    
    def _map_url_to_php_sources(self, url: str) -> List[str]:
        """Map URL to PHP source files using generic analysis."""
        source_files = []
        
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        # Try to find corresponding PHP files
        potential_files = [
            f"{self.app_root}{path}.php",
            f"{self.app_root}{path}/index.php",
            f"{self.app_root}/public{path}.php",
            f"{self.app_root}/public{path}/index.php",
            f"{self.app_root}/index.php"  # Fallback to main entry point
        ]
        
        for file_path in potential_files:
            if self._file_exists_in_container(file_path):
                source_files.append(file_path)
                break
                
        return source_files
    
    def _file_exists_in_container(self, file_path: str) -> bool:
        """Check if file exists in container."""
        try:
            cmd = f"docker exec {self.container} test -f {file_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=3)
            return result.returncode == 0
        except Exception:
            return False
    
    def _save_mapping(self, run_dir: str, url_to_files: Dict[str, List[str]]):
        """Save URL to source files mapping."""
        mapping_file = os.path.join(run_dir, "source_mapping.json")
        try:
            with open(mapping_file, 'w') as f:
                json.dump(url_to_files, f, indent=2)
            logger.info(f"Saved source mapping to {mapping_file}")
        except Exception as e:
            logger.error(f"Error saving mapping: {e}")


def main():
    parser = argparse.ArgumentParser(description="Universal source file mapper for Go-Browse")
    parser.add_argument("--run_dir", type=str, required=True,
                       help="Path to the Go-Browse run directory")
    parser.add_argument("--container", type=str, required=True,
                       help="Docker container name")
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.run_dir):
        logger.error(f"Run directory does not exist: {args.run_dir}")
        return 1
    
    # Check container
    check_cmd = f"docker ps --filter name={args.container} --format '{{{{.Names}}}}'"
    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
    
    if args.container not in result.stdout:
        logger.error(f"Docker container '{args.container}' is not running")
        return 1
    
    # Create universal mapper and run
    mapper = UniversalSourceMapper(args.container)
    
    try:
        url_to_files = mapper.map_exploration_graph_to_sources(args.run_dir)
        
        logger.info("Source mapping completed successfully!")
        logger.info(f"Mapped {len(url_to_files)} URLs to source files")
        
        total_files = sum(len(files) for files in url_to_files.values())
        logger.info(f"Total source files identified: {total_files}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during source mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit(main())
