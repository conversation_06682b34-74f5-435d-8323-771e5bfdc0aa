#!/usr/bin/env python3
"""
Source File Mapper for Go-Browse

This tool analyzes exploration graphs and maps visited URLs to their corresponding 
source files in Docker containers through real analysis (not heuristics).

Usage:
    python source_mapper.py --run_dir ./runs/reddit_qwen_01 --container reddit_container --server_type nginx
"""

import os
import json
import glob
import argparse
import subprocess
import logging
import re
from typing import Dict, List, Set, Optional, Tuple
from urllib.parse import urlparse, parse_qs
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SourceFileMapper:
    def __init__(self, container_name: str, server_type: str):
        self.container = container_name
        self.server_type = server_type.lower()
        self.url_to_files = {}
        
    def map_exploration_graph_to_sources(self, run_dir: str) -> Dict[str, List[str]]:
        """
        Main method to map exploration graph URLs to source files.
        Returns: {url: [source_file_paths]}
        """
        logger.info(f"Starting source file mapping for run: {run_dir}")
        
        # Step 1: Extract all URLs from exploration graph
        visited_urls = self._extract_urls_from_graph(run_dir)
        logger.info(f"Found {len(visited_urls)} unique URLs in exploration graph")
        
        # Step 2: For each URL, determine source files through real analysis
        url_to_files = {}
        for i, url in enumerate(visited_urls, 1):
            logger.info(f"Analyzing URL {i}/{len(visited_urls)}: {url}")
            source_files = self._locate_source_files_for_url(url)
            if source_files:
                url_to_files[url] = source_files
                logger.info(f"  -> Found {len(source_files)} source files")
            else:
                logger.warning(f"  -> No source files found for {url}")
        
        # Step 3: Save mapping results
        self._save_mapping(run_dir, url_to_files)
        logger.info(f"Source mapping completed. Mapped {len(url_to_files)} URLs to source files")
        
        return url_to_files
    
    def _extract_urls_from_graph(self, run_dir: str) -> Set[str]:
        """Extract all visited URLs from exploration graph step_info.json files."""
        urls = set()
        
        # Find all step_info.json files in the graph
        step_info_pattern = os.path.join(run_dir, "graph", "**", "step_info.json")
        step_info_files = glob.glob(step_info_pattern, recursive=True)
        
        logger.info(f"Found {len(step_info_files)} step_info.json files")
        
        for step_file in step_info_files:
            try:
                with open(step_file, 'r') as f:
                    step_data = json.load(f)
                
                # Extract URL from observation.last_action
                if 'observation' in step_data and 'last_action' in step_data['observation']:
                    last_action = step_data['observation']['last_action']
                    url = self._extract_url_from_action(last_action)
                    if url and self._is_localhost_url(url):
                        urls.add(url)
                        
            except Exception as e:
                logger.error(f"Error processing {step_file}: {e}")
                continue
        
        return urls
    
    def _extract_url_from_action(self, action: str) -> Optional[str]:
        """Extract URL from action string like goto('http://localhost:9999/path')."""
        if not action or 'goto(' not in action:
            return None
            
        # Extract URL from goto('url') pattern
        match = re.search(r"goto\(['\"]([^'\"]+)['\"]", action)
        if match:
            return match.group(1)
        return None

    def _is_localhost_url(self, url: str) -> bool:
        """Check if URL is a localhost URL that we should analyze."""
        if not url:
            return False

        parsed = urlparse(url)
        hostname = parsed.hostname

        # Only process localhost URLs
        return hostname in ['localhost', '127.0.0.1'] or (hostname and hostname.startswith('192.168.'))

    def _locate_source_files_for_url(self, url: str) -> List[str]:
        """
        Locate source files for a given URL through real analysis.
        Uses multiple methods to ensure accuracy.
        """
        source_files = []
        
        # Method 1: Analyze web server configuration
        config_files = self._analyze_server_config(url)
        source_files.extend(config_files)
        
        # Method 2: Trace actual file access through system calls
        traced_files = self._trace_file_access(url)
        source_files.extend(traced_files)
        
        # Method 3: Analyze application framework routing
        route_files = self._analyze_application_routes(url)
        source_files.extend(route_files)
        
        # Remove duplicates and verify files exist
        unique_files = list(set(source_files))
        verified_files = self._verify_files_exist(unique_files)
        
        return verified_files
    
    def _analyze_server_config(self, url: str) -> List[str]:
        """Analyze web server configuration to find source files."""
        try:
            if self.server_type == 'nginx':
                return self._analyze_nginx_config(url)
            elif self.server_type == 'apache':
                return self._analyze_apache_config(url)
            elif self.server_type in ['django', 'flask', 'fastapi']:
                return self._analyze_python_app_config(url)
            elif self.server_type == 'php':
                return self._analyze_php_config(url)
            else:
                logger.warning(f"Unsupported server type: {self.server_type}")
                return []
        except Exception as e:
            logger.error(f"Error analyzing server config for {url}: {e}")
            return []
    
    def _analyze_nginx_config(self, url: str) -> List[str]:
        """Analyze nginx configuration to find document root and routing."""
        files = []
        
        # Get nginx configuration
        cmd = f"docker exec {self.container} find /etc/nginx -name '*.conf' -exec cat {{}} \\;"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to read nginx config: {result.stderr}")
            return files
        
        config = result.stdout
        
        # Extract document root
        root_match = re.search(r'root\s+([^;]+);', config)
        if root_match:
            document_root = root_match.group(1).strip()
            
            # Map URL path to file path
            parsed_url = urlparse(url)
            url_path = parsed_url.path if parsed_url.path != '/' else '/index'
            
            # Common file extensions to check
            extensions = ['.html', '.php', '.py', '.js', '']
            for ext in extensions:
                potential_file = os.path.join(document_root, url_path.lstrip('/') + ext)
                if self._file_exists_in_container(potential_file):
                    files.append(potential_file)
        
        # Look for location blocks that might handle this URL
        location_blocks = re.findall(r'location\s+([^{]+)\s*{([^}]+)}', config, re.DOTALL)
        for location_pattern, location_config in location_blocks:
            if self._url_matches_location(url, location_pattern.strip()):
                # Extract files from location config
                location_files = self._extract_files_from_location_config(location_config)
                files.extend(location_files)
        
        return files

    def _analyze_apache_config(self, url: str) -> List[str]:
        """Analyze Apache configuration to find source files."""
        files = []

        # Get Apache configuration
        cmd = f"docker exec {self.container} find /etc/apache2 -name '*.conf' -exec cat {{}} \\;"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"Failed to read Apache config: {result.stderr}")
            return files

        config = result.stdout

        # Extract DocumentRoot
        root_match = re.search(r'DocumentRoot\s+([^\s]+)', config)
        if root_match:
            document_root = root_match.group(1).strip()
            parsed_url = urlparse(url)
            url_path = parsed_url.path if parsed_url.path != '/' else '/index'

            extensions = ['.html', '.php', '.py', '']
            for ext in extensions:
                potential_file = os.path.join(document_root, url_path.lstrip('/') + ext)
                if self._file_exists_in_container(potential_file):
                    files.append(potential_file)

        return files

    def _analyze_python_app_config(self, url: str) -> List[str]:
        """Analyze Python web application routing (Django/Flask/FastAPI)."""
        files = []

        # Find Python application files
        cmd = f"docker exec {self.container} find /app -name '*.py' -type f"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode != 0:
            return files

        python_files = result.stdout.strip().split('\n')

        # Look for URL routing patterns
        parsed_url = urlparse(url)
        url_path = parsed_url.path

        for py_file in python_files:
            if not py_file.strip():
                continue

            # Read file content
            cmd = f"docker exec {self.container} cat {py_file}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                continue

            content = result.stdout

            # Look for URL patterns that match
            if self._python_file_handles_url(content, url_path):
                files.append(py_file)

                # Also look for template files referenced in this Python file
                template_files = self._extract_template_references(content)
                files.extend(template_files)

        return files

    def _analyze_php_config(self, url: str) -> List[str]:
        """Analyze PHP application to find source files."""
        files = []

        # Find document root from web server config first
        document_root = self._get_document_root()
        if not document_root:
            document_root = "/var/www/html"  # Default fallback

        parsed_url = urlparse(url)
        url_path = parsed_url.path

        # Direct PHP file mapping
        if url_path == '/':
            php_files = ['index.php', 'home.php', 'main.php']
        else:
            php_files = [url_path.lstrip('/') + '.php', url_path.lstrip('/') + '/index.php']

        for php_file in php_files:
            full_path = os.path.join(document_root, php_file)
            if self._file_exists_in_container(full_path):
                files.append(full_path)

        return files

    def _trace_file_access(self, url: str) -> List[str]:
        """
        Trace actual file access by making a request and monitoring file system.
        This is the most accurate method.
        """
        files = []

        try:
            # Method 1: Use strace to trace file access (if available)
            if self._is_strace_available():
                files_from_strace = self._trace_with_strace(url)
                files.extend(files_from_strace)
            else:
                logger.debug("strace not available, skipping strace tracing")

            # Method 2: Monitor file access timestamps (simplified version)
            files_from_timestamps = self._trace_with_timestamps_simple(url)
            files.extend(files_from_timestamps)

        except Exception as e:
            logger.error(f"Error tracing file access for {url}: {e}")

        return files

    def _trace_with_strace(self, url: str) -> List[str]:
        """Use strace to trace file system calls when accessing URL."""
        files = []

        try:
            # Get the web server process PID
            cmd = f"docker exec {self.container} pgrep -f 'nginx|apache|python|php'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                return files

            pids = result.stdout.strip().split('\n')
            if not pids or not pids[0]:
                return files

            main_pid = pids[0]

            # Start strace in background
            strace_cmd = f"docker exec -d {self.container} strace -f -e trace=openat,open -p {main_pid} -o /tmp/strace.log"
            subprocess.run(strace_cmd, shell=True)

            # Make request to URL
            self._make_request_to_url(url)

            # Stop strace and read results
            subprocess.run(f"docker exec {self.container} pkill strace", shell=True)

            # Read strace log
            cmd = f"docker exec {self.container} cat /tmp/strace.log 2>/dev/null || echo ''"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                files = self._parse_strace_output(result.stdout)

            # Cleanup
            subprocess.run(f"docker exec {self.container} rm -f /tmp/strace.log", shell=True)

        except Exception as e:
            logger.error(f"Error with strace tracing: {e}")

        return files

    def _is_strace_available(self) -> bool:
        """Check if strace is available in the container."""
        try:
            cmd = f"docker exec {self.container} which strace"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False

    def _trace_with_timestamps_simple(self, url: str) -> List[str]:
        """Simplified timestamp-based file tracing."""
        files = []

        try:
            # Get common web files and their timestamps before request
            web_files = self._get_common_web_files()
            before_times = {}

            for file_path in web_files[:20]:  # Limit to first 20 files for performance
                stat_cmd = f"docker exec {self.container} stat -c %X {file_path} 2>/dev/null || echo '0'"
                result = subprocess.run(stat_cmd, shell=True, capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    before_times[file_path] = int(result.stdout.strip() or '0')

            # Make request
            self._make_request_to_url(url)

            # Check for changed access times
            for file_path, before_time in before_times.items():
                stat_cmd = f"docker exec {self.container} stat -c %X {file_path} 2>/dev/null || echo '0'"
                result = subprocess.run(stat_cmd, shell=True, capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    after_time = int(result.stdout.strip() or '0')
                    if after_time > before_time:
                        files.append(file_path)

        except Exception as e:
            logger.error(f"Error with simple timestamp tracing: {e}")

        return files

    def _get_common_web_files(self) -> List[str]:
        """Get list of common web files in the container."""
        files = []

        # Common web directories
        web_dirs = ['/var/www', '/app', '/usr/share/nginx/html']

        for web_dir in web_dirs:
            try:
                cmd = f"docker exec {self.container} find {web_dir} -type f \\( -name '*.html' -o -name '*.php' -o -name '*.py' -o -name '*.js' -o -name '*.css' \\) 2>/dev/null | head -10"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    dir_files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
                    files.extend(dir_files)
            except Exception as e:
                logger.debug(f"Error scanning {web_dir}: {e}")
                continue

        return files

    def _trace_with_timestamps(self, url: str) -> List[str]:
        """Monitor file access timestamps to identify accessed files."""
        files = []

        try:
            # Get baseline timestamps for common web directories
            web_dirs = ['/var/www', '/app', '/usr/share/nginx', '/etc/nginx', '/etc/apache2']
            baseline_times = {}

            for web_dir in web_dirs:
                cmd = f"docker exec {self.container} find {web_dir} -type f -name '*.html' -o -name '*.php' -o -name '*.py' -o -name '*.js' -o -name '*.css' 2>/dev/null | head -100"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                if result.returncode == 0:
                    for file_path in result.stdout.strip().split('\n'):
                        if file_path.strip():
                            # Get file modification time
                            stat_cmd = f"docker exec {self.container} stat -c %Y {file_path} 2>/dev/null || echo '0'"
                            stat_result = subprocess.run(stat_cmd, shell=True, capture_output=True, text=True)
                            if stat_result.returncode == 0:
                                baseline_times[file_path] = int(stat_result.stdout.strip() or '0')

            # Make request to URL
            self._make_request_to_url(url)

            # Check for files with updated access times
            for file_path, baseline_time in baseline_times.items():
                # Get current access time
                stat_cmd = f"docker exec {self.container} stat -c %X {file_path} 2>/dev/null || echo '0'"
                result = subprocess.run(stat_cmd, shell=True, capture_output=True, text=True)

                if result.returncode == 0:
                    current_time = int(result.stdout.strip() or '0')
                    if current_time > baseline_time:
                        files.append(file_path)

        except Exception as e:
            logger.error(f"Error with timestamp tracing: {e}")

        return files

    def _analyze_application_routes(self, url: str) -> List[str]:
        """Analyze application-specific routing configurations."""
        files = []

        try:
            # Look for common routing files
            routing_files = self._find_routing_files()

            parsed_url = urlparse(url)
            url_path = parsed_url.path

            for routing_file in routing_files:
                # Read routing file content
                cmd = f"docker exec {self.container} cat {routing_file}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                if result.returncode == 0:
                    content = result.stdout
                    route_files = self._extract_files_from_routes(content, url_path)
                    files.extend(route_files)

        except Exception as e:
            logger.error(f"Error analyzing application routes: {e}")

        return files

    def _find_routing_files(self) -> List[str]:
        """Find common routing configuration files."""
        routing_files = []

        # Common routing file patterns
        patterns = [
            "urls.py",      # Django
            "routes.py",    # Flask/FastAPI
            "app.py",       # Flask
            "main.py",      # FastAPI
            "*.routes.js",  # Node.js
            "web.php",      # Laravel
            ".htaccess",    # Apache
        ]

        for pattern in patterns:
            cmd = f"docker exec {self.container} find /app /var/www -name '{pattern}' -type f 2>/dev/null"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                files = result.stdout.strip().split('\n')
                routing_files.extend([f for f in files if f.strip()])

        return routing_files

    # Helper methods
    def _file_exists_in_container(self, file_path: str) -> bool:
        """Check if file exists in container."""
        cmd = f"docker exec {self.container} test -f {file_path}"
        result = subprocess.run(cmd, shell=True, capture_output=True)
        return result.returncode == 0

    def _make_request_to_url(self, url: str):
        """Make HTTP request to URL to trigger file access."""
        try:
            cmd = f"docker exec {self.container} curl -s {url} > /dev/null"
            subprocess.run(cmd, shell=True, timeout=10)
        except Exception as e:
            logger.error(f"Error making request to {url}: {e}")

    def _get_document_root(self) -> Optional[str]:
        """Get document root from web server configuration."""
        try:
            if self.server_type == 'nginx':
                cmd = f"docker exec {self.container} grep -r 'root ' /etc/nginx/ | head -1"
            elif self.server_type == 'apache':
                cmd = f"docker exec {self.container} grep -r 'DocumentRoot' /etc/apache2/ | head -1"
            else:
                return None

            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                # Extract path from config line
                line = result.stdout.strip()
                if 'root ' in line:
                    return line.split('root ')[1].split(';')[0].strip()
                elif 'DocumentRoot' in line:
                    return line.split('DocumentRoot')[1].strip()
        except Exception as e:
            logger.error(f"Error getting document root: {e}")

        return None

    def _url_matches_location(self, url: str, location_pattern: str) -> bool:
        """Check if URL matches nginx location pattern."""
        parsed_url = urlparse(url)
        url_path = parsed_url.path

        # Simple pattern matching (can be enhanced)
        if location_pattern.startswith('~'):
            # Regex pattern
            pattern = location_pattern[1:].strip()
            return bool(re.search(pattern, url_path))
        else:
            # Exact or prefix match
            return url_path.startswith(location_pattern.strip())

    def _extract_files_from_location_config(self, config: str) -> List[str]:
        """Extract file references from nginx location config."""
        files = []

        # Look for try_files directive
        try_files_match = re.search(r'try_files\s+([^;]+);', config)
        if try_files_match:
            file_list = try_files_match.group(1).strip().split()
            for file_ref in file_list:
                if file_ref.startswith('/') and not file_ref.startswith('/$'):
                    files.append(file_ref)

        # Look for alias directive
        alias_match = re.search(r'alias\s+([^;]+);', config)
        if alias_match:
            alias_path = alias_match.group(1).strip()
            files.append(alias_path)

        return files

    def _python_file_handles_url(self, content: str, url_path: str) -> bool:
        """Check if Python file contains routing for the given URL path."""
        # Django URL patterns
        django_patterns = [
            rf"path\(['\"].*{re.escape(url_path)}.*['\"]",
            rf"url\(.*{re.escape(url_path)}.*\)",
        ]

        # Flask route patterns
        flask_patterns = [
            rf"@app\.route\(['\"].*{re.escape(url_path)}.*['\"]",
        ]

        # FastAPI route patterns
        fastapi_patterns = [
            rf"@app\.(get|post|put|delete)\(['\"].*{re.escape(url_path)}.*['\"]",
        ]

        all_patterns = django_patterns + flask_patterns + fastapi_patterns

        for pattern in all_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        return False

    def _extract_template_references(self, content: str) -> List[str]:
        """Extract template file references from Python code."""
        templates = []

        # Common template rendering patterns
        patterns = [
            r"render_template\(['\"]([^'\"]+)['\"]",  # Flask
            r"render\([^,]+,\s*['\"]([^'\"]+)['\"]",  # Django
            r"templates\.TemplateResponse\(['\"]([^'\"]+)['\"]",  # FastAPI
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # Convert template name to full path
                template_path = self._resolve_template_path(match)
                if template_path:
                    templates.append(template_path)

        return templates

    def _resolve_template_path(self, template_name: str) -> Optional[str]:
        """Resolve template name to full file path."""
        # Common template directories
        template_dirs = ['/app/templates', '/var/www/templates', '/app/views']

        for template_dir in template_dirs:
            full_path = os.path.join(template_dir, template_name)
            if self._file_exists_in_container(full_path):
                return full_path

        return None

    def _parse_strace_output(self, strace_output: str) -> List[str]:
        """Parse strace output to extract accessed files."""
        files = []

        # Look for successful file opens
        lines = strace_output.split('\n')
        for line in lines:
            if 'openat(' in line and '= ' in line:
                # Extract file path from openat system call
                match = re.search(r'openat\([^,]+,\s*"([^"]+)"', line)
                if match:
                    file_path = match.group(1)
                    # Filter for relevant web files
                    if any(ext in file_path for ext in ['.html', '.php', '.py', '.js', '.css']):
                        files.append(file_path)

        return files

    def _extract_files_from_routes(self, content: str, url_path: str) -> List[str]:
        """Extract files from routing configuration content."""
        files = []

        # This is a simplified implementation
        # In practice, you'd need more sophisticated parsing for each framework

        # Look for file references near URL patterns
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if url_path in line:
                # Look in surrounding lines for file references
                context_lines = lines[max(0, i-3):i+4]
                for context_line in context_lines:
                    # Look for file patterns
                    file_matches = re.findall(r'[\'"]([^\'\"]*\.(html|php|py|js|css))[\'"]', context_line)
                    for match, _ in file_matches:
                        resolved_path = self._resolve_file_path(match)
                        if resolved_path:
                            files.append(resolved_path)

        return files

    def _resolve_file_path(self, relative_path: str) -> Optional[str]:
        """Resolve relative file path to absolute path in container."""
        # Try common base directories
        base_dirs = ['/app', '/var/www/html', '/usr/share/nginx/html']

        for base_dir in base_dirs:
            full_path = os.path.join(base_dir, relative_path)
            if self._file_exists_in_container(full_path):
                return full_path

        return None

    def _verify_files_exist(self, files: List[str]) -> List[str]:
        """Verify that files actually exist in the container."""
        verified_files = []

        for file_path in files:
            if self._file_exists_in_container(file_path):
                verified_files.append(file_path)
            else:
                logger.debug(f"File does not exist: {file_path}")

        return verified_files

    def _save_mapping(self, run_dir: str, url_to_files: Dict[str, List[str]]):
        """Save URL to source files mapping to JSON file."""
        mapping_file = os.path.join(run_dir, "source_mapping.json")

        try:
            with open(mapping_file, 'w') as f:
                json.dump(url_to_files, f, indent=2)
            logger.info(f"Saved source mapping to {mapping_file}")
        except Exception as e:
            logger.error(f"Error saving mapping to {mapping_file}: {e}")


def main():
    parser = argparse.ArgumentParser(description="Map Go-Browse exploration URLs to source files")
    parser.add_argument("--run_dir", type=str, required=True,
                       help="Path to the Go-Browse run directory")
    parser.add_argument("--container", type=str, required=True,
                       help="Docker container name")
    parser.add_argument("--server_type", type=str, required=True,
                       choices=['nginx', 'apache', 'django', 'flask', 'fastapi', 'php'],
                       help="Web server/framework type")

    args = parser.parse_args()

    # Validate run directory
    if not os.path.exists(args.run_dir):
        logger.error(f"Run directory does not exist: {args.run_dir}")
        return 1

    # Check if Docker container is running
    check_cmd = f"docker ps --filter name={args.container} --format '{{{{.Names}}}}'"
    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)

    if args.container not in result.stdout:
        logger.error(f"Docker container '{args.container}' is not running")
        return 1

    # Create source mapper and run mapping
    mapper = SourceFileMapper(args.container, args.server_type)

    try:
        url_to_files = mapper.map_exploration_graph_to_sources(args.run_dir)

        logger.info("Source mapping completed successfully!")
        logger.info(f"Mapped {len(url_to_files)} URLs to source files")

        # Print summary
        total_files = sum(len(files) for files in url_to_files.values())
        logger.info(f"Total source files identified: {total_files}")

        return 0

    except Exception as e:
        logger.error(f"Error during source mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit(main())
