import logging
import time
import asyncio
import json
import re
import base64
from typing import List, Set
from urllib.parse import urljoin

from playwright.async_api import async_playwright

# 配置日志记录，方便调试
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_path(path: str) -> str:
    """清理webpack路径的辅助函数"""
    if not path or not isinstance(path, str): return None
    if path.startswith('webpack:///'): path = path[len('webpack:///'):]
    elif path.startswith('webpack://'): path = path[len('webpack://'):]
    if path.startswith('/'): path = path[1:]
    if path.startswith('./'): path = path[2:]
    query_index = path.find('?')
    if query_index != -1: path = path[:query_index]
    if '(webpack)' in path or 'node_modules' in path: return None
    return path

async def get_webpack_sources(url: str, timeout: int = 20) -> List[str]:
    """
    使用Playwright访问给定的URL，通过最终的混合策略精确捕获所有类型的webpack模块。
    - 阶段1: 通过劫持 webpackJsonp.push 获取JS模块。
    - 阶段2: 通过解析CSS文件及其Source Maps（包括data: URI）获取CSS/LESS等样式模块。

    Args:
        url: 要分析的目标网站URL。
        timeout: 等待的超时时间（秒）。

    Returns:
        一个包含所有找到的源文件路径的列表。
    """
    all_sources: Set[str] = set()

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(bypass_csp=True)
        page = await context.new_page()

        try:
            # --- 阶段 1: 获取 JavaScript 模块 ---
            js_getter_script = """
            () => {
                return new Promise((resolve) => {
                    const sources = new Set();
                    const processChunk = (chunk) => {
                        if (!chunk || !Array.isArray(chunk)) return;
                        const modulesObject = chunk[1];
                        if (typeof modulesObject === 'object' && modulesObject !== null) {
                            Object.keys(modulesObject).forEach(key => sources.add(key));
                        }
                    };
                    if (window.webpackJsonp && Array.isArray(window.webpackJsonp)) {
                        window.webpackJsonp.forEach(processChunk);
                    }
                    const originalPush = window.webpackJsonp ? window.webpackJsonp.push.bind(window.webpackJsonp) : null;
                    window.webpackJsonp = window.webpackJsonp || [];
                    window.webpackJsonp.push = function(...args) {
                        args.forEach(processChunk);
                        if (originalPush) return originalPush(...args);
                    };
                    // 等待一段时间让所有初始脚本运行
                    setTimeout(() => resolve(Array.from(sources)), 5000);
                });
            }
            """
            logger.info(f"Navigating to URL: {url}")
            await page.goto(url, wait_until='networkidle', timeout=30000)

            logger.info("Phase 1: Getting JS modules via webpackJsonp...")
            js_sources = await page.evaluate(js_getter_script)
            for source in js_sources:
                cleaned = clean_path(source)
                if cleaned:
                    all_sources.add(cleaned)
            logger.info(f"Phase 1 complete. Found {len(js_sources)} JS modules.")

            # --- 阶段 2: 获取 CSS/LESS 等样式模块 ---
            logger.info("Phase 2: Getting CSS/Style modules via Source Maps...")
            stylesheet_links = await page.eval_on_selector_all('link[rel="stylesheet"]', 'elements => elements.map(el => el.href)')
            
            for link in stylesheet_links:
                try:
                    logger.info(f"  - Analyzing stylesheet: {link}")
                    css_response = await page.request.get(link)
                    css_content = await css_response.text()

                    # 查找 sourceMappingURL
                    match = re.search(r'/\*#\s*sourceMappingURL=(.*?)\s*\*/', css_content)
                    if not match:
                        logger.warning(f"    - No source map URL found in {link}")
                        continue

                    map_url_part = match.group(1)
                    
                    map_data = None
                    # 关键修复：使用更宽松的条件检查 data: URI
                    if map_url_part.startswith('data:application/json;'):
                        logger.info("    - Found inline Base64 source map. Decoding...")
                        base64_data = map_url_part.split(',', 1)[1]
                        decoded_data = base64.b64decode(base64_data).decode('utf-8')
                        map_data = json.loads(decoded_data)
                    else:
                        map_url = urljoin(link, map_url_part)
                        logger.info(f"    - Found source map URL: {map_url}")
                        map_response = await page.request.get(map_url)
                        map_data = await map_response.json()

                    if map_data and 'sources' in map_data and isinstance(map_data['sources'], list):
                        logger.info(f"    - Success! Found {len(map_data['sources'])} sources in map.")
                        for source in map_data['sources']:
                            cleaned = clean_path(source)
                            if cleaned:
                                all_sources.add(cleaned)
                    else:
                        logger.warning(f"    - Source map from {link} is missing a 'sources' array.")

                except Exception as e:
                    logger.error(f"  - Failed to process stylesheet {link}: {e}")
            
            logger.info("Phase 2 complete.")

        except Exception as e:
            logger.error(f"An error occurred during webpack source discovery: {e}")
        finally:
            logger.info("Closing browser context.")
            await context.close()
            await browser.close()

    return sorted(list(all_sources))

async def main():
    target_url = "http://localhost:9999/"  # <--- 在这里替换您的 URL
    discovered_files = await get_webpack_sources(target_url)

    if discovered_files:
        print("\n--- Discovered Webpack Source Files ---")
        for file_path in discovered_files:
            print(file_path)
        print(f"\nTotal: {len(discovered_files)} files found.")
    else:
        print("\n--- No Webpack Source Files Discovered ---")

# 在Notebook中，您可以直接在另一个单元格中运行: await main()
# 为了让这个单元格可以独立运行，我们这样启动它：
try:
    loop = asyncio.get_running_loop()
    if loop.is_running():
        print("Awaiting main function in existing event loop...")
        await main()
except RuntimeError:
    print("Starting new event loop to run main function...")
    asyncio.run(main())


