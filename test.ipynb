{"cells": [{"cell_type": "code", "execution_count": 18, "id": "f385e86f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Awaiting main function in existing event loop...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-01 12:05:44,688 - INFO - Navigating to URL: http://localhost:9999/\n", "2025-08-01 12:05:45,519 - INFO - Phase 1: Getting JS modules via webpackJsonp...\n", "2025-08-01 12:05:50,544 - INFO - Phase 1 complete. Found 998 JS modules.\n", "2025-08-01 12:05:50,544 - INFO - Phase 2: Getting CSS/Style modules via Source Maps...\n", "2025-08-01 12:05:50,580 - INFO -   - Analyzing stylesheet: http://localhost:9999/build/fonts/Roboto.8afec132.css\n", "2025-08-01 12:05:50,615 - INFO -     - Found inline Base64 source map. Decoding...\n", "2025-08-01 12:05:50,616 - INFO -     - Success! Found 1 sources in map.\n", "2025-08-01 12:05:50,616 - INFO -   - Analyzing stylesheet: http://localhost:9999/build/core.257311e2.css\n", "2025-08-01 12:05:50,641 - INFO -     - Found inline Base64 source map. Decoding...\n", "2025-08-01 12:05:50,642 - INFO -     - Success! Found 81 sources in map.\n", "2025-08-01 12:05:50,643 - INFO -   - Analyzing stylesheet: http://localhost:9999/build/themes/postmill.a454a2fb.css\n", "2025-08-01 12:05:50,660 - INFO -     - Found inline Base64 source map. Decoding...\n", "2025-08-01 12:05:50,661 - INFO -     - Success! Found 8 sources in map.\n", "2025-08-01 12:05:50,661 - INFO - Phase 2 complete.\n", "2025-08-01 12:05:50,662 - INFO - Closing browser context.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Discovered Webpack Source Files ---\n", "assets/css/_card/dropdown-card.less\n", "assets/css/_form/_mixins.less\n", "assets/css/_form/compound-form-widget.less\n", "assets/css/_form/decorated-form-control.less\n", "assets/css/_form/fieldset.less\n", "assets/css/_form/form-control.less\n", "assets/css/_form/form-error-list.less\n", "assets/css/_form/form-flex.less\n", "assets/css/_form/form-tabs.less\n", "assets/css/_form/form.less\n", "assets/css/_form/formatting-help.less\n", "assets/css/_form/markdown-preview.less\n", "assets/css/_form/unstylable-widget.less\n", "assets/css/_global.less\n", "assets/css/_layout/content-container.less\n", "assets/css/_layout/flow.less\n", "assets/css/_layout/sidebar.less\n", "assets/css/_layout/site-accessibility-nav.less\n", "assets/css/_layout/site-alerts.less\n", "assets/css/_layout/site-content.less\n", "assets/css/_layout/site-footer.less\n", "assets/css/_layout/site-nav.less\n", "assets/css/_layout/text-flow.less\n", "assets/css/_resets/unbuttonize.less\n", "assets/css/_resets/undecorate.less\n", "assets/css/_resets/unheaderize.less\n", "assets/css/_resets/unlistify.less\n", "assets/css/_things/alert.less\n", "assets/css/_things/border-list.less\n", "assets/css/_things/columns.less\n", "assets/css/_things/comment.less\n", "assets/css/_things/definition-list.less\n", "assets/css/_things/drop-zone.less\n", "assets/css/_things/empty.less\n", "assets/css/_things/flair.less\n", "assets/css/_things/flex.less\n", "assets/css/_things/heading-permalink.less\n", "assets/css/_things/hideable.less\n", "assets/css/_things/icon.less\n", "assets/css/_things/message.less\n", "assets/css/_things/submission-meta.less\n", "assets/css/_things/submission.less\n", "assets/css/_things/table-of-contents.less\n", "assets/css/_things/table.less\n", "assets/css/_things/user-flag.less\n", "assets/css/_things/vote.less\n", "assets/css/_things/wiki-article.less\n", "assets/css/_things/wiki-lock-notice.less\n", "assets/css/_utilities/block.less\n", "assets/css/_utilities/break-text.less\n", "assets/css/_utilities/colors.less\n", "assets/css/_utilities/font-weight.less\n", "assets/css/_utilities/hidden.less\n", "assets/css/_utilities/inline.less\n", "assets/css/_utilities/monospace.less\n", "assets/css/_utilities/night-mode.less\n", "assets/css/_utilities/no-desktop.less\n", "assets/css/_utilities/no-mobile.less\n", "assets/css/_utilities/no-select.less\n", "assets/css/_utilities/no-underline.less\n", "assets/css/_utilities/no-visibility.less\n", "assets/css/_utilities/no-wrap.less\n", "assets/css/_utilities/pad.less\n", "assets/css/_utilities/page-shadow.less\n", "assets/css/_utilities/text-align.less\n", "assets/css/_utilities/text-size.less\n", "assets/css/_vendor/hljs.less\n", "assets/css/_vendor/select2.less\n", "assets/css/_vendor/tippy.less\n", "assets/css/_widgets/_mixins.less\n", "assets/css/_widgets/button.less\n", "assets/css/_widgets/clear-notification-button.less\n", "assets/css/_widgets/discreet-tab.less\n", "assets/css/_widgets/dropdown.less\n", "assets/css/_widgets/menu-item.less\n", "assets/css/_widgets/subscribe-button.less\n", "assets/css/_widgets/tab.less\n", "assets/css/themes/_modern-night.less\n", "assets/css/themes/_modern.less\n", "assets/css/themes/postmill/_dark.less\n", "assets/css/themes/postmill/_light.less\n", "assets/css/themes/postmill/index.less\n", "assets/icons/icons.svg\n", "assets/js/comment-count.js\n", "assets/js/commenting.js\n", "assets/js/controller sync recursive \\.js$\n", "assets/js/controller/alert-controller.js\n", "assets/js/controller/dialog-controller.js\n", "assets/js/controller/diff-time-controller.js\n", "assets/js/controller/fetch-titles-controller.js\n", "assets/js/controller/file-drop-controller.js\n", "assets/js/controller/markdown-controller.js\n", "assets/js/controller/relative-time-controller.js\n", "assets/js/controller/reload-captcha-controller.js\n", "assets/js/controller/remaining-chars-controller.js\n", "assets/js/controller/subscribe-button-controller.js\n", "assets/js/controller/syntax-highlight-controller.js\n", "assets/js/controller/toggle-night-mode-controller.js\n", "assets/js/controller/vote-controller.js\n", "assets/js/dropdowns.js\n", "assets/js/lib/html.js\n", "assets/js/lib/http.js\n", "assets/js/lib/icon.js\n", "assets/js/lib/intl.js\n", "assets/js/lib/time.js\n", "assets/js/main.js\n", "assets/js/select2.js\n", "assets/js/unload-forms.js\n", "assets/js/user-popper.js\n", "bazi<PERSON>-translator\n", "core.less\n", "fosjsrouting\n", "index.css\n", "index.less\n", "\n", "Total: 114 files found.\n"]}], "source": ["import logging\n", "import time\n", "import asyncio\n", "import json\n", "import re\n", "import base64\n", "from typing import List, Set\n", "from urllib.parse import urljoin\n", "\n", "from playwright.async_api import async_playwright\n", "\n", "# 配置日志记录，方便调试\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "def clean_path(path: str) -> str:\n", "    \"\"\"清理webpack路径的辅助函数\"\"\"\n", "    if not path or not isinstance(path, str): return None\n", "    if path.startswith('webpack:///'): path = path[len('webpack:///'):]\n", "    elif path.startswith('webpack://'): path = path[len('webpack://'):]\n", "    if path.startswith('/'): path = path[1:]\n", "    if path.startswith('./'): path = path[2:]\n", "    query_index = path.find('?')\n", "    if query_index != -1: path = path[:query_index]\n", "    if '(webpack)' in path or 'node_modules' in path: return None\n", "    return path\n", "\n", "async def get_webpack_sources(url: str, timeout: int = 20) -> List[str]:\n", "    \"\"\"\n", "    使用Playwright访问给定的URL，通过最终的混合策略精确捕获所有类型的webpack模块。\n", "    - 阶段1: 通过劫持 webpackJsonp.push 获取JS模块。\n", "    - 阶段2: 通过解析CSS文件及其Source Maps（包括data: URI）获取CSS/LESS等样式模块。\n", "\n", "    Args:\n", "        url: 要分析的目标网站URL。\n", "        timeout: 等待的超时时间（秒）。\n", "\n", "    Returns:\n", "        一个包含所有找到的源文件路径的列表。\n", "    \"\"\"\n", "    all_sources: Set[str] = set()\n", "\n", "    async with async_playwright() as p:\n", "        browser = await p.chromium.launch(headless=True)\n", "        context = await browser.new_context(bypass_csp=True)\n", "        page = await context.new_page()\n", "\n", "        try:\n", "            # --- 阶段 1: 获取 JavaScript 模块 ---\n", "            js_getter_script = \"\"\"\n", "            () => {\n", "                return new Promise((resolve) => {\n", "                    const sources = new Set();\n", "                    const processChunk = (chunk) => {\n", "                        if (!chunk || !Array.isArray(chunk)) return;\n", "                        const modulesObject = chunk[1];\n", "                        if (typeof modulesObject === 'object' && modulesObject !== null) {\n", "                            Object.keys(modulesObject).forEach(key => sources.add(key));\n", "                        }\n", "                    };\n", "                    if (window.webpackJsonp && Array.isArray(window.webpackJsonp)) {\n", "                        window.webpackJsonp.forEach(processChunk);\n", "                    }\n", "                    const originalPush = window.webpackJsonp ? window.webpackJsonp.push.bind(window.webpackJsonp) : null;\n", "                    window.webpackJsonp = window.webpackJsonp || [];\n", "                    window.webpackJsonp.push = function(...args) {\n", "                        args.forEach(processChunk);\n", "                        if (originalPush) return originalPush(...args);\n", "                    };\n", "                    // 等待一段时间让所有初始脚本运行\n", "                    setTimeout(() => resolve(Array.from(sources)), 5000);\n", "                });\n", "            }\n", "            \"\"\"\n", "            logger.info(f\"Navigating to URL: {url}\")\n", "            await page.goto(url, wait_until='networkidle', timeout=30000)\n", "\n", "            logger.info(\"Phase 1: Getting JS modules via webpackJsonp...\")\n", "            js_sources = await page.evaluate(js_getter_script)\n", "            for source in js_sources:\n", "                cleaned = clean_path(source)\n", "                if cleaned:\n", "                    all_sources.add(cleaned)\n", "            logger.info(f\"Phase 1 complete. Found {len(js_sources)} JS modules.\")\n", "\n", "            # --- 阶段 2: 获取 CSS/LESS 等样式模块 ---\n", "            logger.info(\"Phase 2: Getting CSS/Style modules via Source Maps...\")\n", "            stylesheet_links = await page.eval_on_selector_all('link[rel=\"stylesheet\"]', 'elements => elements.map(el => el.href)')\n", "            \n", "            for link in stylesheet_links:\n", "                try:\n", "                    logger.info(f\"  - Analyzing stylesheet: {link}\")\n", "                    css_response = await page.request.get(link)\n", "                    css_content = await css_response.text()\n", "\n", "                    # 查找 sourceMappingURL\n", "                    match = re.search(r'/\\*#\\s*sourceMappingURL=(.*?)\\s*\\*/', css_content)\n", "                    if not match:\n", "                        logger.warning(f\"    - No source map URL found in {link}\")\n", "                        continue\n", "\n", "                    map_url_part = match.group(1)\n", "                    \n", "                    map_data = None\n", "                    # 关键修复：使用更宽松的条件检查 data: URI\n", "                    if map_url_part.startswith('data:application/json;'):\n", "                        logger.info(\"    - Found inline Base64 source map. Decoding...\")\n", "                        base64_data = map_url_part.split(',', 1)[1]\n", "                        decoded_data = base64.b64decode(base64_data).decode('utf-8')\n", "                        map_data = json.loads(decoded_data)\n", "                    else:\n", "                        map_url = urljoin(link, map_url_part)\n", "                        logger.info(f\"    - Found source map URL: {map_url}\")\n", "                        map_response = await page.request.get(map_url)\n", "                        map_data = await map_response.json()\n", "\n", "                    if map_data and 'sources' in map_data and isinstance(map_data['sources'], list):\n", "                        logger.info(f\"    - Success! Found {len(map_data['sources'])} sources in map.\")\n", "                        for source in map_data['sources']:\n", "                            cleaned = clean_path(source)\n", "                            if cleaned:\n", "                                all_sources.add(cleaned)\n", "                    else:\n", "                        logger.warning(f\"    - Source map from {link} is missing a 'sources' array.\")\n", "\n", "                except Exception as e:\n", "                    logger.error(f\"  - Failed to process stylesheet {link}: {e}\")\n", "            \n", "            logger.info(\"Phase 2 complete.\")\n", "\n", "        except Exception as e:\n", "            logger.error(f\"An error occurred during webpack source discovery: {e}\")\n", "        finally:\n", "            logger.info(\"Closing browser context.\")\n", "            await context.close()\n", "            await browser.close()\n", "\n", "    return sorted(list(all_sources))\n", "\n", "async def main():\n", "    target_url = \"http://localhost:9999/\"  # <--- 在这里替换您的 URL\n", "    discovered_files = await get_webpack_sources(target_url)\n", "\n", "    if discovered_files:\n", "        print(\"\\n--- Discovered Webpack Source Files ---\")\n", "        for file_path in discovered_files:\n", "            print(file_path)\n", "        print(f\"\\nTotal: {len(discovered_files)} files found.\")\n", "    else:\n", "        print(\"\\n--- No Webpack Source Files Discovered ---\")\n", "\n", "# 在Notebook中，您可以直接在另一个单元格中运行: await main()\n", "# 为了让这个单元格可以独立运行，我们这样启动它：\n", "try:\n", "    loop = asyncio.get_running_loop()\n", "    if loop.is_running():\n", "        print(\"Awaiting main function in existing event loop...\")\n", "        await main()\n", "except RuntimeError:\n", "    print(\"Starting new event loop to run main function...\")\n", "    asyncio.run(main())\n"]}, {"cell_type": "code", "execution_count": null, "id": "28b1307b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ui", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}