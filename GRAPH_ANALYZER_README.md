# Web Exploration Graph Analyzer

Complete analysis and visualization tool for web exploration runs from the Go-Browse system.

## Quick Start

```bash
# Analyze default run directory
conda activate ui
python graph_analyzer.py

# Analyze specific run directory  
python graph_analyzer.py /path/to/your/run/directory
```

## Generated Outputs

All results are saved to `./visualization/` directory:

### 📊 URL & Visit Analysis
1. **`visit_analysis.png`** - 4-panel analysis:
   - Top 15 most visited URLs (bar chart)
   - URL type distribution (pie chart)
   - Visit count distribution (histogram)
   - Visited vs unvisited comparison (fixed to use visited status)

2. **`detailed_statistics.json`** - Complete statistics in JSON format
3. **`analysis_report.txt`** - Human-readable analysis report

### 🕸️ Network Analysis
4. **`main_network_graph.png`** - Main network graph:
   - Node size = visit frequency
   - Node color = visit intensity (gray→blue→orange→red→dark red)
   - Edge color = relationship type
   - Labels for highly visited nodes

5. **`edge_relationship_analysis.png`** - Edge analysis:
   - Pie chart of edge relationship types
   - Bar chart with counts

6. **`visit_pattern_network.png`** - Specialized network:
   - Visited nodes (colored by visit count) vs unvisited nodes (gray)
   - Edge colors show relationship types:
     - Green: Visited → Visited
     - Orange: Visited → Unvisited
     - Blue: Unvisited → Visited
     - Light gray: Unvisited → Unvisited

### 🎯 Trajectory Analysis
7. **`longest_trajectory_analysis.png`** - Trajectory analysis:
   - Action types distribution
   - Thought length progression over steps
   - Success/failure and reward information
   - Action timeline (first 10 steps)

8. **`longest_trajectory_interactive.html`** - **Interactive HTML visualization**:
   - Complete step-by-step breakdown
   - Agent thoughts and actions for each step
   - **Visual observations (UI screenshots)** embedded as base64 images (in collapsible section)
   - Task goal and outcome details
   - Agent information (model, temperature, etc.)
   - **Evaluation agent analysis**: How success/failure was determined
   - **Evaluation process details**: Evaluation prompts and methodology
   - **Final evaluation decision**: Eval agent's final reasoning at the end
   - Collapsible observations and details
   - Beautiful responsive design with highlight animation

9. **`longest_trajectory_analysis.json`** - Complete trajectory data
10. **`longest_trajectory_report.txt`** - Detailed trajectory report

### 🌐 Localhost Website Deep Analysis
11. **`website_analysis.png`** - **12-panel localhost insights** (direct metrics only):
    - **URL Depth Distribution**: How deep users navigate (with visit rates)
    - **Zero Incoming Links Pages**: Potential orphans or entry points
    - **Most Visited Pages**: Direct visit count ranking
    - **Potential Entry Points**: High visits with ≤3 incoming links
    - **Outgoing Link Distribution**: URLs grouped by outgoing link count
    - **Navigation Hub Pages**: Pages with most outgoing links
    - **Incoming Link Analysis**: URLs grouped by incoming link count
    - **Unvisited Well-Connected Pages**: Missed opportunities (by link count)
    - **URL Types Distribution**: What types of content exist
    - **Content Type Performance**: Engagement level by content type
    - **Exploration Depth vs Success**: Success rate and avg visits by URL depth
    - **URL Length vs Success**: How URL character count affects visit success

12. **`website_analysis.json`** - Comprehensive localhost analysis data:
    - Functional area breakdowns
    - User journey patterns
    - Page importance scoring
    - Content accessibility metrics

## Key Insights from Analysis

### Edge Relationships
- **Visited → Unvisited (72.3%)**: Most links lead from explored to unexplored pages
- **Visited → Visited (26.6%)**: Links between explored pages
- **Unvisited → Unvisited (0.8%)**: Links between unexplored pages
- **Unvisited → Visited (0.4%)**: Rare reverse discovery

### URL Categories
- **Login pages (53.3%)**: Many authentication-related URLs
- **External URLs (16.5%)**: Links to external sites
- **Forum pages (8.2%)**: Discussion threads and forums
- **Main pages (3.8%)**: Core navigation pages

### Visit Patterns
- **Low exploration efficiency (6.9%)**: Only 20 of 291 discovered URLs were actually visited
- **High activity on visited pages**: 5092 total visits concentrated on few pages
- **Heavy focus**: Top page (forums) received 691 visits

### Trajectory Analysis
- **Longest trajectory**: 10 steps attempting to "Access the Wiki"
- **Failed task**: Agent struggled with clicking Wiki link (negative trajectory)
- **Action patterns**: Mostly click actions with send_msg_to_user for communication
- **Agent behavior**: Persistent retry attempts when actions fail
- **Interactive HTML**: Complete step-by-step visualization with collapsible details

### Localhost Website Deep Insights (Direct Metrics)
- **URL depth patterns**: Most content at depth 1 (189 URLs), visit rate decreases with depth
- **Most visited pages**: Direct ranking by visit count (no artificial scoring)
- **Entry point candidates**: Pages with high visits but ≤3 incoming links
- **Content accessibility**: Main navigation (100%), Wiki (20.8%), Authentication (9.7%)
- **Navigation hubs**: Pages with most outgoing links (homepage: 112 links)
- **Incoming link distribution**: How well-connected pages are
- **Missed opportunities**: Unvisited pages with multiple incoming links
- **Content type performance**: Engagement levels vary by content type
- **Depth vs success**: Success rate and average visits by URL depth
- **URL length impact**: Shorter URLs tend to have higher visit success rates
- **Visual observations**: Screenshots embedded in trajectory analysis (collapsible)
- **Evaluation transparency**: How eval agent determined success/failure

## Understanding the Visualizations

### Node Styling
- **Size**: Larger nodes = more visits
- **Color coding**:
  - Gray: Never visited
  - Light blue: 1-5 visits
  - Orange: 6-20 visits
  - Red: 21-100 visits
  - Dark red: 100+ visits

### Edge Styling  
- **Width**: Thicker edges = stronger relationships
- **Color**: Shows visit relationship between connected pages
- **Direction**: Arrows show navigation direction

## Use Cases

- **Agent behavior analysis**: Understand exploration patterns
- **Strategy optimization**: Identify inefficient exploration
- **Coverage assessment**: See which areas are under-explored
- **Performance debugging**: Find bottlenecks and focus areas

## Requirements

```bash
conda install matplotlib networkx numpy -y
```

The tool automatically creates the `./visualization` directory and saves all outputs there.
