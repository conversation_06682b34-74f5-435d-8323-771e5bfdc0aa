# Go-Browse Design Judge - Usage Guide

The Design Judge is now implemented and ready to use! This tool analyzes UI design issues in negative trajectories from Go-Browse exploration runs.

## Quick Start

### 1. Run Design Judge Analysis

```bash
# Basic usage
python webexp/explore/design_judge.py \
    --model_name "Qwen/Qwen2.5-VL-72B-Instruct" \
    --run_dir "./runs/your_run_directory" \
    --base_url "http://localhost:8000"

# With API key (if needed)
python webexp/explore/design_judge.py \
    --model_name "Qwen/Qwen2.5-VL-72B-Instruct" \
    --run_dir "./runs/your_run_directory" \
    --base_url "http://localhost:8000" \
    --api_key "your_api_key"
```

### 2. Analyze Results with Summary Report

```bash
# Run analysis and generate summary report
python example_design_judge_usage.py \
    --run_dir "./runs/your_run_directory" \
    --model_name "Qwen/Qwen2.5-VL-72B-Instruct"

# Only analyze existing feedback (skip running design judge)
python example_design_judge_usage.py \
    --run_dir "./runs/your_run_directory" \
    --analyze_only
```

## What the Design Judge Does

1. **Finds Negative Trajectories**: Automatically discovers all failed trajectories in your run
2. **Step-by-Step Analysis**: Analyzes each step in negative trajectories individually
3. **UI Issue Detection**: Uses Qwen VL to determine if UI design caused the failure
4. **Contextual Analysis**: Considers:
   - Current agent thought and action
   - UI screenshot
   - Next agent thought and action
   - Overall task goal
   - Evaluation agent's failure analysis
5. **Generates Feedback**: Creates `design_feedback.json` files with specific improvement suggestions

## Output Structure

After running the Design Judge, your run directory will have additional feedback files:

```
runs/your_run/
└── graph/
    └── node_0/
        └── tasks/
            └── task_0/
                └── negative_trajs/
                    └── 0/
                        ├── step_0/
                        │   ├── step_info.json
                        │   ├── screenshot.png
                        │   └── design_feedback.json  # ← NEW
                        └── step_1/
                            ├── step_info.json
                            ├── screenshot.png
                            └── design_feedback.json  # ← NEW
```

## Example Workflow

### Step 1: Run Go-Browse Exploration
```bash
# Your normal Go-Browse exploration
python webexp/explore/algorithms/web_explore.py -c configs/your_config.yaml
```

### Step 2: Analyze UI Design Issues
```bash
# Run design judge on the results
python webexp/explore/design_judge.py \
    --model_name "Qwen/Qwen2.5-VL-72B-Instruct" \
    --run_dir "./runs/your_run_directory"
```

### Step 3: Review Summary Report
```bash
# Generate and view summary report
python example_design_judge_usage.py \
    --run_dir "./runs/your_run_directory" \
    --analyze_only
```

## Sample Output

### Design Feedback JSON
```json
{
  "is_ui_design_issue": true,
  "analysis": "The submit button lacks clear visual indication when clicked, causing the agent to repeatedly attempt the same action without understanding if it was successful.",
  "suggestions": "Add loading state to submit button, Provide clear success/error feedback, Use more descriptive button text",
  "current_step": {
    "thought": "I need to click the submit button",
    "action": "click(123)"
  },
  "next_step": {
    "thought": "The page didn't respond as expected, trying again",
    "action": "click(123)"
  },
  "task_goal": "Submit the form successfully",
  "eval_context": "Agent got confused because the submit button provided no feedback..."
}
```

### Summary Report
```
============================================================
DESIGN JUDGE ANALYSIS SUMMARY
============================================================
Run Directory: ./runs/reddit_qwen_01
Total Steps Analyzed: 45
UI Issues Found: 12
UI Issue Rate: 26.67%

COMMON ISSUE KEYWORDS:
------------------------------
  button: 8 occurrences
  feedback: 6 occurrences
  loading: 4 occurrences
  unclear: 3 occurrences

SAMPLE SUGGESTIONS:
------------------------------
1. Add loading state to submit button, Provide clear success/error feedback
2. Use more descriptive button text instead of generic "Submit"
3. Improve visual hierarchy to make important elements more prominent
============================================================
```

## Key Features

- ✅ **Non-invasive**: Doesn't modify existing data structure
- ✅ **Step-level granularity**: Analyzes each step individually
- ✅ **Smart filtering**: Only generates feedback for actual UI issues
- ✅ **Contextual analysis**: Uses full context including agent thoughts and evaluation
- ✅ **Automatic cleanup**: Clears previous feedback before generating new ones
- ✅ **Comprehensive reporting**: Provides both detailed and summary views
- ✅ **Cost tracking**: Includes model usage statistics

## Testing

Run the test suite to verify everything works:

```bash
python test_design_judge.py
```

## Requirements

- Python 3.8+
- PIL (Pillow)
- numpy  
- requests
- Access to a Qwen VL model endpoint (e.g., local vLLM server)

## Next Steps

1. **Run on your data**: Use the Design Judge on your Go-Browse runs
2. **Analyze patterns**: Look for common UI issues across different sites
3. **Improve designs**: Use the feedback to enhance UI designs
4. **Validate improvements**: Re-run exploration to see if issues are resolved
5. **Integrate with RL**: Consider using feedback as dense rewards for training

The Design Judge is ready to help you identify and fix UI design issues that confuse navigation agents!
