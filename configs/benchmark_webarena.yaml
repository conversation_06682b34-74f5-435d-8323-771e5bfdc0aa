agent_factory_args:
  name: SolverAgent
  model_id: <PLACEHOLDER - Huggingface model ID or path to local model>
  base_url: <PLACEHOLDER - Optional URL for model inference. Can leave null if setting OpenAI API enviromnent variables>
  base_url_2: <PLACEHOLDER - Optional backup URL for model inference. For example, when using Qwen models, this URL can expose a VLLM endpoint configured for a longer context length>
  api_key: <PLACEHOLDER - Optional API key for model inference. Can leave null if setting OpenAI API enviromnent variables>
  temperature: 0.
  char_limit: 80000 # Character limit for truncating prompt.

exp_dir: <PLACEHOLDER - Path to user-specified experiment directory>
n_jobs: 12
resume_dir: null # If resuming from a previous, incomplete run, you can specify the specific run directory path here (exp_dir/run_dir).