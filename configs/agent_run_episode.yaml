agent_factory_args:
  name: SolverAgent
  model_id: <PLACEHOLDER - Huggingface model ID or path to local model>
  base_url: <PLACEHOLDER - Optional URL for model inference. Can leave null if setting OpenAI API enviromnent variables>
  base_url_2: <PLACEHOLDER - Optional backup URL for model inference. For example, when using Qwen models, this URL can expose a VLLM endpoint configured for a longer context length>
  api_key: <PLACEHOLDER - Optional API key for model inference. Can leave null if setting OpenAI API enviromnent variables>
  temperature: 0.
  char_limit: 80000 # Character limit for truncating prompt.


env_args:
  # task name can be any task registered with browsergym.
  # you can also run on any live website by setting task_name to "openended"
  # and providing another "start_url" in env_args.task_kwargs with the URL you want to run the agent on.
  task_name: webarena.608
  #task_name: openended
  #task_kwargs:
  #  start_url: https://www.google.com/maps
  max_steps: 30
  # If headless is set to true, browser GUI will not be shown. Useful for running on a remote machine.
  headless: false
  viewport:
    width: 1280
    height: 1440

exp_dir: "./results"