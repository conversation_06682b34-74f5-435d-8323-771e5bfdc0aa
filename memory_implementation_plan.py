"""
Agent Memory Implementation Plan
具体的代码实现方案，展示如何集成到现有系统
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Set, Optional, Tuple
import json
import os
from pathlib import Path

@dataclass
class VisitRecord:
    """URL访问记录"""
    url: str
    visit_count: int = 0
    last_visit_time: Optional[datetime] = None
    successful_tasks: List[str] = field(default_factory=list)
    failed_tasks: List[str] = field(default_factory=list)
    discovered_elements: Set[str] = field(default_factory=set)
    page_type: Optional[str] = None
    success_rate: float = 0.0
    
    def update_visit(self, task_success: bool, task_name: str = None):
        self.visit_count += 1
        self.last_visit_time = datetime.now()
        if task_name:
            if task_success:
                self.successful_tasks.append(task_name)
            else:
                self.failed_tasks.append(task_name)
        self._update_success_rate()
    
    def _update_success_rate(self):
        total_tasks = len(self.successful_tasks) + len(self.failed_tasks)
        if total_tasks > 0:
            self.success_rate = len(self.successful_tasks) / total_tasks

@dataclass
class ActionPattern:
    """动作模式记录"""
    pattern_id: str
    actions: List[str]
    success_count: int = 0
    failure_count: int = 0
    contexts: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0

class AgentMemory:
    """Agent Memory核心类"""
    
    def __init__(self, memory_dir: str):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        # 核心memory组件
        self.url_visits: Dict[str, VisitRecord] = {}
        self.action_patterns: Dict[str, ActionPattern] = {}
        self.navigation_paths: Dict[Tuple[str, str], List[List[str]]] = {}
        self.failed_actions: Dict[str, Set[str]] = {}
        
        # 配置参数
        self.max_visit_threshold = 10  # 最大访问次数阈值
        self.success_rate_threshold = 0.3  # 成功率阈值
        self.memory_retention_days = 30
        
        self.load_memory()
    
    def is_overvisited(self, url: str) -> bool:
        """检查URL是否被过度访问"""
        if url not in self.url_visits:
            return False
        
        record = self.url_visits[url]
        # 如果访问次数超过阈值且成功率低，认为过度访问
        return (record.visit_count > self.max_visit_threshold and 
                record.success_rate < self.success_rate_threshold)
    
    def record_visit(self, url: str, task_success: bool = None, task_name: str = None):
        """记录URL访问"""
        if url not in self.url_visits:
            self.url_visits[url] = VisitRecord(url=url)
        
        self.url_visits[url].update_visit(task_success, task_name)
    
    def get_successful_patterns(self, url: str) -> List[ActionPattern]:
        """获取在该URL上成功的动作模式"""
        patterns = []
        for pattern in self.action_patterns.values():
            if url in pattern.contexts and pattern.success_rate > 0.5:
                patterns.append(pattern)
        return sorted(patterns, key=lambda p: p.success_rate, reverse=True)
    
    def get_failed_actions(self, url: str) -> Set[str]:
        """获取在该URL上失败的动作"""
        return self.failed_actions.get(url, set())
    
    def record_action_result(self, url: str, action: str, success: bool):
        """记录动作执行结果"""
        if not success:
            if url not in self.failed_actions:
                self.failed_actions[url] = set()
            self.failed_actions[url].add(action)
    
    def calculate_exploration_value(self, url: str) -> float:
        """计算URL的探索价值"""
        if url not in self.url_visits:
            return 1.0  # 未访问过的URL价值最高
        
        record = self.url_visits[url]
        
        # 新颖性分数 (访问次数越少分数越高)
        novelty_score = max(0, 1.0 - record.visit_count / 20.0)
        
        # 成功率分数
        success_score = record.success_rate
        
        # 时间衰减分数 (最近访问的分数较低)
        time_score = 1.0
        if record.last_visit_time:
            days_since_visit = (datetime.now() - record.last_visit_time).days
            time_score = min(1.0, days_since_visit / 7.0)  # 7天后完全恢复
        
        # 综合分数
        return novelty_score * 0.5 + success_score * 0.3 + time_score * 0.2
    
    def prioritize_urls(self, urls: List[str]) -> List[str]:
        """基于memory对URL进行优先级排序"""
        url_scores = [(url, self.calculate_exploration_value(url)) for url in urls]
        sorted_urls = sorted(url_scores, key=lambda x: x[1], reverse=True)
        return [url for url, score in sorted_urls]
    
    def should_skip_url(self, url: str) -> bool:
        """判断是否应该跳过某个URL"""
        return self.is_overvisited(url)
    
    def get_recommended_action(self, url: str, available_actions: List[str]) -> Optional[str]:
        """基于memory推荐动作"""
        # 获取成功模式
        successful_patterns = self.get_successful_patterns(url)
        if successful_patterns:
            for pattern in successful_patterns:
                for action in pattern.actions:
                    if action in available_actions:
                        return action
        
        # 避免失败的动作
        failed_actions = self.get_failed_actions(url)
        available_actions = [a for a in available_actions if a not in failed_actions]
        
        return available_actions[0] if available_actions else None
    
    def save_memory(self):
        """保存memory到磁盘"""
        memory_data = {
            'url_visits': {
                url: {
                    'url': record.url,
                    'visit_count': record.visit_count,
                    'last_visit_time': record.last_visit_time.isoformat() if record.last_visit_time else None,
                    'successful_tasks': record.successful_tasks,
                    'failed_tasks': record.failed_tasks,
                    'discovered_elements': list(record.discovered_elements),
                    'page_type': record.page_type,
                    'success_rate': record.success_rate
                }
                for url, record in self.url_visits.items()
            },
            'action_patterns': {
                pattern_id: {
                    'pattern_id': pattern.pattern_id,
                    'actions': pattern.actions,
                    'success_count': pattern.success_count,
                    'failure_count': pattern.failure_count,
                    'contexts': pattern.contexts
                }
                for pattern_id, pattern in self.action_patterns.items()
            },
            'failed_actions': {
                url: list(actions) for url, actions in self.failed_actions.items()
            }
        }
        
        with open(self.memory_dir / 'agent_memory.json', 'w') as f:
            json.dump(memory_data, f, indent=2)
    
    def load_memory(self):
        """从磁盘加载memory"""
        memory_file = self.memory_dir / 'agent_memory.json'
        if not memory_file.exists():
            return
        
        try:
            with open(memory_file, 'r') as f:
                memory_data = json.load(f)
            
            # 加载URL访问记录
            for url, data in memory_data.get('url_visits', {}).items():
                record = VisitRecord(
                    url=data['url'],
                    visit_count=data['visit_count'],
                    last_visit_time=datetime.fromisoformat(data['last_visit_time']) if data['last_visit_time'] else None,
                    successful_tasks=data['successful_tasks'],
                    failed_tasks=data['failed_tasks'],
                    discovered_elements=set(data['discovered_elements']),
                    page_type=data['page_type'],
                    success_rate=data['success_rate']
                )
                self.url_visits[url] = record
            
            # 加载动作模式
            for pattern_id, data in memory_data.get('action_patterns', {}).items():
                pattern = ActionPattern(
                    pattern_id=data['pattern_id'],
                    actions=data['actions'],
                    success_count=data['success_count'],
                    failure_count=data['failure_count'],
                    contexts=data['contexts']
                )
                self.action_patterns[pattern_id] = pattern
            
            # 加载失败动作
            for url, actions in memory_data.get('failed_actions', {}).items():
                self.failed_actions[url] = set(actions)
                
        except Exception as e:
            print(f"Failed to load memory: {e}")

# 集成到现有系统的关键修改点

class MemoryAwareGraph:
    """集成memory的Graph类扩展"""
    
    def __init__(self, *args, memory: AgentMemory, **kwargs):
        super().__init__(*args, **kwargs)
        self.memory = memory
    
    def get_next_node(self) -> Optional['Node']:
        """基于memory优化的节点选择"""
        if len(self.unexplored_nodes) == 0:
            return None
        
        # 获取所有未探索的URL
        unexplored_urls = [node.url for node in self.unexplored_nodes]
        
        # 过滤掉过度访问的URL
        filtered_urls = [url for url in unexplored_urls if not self.memory.should_skip_url(url)]
        
        if not filtered_urls:
            # 如果所有URL都被过度访问，选择访问次数最少的
            filtered_urls = unexplored_urls
        
        # 基于memory进行优先级排序
        prioritized_urls = self.memory.prioritize_urls(filtered_urls)
        
        # 返回优先级最高的节点
        for node in self.unexplored_nodes:
            if node.url == prioritized_urls[0]:
                return node
        
        return self.unexplored_nodes[0]  # fallback

class MemoryAwareAgent:
    """集成memory的Agent扩展"""
    
    def __init__(self, *args, memory: AgentMemory, **kwargs):
        super().__init__(*args, **kwargs)
        self.memory = memory
    
    def get_action(self, obs: dict, **kwargs) -> Tuple[str, dict]:
        """基于memory的动作选择"""
        current_url = obs.get('url', '')
        
        # 记录访问
        self.memory.record_visit(current_url)
        
        # 检查是否应该跳过当前页面
        if self.memory.should_skip_url(current_url):
            return "go_back()", {"reason": "URL overvisited, backing out"}
        
        # 获取推荐动作
        available_actions = self._extract_available_actions(obs)
        recommended_action = self.memory.get_recommended_action(current_url, available_actions)
        
        if recommended_action:
            return recommended_action, {"source": "memory_recommendation"}
        
        # 回退到原始动作选择逻辑
        return super().get_action(obs, **kwargs)
